import logging


from app.config.db.init_db import init_db
from app.config.db.init_stripe import init_stripe
from app.config.db.database import SessionLocal

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def init() -> None:
    db = None
    try:
        db = SessionLocal()
        init_db(db)
        init_stripe(db)
    except Exception as e:
        raise e
    finally:
        if db:
            db.close()
    
    


def main() -> None:
    logger.info("Creating initial data")
    init()
    logger.info("Initial data created")


if __name__ == "__main__":
    main()
