from datetime import datetime, timezone
import logging
from typing import Any
from fastapi.responses import JSONResponse

logger = logging.getLogger(__name__)

class CustomJsonResponse(JSONResponse):
     def __init__(self, content: Any, status_code: int = 200, *args, **kwargs) -> None:
        # Customize content and pass my new content...
        def convert_datetime(obj):
            """ Recursively convert datetime objects to ISO 8601 UTC format with 'Z'. """
            if isinstance(obj, datetime):
                return obj.astimezone(timezone.utc).isoformat().replace("+00:00", "Z")  # Ensures UTC with 'Z'
            elif isinstance(obj, dict):
                return {k: convert_datetime(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_datetime(v) for v in obj]
            return obj  # Keep everything else unchanged
         
        super().__init__(convert_datetime(content), status_code, *args, **kwargs)
         