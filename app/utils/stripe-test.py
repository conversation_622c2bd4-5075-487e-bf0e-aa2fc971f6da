 
 
import stripe
stripe.api_key = "sk_test_51Psh8RCNxPSZy3wjvHlv1JZWQlTa1XiTPpwXtny3jzKq2Re2L3GPraje1JQKK68B9rhF038bJDtf4IlgGurA2oAQ00Uqs0Muk2"
products = stripe.Product.list(limit=100, active=True) 


def get_charges_to_refund(customer_stripe_id, amount_to_refund_in_cur, starting_after_charge_stripe_id=None):
    #convert in cents
    amount_to_refund = amount_to_refund_in_cur * 100
    
    
    charges_to_refund = []
    charges_res = stripe.Charge.list(
        customer=customer_stripe_id,
        limit=100, 
        starting_after=starting_after_charge_stripe_id,
        expand=["data.payment_intent.invoice"],
    )
    for charge in charges_res['data']: 
        payment_intent = charge['payment_intent']
        payment_intent_metadata = payment_intent['metadata']
        invoice = payment_intent['invoice']
        
        # Filtering if the charge is relevant:
        is_relevant_charge = (
            # Is a subscription creation or renewal charge
            (
                invoice
                and (
                    invoice['billing_reason'] == 'subscription_cycle'
                    or invoice['billing_reason'] == 'subscription_create'
                )
            )
            # Is an upgrade charge
            or (
                'isUpgrade' in payment_intent_metadata
                or (
                    'subscriptionActionType' in payment_intent_metadata  
                    and payment_intent_metadata['subscriptionActionType'] =='upgrade'
                )
            )
        )
        
        if not is_relevant_charge:
            continue
        
        cur_refundable_amount = charge['amount_captured'] - charge['amount_refunded']
        if cur_refundable_amount > 0:
            cur_amount_to_refund = min(cur_refundable_amount, amount_to_refund)
            amount_to_refund -= cur_amount_to_refund
            charges_to_refund.append({'charge': charge, 'amount_to_refund': cur_amount_to_refund})
        if amount_to_refund <= 0:
            break
    if charges_res['has_more'] and amount_to_refund > 0:
        charges_to_refund.extend(get_charges_to_refund(customer_stripe_id, amount_to_refund, charges_res['data'][-1]['id']))

    if amount_to_refund > 0:
        raise Exception('Not enough charges to refund')
    
    return charges_to_refund

# charges = get_charges_to_refund('cus_S3RMbszvTc6dz0', 1000)
# print([(c['charge']['id'], c['amount_to_refund']) for c in charges])
def product_exists(product_name, db_price, billing_period_interval_str):
    try:
        # Search for the product using Stripe's API
        
        for product in products['data']: 
            if product['name'].lower() == product_name.lower():
                prices = stripe.Price.list(product=product['id'] ,limit=100)
                for price in prices:  # Loop through all associated prices
                    if (
                        price['unit_amount'] == (db_price*100)
                        and price['recurring']
                        and price['recurring']['interval'] == billing_period_interval_str
                        and price['active']   
                    ):
                        return (product,price) 
                return (product, None)
        return (None, None)
    except Exception as e:
        traceback.print_exc()  # Print the traceback to the console
        print(f"Error while searching for product: {str(e)}")
        return (None, None)

additional_product_exists = product_exists('PVGIS24 ADDITIONAL CREDIT', 10, 'month')
print(additional_product_exists)