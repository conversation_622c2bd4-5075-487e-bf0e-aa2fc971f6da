from collections.abc import Iterable
import requests
from app.config.settings import get_settings
from fastapi.encoders import jsonable_encoder
from datetime import datetime
from enum import Enum
from typing import Dict, Any
from decimal import Decimal
from datetime import datetime, timedelta, timezone
import zoneinfo
import pytz

settings = get_settings()

def to_dict(obj):
    return {c.key: getattr(obj, c.key) for c in obj.__table__.columns}

def send_email(email_param, lang, template):
    email_param["templateVars"]["current_year"] = datetime.now().year
    url = f"{settings.MAIL_API}/send/{lang}/{template}"

    response = requests.post(url, json=email_param)
    if response.status_code == 200:
        return response.json()
    else:
        print(f"Error: {response.status_code}")
        return None
    
def send_email_cms(email_param, lang, cmsKey):
    email_param["templateVars"]["current_year"] = datetime.now().year
    url = f"{settings.MAIL_API}/send/cms/{lang}/{cmsKey}" 
    response = requests.post(url, json=email_param) 
    if response.status_code == 200:
        return response.json()
    else:
        print(f"Error: {response.status_code}")
        print(response.text)
        return None
     
def custom_jsonable_encoder(obj):
    try:
        data = jsonable_encoder(obj)
    except:
        data = to_dict(obj)
    return data

def get_from_emb_dict_or_throw(emb_dict_arr, *args): 
    (result, key_not_found, source_dict_arr) = get_from_emb_dict_core(emb_dict_arr, *args)
    if key_not_found:
        raise Exception(f'No key/index "{key_not_found}" in dict/indexable \n'+str(source_dict_arr))
    return result

def get_from_emb_dict_core(emb_dict_arr, *args): 
     
    cur_value = emb_dict_arr
    for key in args:
        if cur_value is None:
            return (None, key, None)
        if (
            (
                isinstance(key, int) and is_indexable(cur_value)
            ) 
            or key in cur_value
        ):
            cur_value = cur_value[key] 
        else:
            return (None, key, cur_value)
    return (cur_value, None, None)

def get_from_emb_dict (emb_dict, *args):
    (result, key_not_found, source_dict_arr) = get_from_emb_dict_core(emb_dict, *args)
    return result

def is_indexable(obj):
    try:
        obj[0]  # Check if indexing works
        return isinstance(obj, Iterable)  # Ensure it's an iterable
    except (TypeError, IndexError, KeyError):
        return False
    
def serialize_value(value):
    if isinstance(value, datetime):
        return value.isoformat()
    elif isinstance(value, Enum):
        return value.value
    elif isinstance(value, Decimal):
        return float(value)  # Convert Decimal to float for JSON serialization
    elif isinstance(value, (list, dict)):
        return value  # let json.dumps handle nested structures
    else:
        return value

def serialize_dict_to_json(data: Dict[str, Any]) -> Dict[str, Any]:
    def serialize_recursive(value):
        if isinstance(value, dict):
            return {k: serialize_recursive(v) for k, v in value.items()}
        elif isinstance(value, list):
            return [serialize_recursive(item) for item in value]
        else:
            return serialize_value(value)
    
    return serialize_recursive(data)


def get_timezone_names_from_offset(
    offset_str: str, find_all: bool = False
):
    """
    Finds IANA timezone name(s) that currently match a given UTC offset string.
    
    Args:
        offset_str: The UTC offset in string format, e.g., "+02:00", "-05:30", "Z".
        find_all: If True, returns all matching timezone names. If False (default),
                  returns the first match.
    
    Returns:
        - A single timezone name (str) if find_all is False and a match is found
        - A list of timezone names (list[str]) if find_all is True
        - None if no match is found
    
    Note:
        This function's result depends on current date/time due to Daylight Saving Time.
        Multiple timezones can share the same offset (e.g., both Mauritius and Azerbaijan
        use UTC+4, but have different timezone identifiers).
    """
    if not isinstance(offset_str, str) or not offset_str.strip():
        return None

    # Handle UTC "Z" (Zulu time)
    if offset_str.upper() == "Z":
        offset_str = "+00:00"

    try:
        # Parse the offset string into hours and minutes
        is_negative = offset_str.startswith("-")
        time_str = offset_str[1:] if is_negative else offset_str.lstrip("+")
        
        parts = time_str.split(":")
        hours = int(parts[0])
        minutes = int(parts[1]) if len(parts) > 1 else 0
        
        # Apply sign to hours/minutes (critical fix)
        if is_negative:
            hours = -hours
            minutes = -minutes
            
        target_offset = timedelta(hours=hours, minutes=minutes)
    except (IndexError, ValueError):
        return None

    # Get current UTC time for comparison
    now_utc = datetime.now(timezone.utc)
    matching_timezones = []

    # Check all available timezones
    for tz_name in zoneinfo.available_timezones():
        try:
            tz = zoneinfo.ZoneInfo(tz_name)
            current_offset = now_utc.astimezone(tz).utcoffset()
            
            if current_offset == target_offset:
                if not find_all:
                    return tz_name
                matching_timezones.append(tz_name)
        except Exception:
            continue

    return matching_timezones if find_all and matching_timezones else None

def get_timezone_names_from_offset_pytz(
    offset_str: str, find_all: bool = False
):
    """
    Finds IANA timezone name(s) that currently match a given UTC offset string using pytz.
    
    Args:
        offset_str: The UTC offset in string format, e.g., "+02:00", "-05:30", "Z".
        find_all: If True, returns all matching timezone names. If False (default),
                  returns the first match.
    
    Returns:
        - A single timezone name (str) if find_all is False
        - A list of timezone names (list[str]) if find_all is True
    
    Note:
        Requires 'pytz' package to be installed in your Docker container.
        This function's result depends on current date/time due to Daylight Saving Time.
    """
    # Validate input
    if not isinstance(offset_str, str) or not offset_str.strip():
        return "UTC" if not find_all else []
    
    # Handle UTC "Z" (Zulu time)
    if offset_str.upper() == "Z":
        offset_str = "+00:00"

    try:
        # Parse the offset string into hours and minutes
        is_negative = offset_str.startswith("-")
        time_str = offset_str[1:] if is_negative else offset_str.lstrip("+")
        
        parts = time_str.split(":")
        hours = int(parts[0])
        minutes = int(parts[1]) if len(parts) > 1 else 0
        
        # Apply sign to hours/minutes (critical fix)
        if is_negative:
            hours = -hours
            minutes = -minutes
            
        target_offset = timedelta(hours=hours, minutes=minutes)
    except (IndexError, ValueError):
        return "UTC" if not find_all else []

    matching_timezones = []
    
    # Check all pytz timezones
    for tz_name in pytz.all_timezones:
        try:
            tz = pytz.timezone(tz_name)
            current_offset = datetime.now(tz).utcoffset()
            
            if current_offset == target_offset:
                if not find_all:
                    return tz_name
                matching_timezones.append(tz_name)
        except Exception:
            continue

    return matching_timezones if find_all and matching_timezones else "UTC"

def get_timezone(country: str, offset: str) -> str:
    all_matches = get_timezone_names_from_offset(offset, find_all=True) or []
    # Filter by country-specific timezones (implementation depends on your data)
    return next((tz for tz in all_matches if country in tz), "UTC")