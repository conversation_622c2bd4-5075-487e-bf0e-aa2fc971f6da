 



select  c.created_at
from customer c
join account_information ai on ai.customer_id  = c.id
JOIN country ON
	country.id = c.country_id
LEFT OUTER JOIN subscription ON
	subscription.customer_id = c.id
	AND subscription.subscription_status = 'ACTIVE'
	AND date_add(subscription.start_date, INTERVAL 1 MONTH) >=  NOW()
	AND subscription.start_date <=  NOW()
	AND subscription.product_id != ********
left join customer_email_item cei on cei.customer_id = c.id and cei.customer_email_task_id = 1
where subscription.id is null 
and c.deleted_at is null
and  ai.account_type_id = 2 
and cei.id is null


-- Testing the filters 
SELECT
    count(*)
	-- account_information.account_type_id,
	-- customer.created_at,
	-- customer.email,
    -- coalesce(customer.timezone_offset, country.timezone_offset)
FROM
	customer
LEFT OUTER JOIN (
	SELECT
		customer_2.country_id AS country_id,
		min(customer_2.id) AS customer_id
	FROM
		customer AS customer_2
	JOIN subscription ON
		subscription.customer_id = customer_2.id
		AND subscription.subscription_status = 'ACTIVE'
		AND date_add(subscription.start_date, INTERVAL 1 MONTH) >= NOW()
			AND subscription.start_date <=  NOW()
			AND subscription.product_id != ********
		WHERE
			customer_2.deleted_at IS NULL
			AND customer_2.first_name IS NOT NULL
			AND length(trim(customer_2.first_name)) > 0
		GROUP BY
			customer_2.country_id) AS anon_7 ON
	anon_7.country_id = customer.country_id
LEFT OUTER JOIN customer AS customer_1 ON
	customer_1.id = anon_7.customer_id
JOIN account_information ON
	customer.id = account_information.customer_id
JOIN country ON
	country.id = customer.country_id
JOIN (
	SELECT
		customer_3.id AS customer_id,
		count(customer_email_task_1.id) AS email_count
	FROM
		customer AS customer_3
	LEFT OUTER JOIN customer_email_item AS customer_email_item_1 ON
		customer_3.id = customer_email_item_1.customer_id
	LEFT OUTER JOIN customer_email_task AS customer_email_task_1 ON
		customer_email_task_1.id = customer_email_item_1.customer_email_task_id
		AND customer_email_task_1.email_number = 1
		AND customer_email_item_1.sent_at IS NOT NULL
	GROUP BY
		customer_3.id
	HAVING
		count(customer_email_task_1.id) = 0) AS anon_8 ON
	anon_8.customer_id = customer.id
LEFT OUTER JOIN subscription ON
	subscription.customer_id = customer.id
	AND subscription.subscription_status = 'ACTIVE'
	AND date_add(subscription.start_date, INTERVAL 1 MONTH) >=  NOW()
	AND subscription.start_date <=  NOW()
	AND subscription.product_id != ********
WHERE
	customer.deleted_at IS NULL
	AND subscription.id IS NULL
	AND account_information.account_type_id = 2
	AND CAST(customer.created_at AS DATE) <= CAST(date_sub(NOW(), interval 24 hour) AS DATE)
    and (EXTRACT(hour FROM convert_tz(
    	date_add(NOW(), INTERVAL 1 hour)
    , '+00:00', coalesce(customer.timezone_offset, country.timezone_offset))) = 8
    	or
EXTRACT(hour FROM convert_tz(
    	date_add(NOW(), INTERVAL 2 hour)
    , '+00:00', coalesce(customer.timezone_offset, country.timezone_offset))) = 8
    	or
EXTRACT(hour FROM convert_tz(
    	date_add(NOW(), INTERVAL 3 hour)
    , '+00:00', coalesce(customer.timezone_offset, country.timezone_offset))) = 8
    	or
EXTRACT(hour FROM convert_tz(
    	date_add(NOW(), INTERVAL 4 hour)
    , '+00:00', coalesce(customer.timezone_offset, country.timezone_offset))) = 8
    	or
EXTRACT(hour FROM convert_tz(
    	date_add(NOW(), INTERVAL 5 hour)
    , '+00:00', coalesce(customer.timezone_offset, country.timezone_offset))) = 8
    	or
EXTRACT(hour FROM convert_tz(
    	date_add(NOW(), INTERVAL 6 hour)
    , '+00:00', coalesce(customer.timezone_offset, country.timezone_offset))) = 8
    	or
EXTRACT(hour FROM convert_tz(
    	date_add(NOW(), INTERVAL 7 hour)
    , '+00:00', coalesce(customer.timezone_offset, country.timezone_offset))) = 8
    	or
EXTRACT(hour FROM convert_tz(
    	date_add(NOW(), INTERVAL 8 hour)
    , '+00:00', coalesce(customer.timezone_offset, country.timezone_offset))) = 8
    	or
EXTRACT(hour FROM convert_tz(
    	date_add(NOW(), INTERVAL 9 hour)
    , '+00:00', coalesce(customer.timezone_offset, country.timezone_offset))) = 8
    	or
EXTRACT(hour FROM convert_tz(
    	date_add(NOW(), INTERVAL 10 hour)
    , '+00:00', coalesce(customer.timezone_offset, country.timezone_offset))) = 8
    	or
EXTRACT(hour FROM convert_tz(
    	date_add(NOW(), INTERVAL 11 hour)
    , '+00:00', coalesce(customer.timezone_offset, country.timezone_offset))) = 8
    	or
EXTRACT(hour FROM convert_tz(
    	date_add(NOW(), INTERVAL 12 hour)
    , '+00:00', coalesce(customer.timezone_offset, country.timezone_offset))) = 8
    	or
EXTRACT(hour FROM convert_tz(
    	date_add(NOW(), INTERVAL 13 hour)
    , '+00:00', coalesce(customer.timezone_offset, country.timezone_offset))) = 8
    	or
EXTRACT(hour FROM convert_tz(
    	date_add(NOW(), INTERVAL 14 hour)
    , '+00:00', coalesce(customer.timezone_offset, country.timezone_offset))) = 8
    	or
EXTRACT(hour FROM convert_tz(
    	date_add(NOW(), INTERVAL 15 hour)
    , '+00:00', coalesce(customer.timezone_offset, country.timezone_offset))) = 8
    	or
EXTRACT(hour FROM convert_tz(
    	date_add(NOW(), INTERVAL 16 hour)
    , '+00:00', coalesce(customer.timezone_offset, country.timezone_offset))) = 8
    	or
EXTRACT(hour FROM convert_tz(
    	date_add(NOW(), INTERVAL 17 hour)
    , '+00:00', coalesce(customer.timezone_offset, country.timezone_offset))) = 8
    	or
EXTRACT(hour FROM convert_tz(
    	date_add(NOW(), INTERVAL 18 hour)
    , '+00:00', coalesce(customer.timezone_offset, country.timezone_offset))) = 8
    	or
EXTRACT(hour FROM convert_tz(
    	date_add(NOW(), INTERVAL 19 hour)
    , '+00:00', coalesce(customer.timezone_offset, country.timezone_offset))) = 8
    	or
EXTRACT(hour FROM convert_tz(
    	date_add(NOW(), INTERVAL 20 hour)
    , '+00:00', coalesce(customer.timezone_offset, country.timezone_offset))) = 8
    	or
EXTRACT(hour FROM convert_tz(
    	date_add(NOW(), INTERVAL 21 hour)
    , '+00:00', coalesce(customer.timezone_offset, country.timezone_offset))) = 8
    	or
EXTRACT(hour FROM convert_tz(
    	date_add(NOW(), INTERVAL 22 hour)
    , '+00:00', coalesce(customer.timezone_offset, country.timezone_offset))) = 8
    	or
EXTRACT(hour FROM convert_tz(
    	date_add(NOW(), INTERVAL 23 hour)
    , '+00:00', coalesce(customer.timezone_offset, country.timezone_offset))) = 8
    	or
EXTRACT(hour FROM convert_tz(
    	date_add(NOW(), INTERVAL 24 hour)
    , '+00:00', coalesce(customer.timezone_offset, country.timezone_offset))) = 8
    	);


