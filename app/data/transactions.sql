--TODO: cleaning data (before migration)

-- Select duplicate stripe ids
select stripe_id, count(stripe_id) stripe_id_count 
from (SELECT JSON_EXTRACT(payment_transaction_json, '$.id')  AS stripe_id FROM subscription_payment_transaction) a
group by stripe_id
having count(stripe_id) > 1;



-- 1-delete receipts of duplicate transactions
WITH ranked_duplicates AS (
    SELECT 
        *,
        ROW_NUMBER() OVER (
            PARTITION BY stripe_object_id 
            ORDER BY id
        ) AS rn
    FROM subscription_payment_transaction spt 
)
DELETE spr FROM subscription_payment_receipt spr 
join subscription_payment_transaction spt on spr.subscription_payment_transaction_id  = spt.id
WHERE spt.id IN (
    SELECT id FROM ranked_duplicates WHERE rn > 1
);

-- 2-delete duplicate receipts 
WITH ranked_duplicates AS (
    SELECT 
        *,
        ROW_NUMBER() OVER (
            PARTITION BY stripe_object_id 
            ORDER BY id
        ) AS rn
    FROM subscription_payment_receipt spr 
)
DELETE FROM subscription_payment_receipt WHERE id IN (
    SELECT id FROM ranked_duplicates WHERE rn > 1
);

-- 3-delete duplicate transactions
WITH ranked_duplicates AS (
    SELECT 
        *,
        ROW_NUMBER() OVER (
            PARTITION BY stripe_object_id 
            ORDER BY id
        ) AS rn
    FROM subscription_payment_transaction spt 
)
DELETE FROM subscription_payment_transaction WHERE id IN (
    SELECT id FROM ranked_duplicates WHERE rn > 1
);