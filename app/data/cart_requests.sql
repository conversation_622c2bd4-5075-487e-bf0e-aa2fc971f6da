-- carts by country => map
select co.id country_id, co.name country_name,  ca.id cart_id
from country co 
left join cart ca on ca.country_id = co.id and ca.product_id in (1,2,3,4) and ca.converted_at is  null
group by co.id,co.name 

-- carts subscription type by country => collapsble rows table
select 
    co.id country_id, 
    co.name country_name,
    p.id product_id, 
    p.name product_name, 
    ca.id cart_id,
    ca.created_at,
    ca.ip_address
from country co 
join product p on p.id in (1,2,3,4)
left join cart ca on ca.country_id = co.id   and ca.converted_at is  null and p.id = ca.id 
group by co.id, co.name, p.id, p.name
 



