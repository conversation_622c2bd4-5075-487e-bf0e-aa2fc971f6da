-- Clean rows that has been fixed
delete from `customer_email_item` where id in (
    SELECT ci.id  FROM `customer_email_item` ci 
    join `customer_email_item` ci_success on ci.customer_id = ci_success.customer_id 
    and ci.customer_email_task_id = ci_success.customer_email_task_id and ci_success.sent_at is not null
    where ci.error is not null
)



-- Total Email sent
SELECT 
    ei.id
FROM customer_email_item
where sent_at is not null


-- Total Email sent by registered user type  
select  
 act.id as account_type_id,
 act.name as account_type_name, 
 ei.id as ei_id
FROM  account_type act 
LEFT JOIN account_information ai on ai.account_type_id = act.id 
LEFT JOIN customer cu ON cu.id = ai.customer_id 
LEFT JOIN customer_email_item ei on 
    ei.customer_id = cu.id 
    and ei.sent_at is not null
 

-- Total Email sent by country
select 
 co.name,
 co.code_alpha_2,
 ei.sent_at,
 ei.id as ei_id
FROM country co 
left join customer_email_item ei on  
    ei.country_id = co.id 
    and ei.sent_at is not null
 

-- Last 24 hours Email sent
SELECT 
    ei.id
FROM customer_email_item
where 
    sent_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)


-- Last 24 hours Email sent by registered user type  
select  
 act.id as account_type_id,
 act.name as account_type_name, 
 ei.id as ei_id
FROM  account_type act 
LEFT JOIN account_information ai on ai.account_type_id = act.id 
LEFT JOIN customer cu ON cu.id = ai.customer_id 
LEFT JOIN customer_email_item ei on 
    ei.customer_id = cu.id 
    and ei.sent_at is not null
    and ei.sent_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)
    and ei.sent_at <=NOW()

-- Last 24 hours Email sent by country
select 
 co.name,
 co.code_alpha_2,
 ei.sent_at,
 ei.id as ei_id,
 ei.created_at
FROM country co 
left join customer_email_item ei on  
    ei.country_id = co.id 
    and ei.sent_at is not null
    and ei.sent_at > DATE_SUB(NOW(), INTERVAL 1 DAY)
    and ei.sent_at <=NOW()



-- Email 2 without email 1
select count(cei_2.id) 
from customer_email_item cei_2 
left join customer_email_item cei_1 
    on cei_1.customer_id = cei_2.customer_id 
    and cei_1.customer_email_task_id = 1 
where cei_2.customer_email_task_id = 2 and cei_1.id is null

-- Email 3 without email 2
select count(cei_3.id) 
from customer_email_item cei_3 
left join customer_email_item cei_2 
    on cei_2.customer_id = cei_3.customer_id 
    and cei_2.customer_email_task_id = 2 
where cei_3.customer_email_task_id = 3 and cei_2.id is null

-- Email 4 without email 3
select count(cei_4.id) 
from customer_email_item cei_4 
left join customer_email_item cei_3 
    on cei_3.customer_id = cei_4.customer_id 
    and cei_3.customer_email_task_id = 3 
where cei_4.customer_email_task_id = 4 and cei_3.id is null

-- All email 4 have been sent without a previous email 3