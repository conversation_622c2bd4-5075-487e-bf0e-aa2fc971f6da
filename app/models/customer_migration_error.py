from sqlalchemy import Colum<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, func, <PERSON><PERSON><PERSON>, ForeignKey
from sqlalchemy.orm import relationship, column_property
from app.config.db.database import Base
from sqlalchemy.dialects import mysql
from sqlalchemy.ext.hybrid import hybrid_method

class CustomerMigrationError(Base):
    __tablename__ = "customer_migration_error"
    
    company = Column(String(255))
    email = Column(String(255))
    import_id= Column(String(500))
    source_row_id = Column(Integer)
    error = Column(mysql.LONGTEXT)
   
    