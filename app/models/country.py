from sqlalchemy import Column, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, func, Integer, ForeignKey
from app.config.db.database import Base
from sqlalchemy.orm import  column_property, relationship
from app.models.region import Region 
from app.models.monotonous_electricity_consumption import MonotonousElectricityConsumption 
from app.models.monotonous_electricity_consumption_daily import MonotonousElectricityConsumptionDaily

class Country(Base):
    __tablename__ = "country"
    
    name = Column(String(255), index=True)
    import_id = Column(String(255))
    code_alpha_2 = Column(String(10), unique=True)
    code_alpha_3 = Column(String(10), unique=True)
    currencies = Column(JSON)
    languages = Column(JSON)
    status = Column(Boolean, default=False)
    timezone_offset = Column(String(50), nullable=True)
    email_sender_json = Column(JSON) 
    default_customer_json = Column(JSON)
    normalized_name = Column(String(255), index=True)
    continent_id =  Column(Integer, ForeignKey("continent.id"))
    avg_residential_consumption_json = Column(JSON, nullable=True)
    use_avg_residential_consumption = Column(Boolean, nullable=True)
    timezone_name = Column(String(50), nullable=True)
    
    continent = relationship(
        "Continent",
        foreign_keys=[continent_id],
    )
    
    regions = relationship("Region", back_populates="country")
    
    monotonous_electricity_consumption = relationship("MonotonousElectricityConsumption", back_populates="country")
    monotonous_electricity_consumptions_daily = relationship("MonotonousElectricityConsumptionDaily", back_populates="country")
    
    

