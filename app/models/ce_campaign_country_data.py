from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, String, <PERSON>olean, func, Integer, Foreign<PERSON>ey
from sqlalchemy.orm import relationship, column_property
from app.config.db.database import Base
from sqlalchemy.dialects import mysql
from sqlalchemy.ext.hybrid import hybrid_method

class CeCampaignCountryData(Base):
    __tablename__ = "ce_campaign_country_data"
    
   
    country_id = Column(Integer, ForeignKey("country.id"))
    customer_email_campaign_id = Column(Integer, ForeignKey("customer_email_campaign.id"))
    default_customer_json = Column(JSON)
    default_language = Column(String(50))
    
    country = relationship(
        "Country",
        foreign_keys=[country_id],
    )
    
    customer_email_campaign = relationship(
        "CustomerEmailCampaign",
        foreign_keys=[customer_email_campaign_id],
    )
    
    