from sqlalchemy import Column, String, Integer, Enum
from app.config.db.database import Base
from app.enums.contact_type_enum import ContactTypeEnum

class ContactPlatform(Base):
    __tablename__ = "contact_platform"
    id = Column(Integer, primary_key=True, autoincrement=True)
    key = Column(String(255), unique=True, index=True)
    label = Column(String(255))
    type = Column(Enum(ContactTypeEnum))