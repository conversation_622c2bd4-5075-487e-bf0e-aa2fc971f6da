from sqlalchemy import Colum<PERSON>, <PERSON>, Integer, ForeignKey
from app.config.db.database import Base
from sqlalchemy.orm import relationship

class CustomerContactPlatformInformation(Base):
    __tablename__ = "customer_contact_platform_information"
    id = Column(Integer, primary_key=True, autoincrement=True)
    value = Column(String(255))

    contact_platform_id = Column(Integer, ForeignKey("contact_platform.id"))
    customer_id = Column(Integer, ForeignKey("customer.id"))

    customer = relationship(
        "Customer",
        foreign_keys=[customer_id],
    )
    contact_platform = relationship(
        "ContactPlatform",
        foreign_keys=[contact_platform_id],
    )