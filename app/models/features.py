from app.config.db.database import Base

from sqlalchemy import Column, Integer, String, Text, Boolean
from sqlalchemy.orm import relationship


class Features(Base):
    __tablename__ = "features"

    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(255))
    description = Column(Text())
    key = Column(String(255))
    value = Column(String(255))
    ui_order = Column(Integer)
    display_for_free = Column(Boolean)
    allow_for_free = Column(Boolean, nullable=False, default=True) 
    hide = Column(Boolean, nullable=True, default=False) 

    product_features = relationship("ProductFeatures", back_populates="features")
