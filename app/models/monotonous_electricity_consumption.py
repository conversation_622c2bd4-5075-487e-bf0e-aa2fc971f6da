from sqlalchemy import <PERSON>umn, JSON, Integer, ForeignKey,Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.dialects import mysql
from app.config.db.database import Base

class MonotonousElectricityConsumption(Base):
    __tablename__ = "monotonous_electricity_consumption"
    
    country_id = Column(Integer, ForeignKey("country.id"))
    region_id = Column(Integer, ForeignKey("region.id"), nullable=False)
    residential_consumption_json = Column(JSON)
    content = Column(mysql.LONGTEXT, nullable=False)
    style = Column(mysql.LONGTEXT, nullable=True)
    use_avg_residential_consumption = Column(Boolean, nullable=True)
    
    country = relationship(
      "Country", 
      foreign_keys=[country_id]
    )
    
    region = relationship(
        "Region", 
        foreign_keys=[region_id],
        backref="monotonous_electricity_consumptions"
    )