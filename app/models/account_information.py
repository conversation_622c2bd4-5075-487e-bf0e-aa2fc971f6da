from typing import Optional

from sqlalchemy import (
    Column,
    String,
    Integer,
    Foreign<PERSON>ey,
    Enum,
    Boolean
)
from app.config.db.database import Base
from sqlalchemy.orm import relationship

from app.enums.support_enum import SupportEnum
from app.models.customer import Customer
from app.models.account_type import AccountType
from app.models.school_category import SchoolCategory
from app.models.professional_category import ProfessionalCategory

from app.enums.age_range_enum import AgeRangeEnum


class AccountInformation(Base):
    __tablename__ = "account_information"
    id = Column(Integer, primary_key=True, autoincrement=True)
    customer_id = Column(Integer, ForeignKey("customer.id"))
    account_type_id = Column(Integer, ForeignKey("account_type.id"))
    professional_category_id = Column(Integer, ForeignKey("professional_category.id"))
    school_category_id = Column(Integer, ForeignKey("school_category.id"))
    profession = Column(String(255))
    age_range = Column(Enum(AgeRangeEnum))
    siret_number = Column(String(25))
    company_name = Column(String(255))
    support = Column(Enum(SupportEnum))
    support_verified = Column(Boolean(), default=False)
    other_category = Column(String(255))
   
    customer = relationship(
        "Customer",
        foreign_keys=[customer_id],
        back_populates="account_information",
    )
    school_category = relationship(
        "SchoolCategory",
        foreign_keys=[school_category_id],
    )
    professional_category = relationship(
        "ProfessionalCategory",
        foreign_keys=[professional_category_id],
    )
    account_type = relationship(
        "AccountType",
        foreign_keys=[account_type_id],
    )
