from __future__ import annotations
from datetime import datetime, timedelta
from typing import Any, Optional
from sqlalchemy import <PERSON>um<PERSON>, JSO<PERSON>, Integer, Foreign<PERSON>ey, String, <PERSON>olean, DateTime
from sqlalchemy.orm import relationship
from sqlalchemy.dialects import mysql
from app.config.db.database import Base
from app.models.customer import Customer
from app.models.customer_email_sender import CustomerEmailSender
from app.schemas.customer_email_item import CustomerEmailItemGenerateParams


class CustomerEmailItem(Base):
    EXTENDED_PROMOTION_AVAILABILITY_DURATION_DAYS = 3
    
    __tablename__ = "customer_email_item"
    
    sent_at = Column(DateTime)
    sender_email= Column(String(255))
    language = Column(String(5))
    customer_id = Column(Integer, ForeignKey("customer.id"))
    country_id = Column(Integer, ForeignKey("country.id")) 
    customer_email_task_id = Column(Integer, ForeignKey("customer_email_task.id"))
    error = Column(mysql.LONGTEXT)
    
    brevo = Column(Bo<PERSON>an, nullable=True)
    brevo_template = Column(Integer, nullable=True)
    workflow_id = Column(Integer, nullable=True, default=None)
    current_task = Column(Integer, nullable=True, default=None)
    campaign_number_brevo = Column(Integer, nullable=True, default=None)
    
    country = relationship(
        "Country",
        foreign_keys=[country_id],
    )
    
    customer = relationship(
        "Customer",
        foreign_keys=[customer_id],
    )
    
    customer_email_task = relationship(
        "CustomerEmailTask",
        foreign_keys=[customer_email_task_id],
    )
    
    sender: CustomerEmailSender  = None
    generation_params: CustomerEmailItemGenerateParams = None
    random_subscribed_customer: Customer = None
    prev_email_item: CustomerEmailItem = None
    additional_template_vars: Any = None
    
    def get_last_offer_day(self):
        # Last offer day becomes the 3rd day after email 4
        #return self.customer.created_at + self.generation_params.special_offer_duration
        if not self.prev_email_item:
            return None
        if self.customer_email_task_id == 5:
            return self.prev_email_item.sent_at + timedelta(days=self.EXTENDED_PROMOTION_AVAILABILITY_DURATION_DAYS)
        if self.customer_email_task_id == 4:
            return self.generation_params.current_datetime + timedelta(days=self.EXTENDED_PROMOTION_AVAILABILITY_DURATION_DAYS)
        return None
    
    def get_testimony_customer_data(self):
        # TODO: Dynamise data
        if self.random_subscribed_customer:
            return {
                "first_name": self.random_subscribed_customer.first_name
            }
        if not self.country:
            return {
                "first_name": "John" 
            }
        return self.country.default_customer_json