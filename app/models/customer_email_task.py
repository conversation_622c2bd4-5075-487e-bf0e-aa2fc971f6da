from sqlalchemy import <PERSON><PERSON>n, JSO<PERSON>, Integer, Foreign<PERSON>ey, String, Boolean
from sqlalchemy.orm import relationship 
from app.config.db.database import Base  

class CustomerEmailTask(Base):
    __tablename__ = "customer_email_task"
    
    key = Column(String(255))
    name =  Column(String(255))
    email_number = Column(Integer)
    cms_key =  Column(String(255))
    is_active = Column(Boolean)
    customer_email_campaign_id = Column(Integer, ForeignKey("customer_email_campaign.id"), nullable=True)
     
    generate_customer_email_items = None
    pre_sending_process = None
    
    
    customer_email_campaign = relationship(
        "CustomerEmailCampaign",
        foreign_keys=[customer_email_campaign_id],
    )
     
    