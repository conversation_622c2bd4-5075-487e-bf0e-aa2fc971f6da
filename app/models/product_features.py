from app.config.db.database import Base

from sqlalchemy import Column, Integer, String, ForeignKey, Float, Boolean
from sqlalchemy.orm import relationship

# from app.models.product import Product
from app.models.features import Features


class ProductFeatures(Base):
    __tablename__ = "product_features"

    id = Column(Integer, primary_key=True, autoincrement=True)
    product_id = Column(Integer, ForeignKey("product.id"))
    features_id = Column(Integer, ForeignKey("features.id")) 
    allow=Column(Boolean, nullable=False, default=True)
    
    product = relationship("Product", back_populates="product_features")
    features = relationship("Features", back_populates="product_features", order_by="Features.ui_order.asc()")
