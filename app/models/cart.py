from typing import List
from app.models.customer import Customer
from app.models.subscription import Subscription
from sqlalchemy import Column, String, Integer, ForeignKey, Boolean, JSON, DateTime
from app.config.db.database import Base
# from app import crud
from sqlalchemy.orm import relationship
from sqlalchemy.orm import Session
from fastapi.encoders import jsonable_encoder
class Cart(Base):
    # __tablename__ = "cart"
    id = Column(Integer, primary_key=True, autoincrement=True)
    cart_reference = Column(String(255))
    product_id = Column(Integer, ForeignKey("product.id"))
    
    quantity = Column(Integer)
    converted_at = Column(DateTime)
    product_json = Column(JSON)
    country_id = Column(Integer, ForeignKey("country.id"))
    language_iso_2 = Column(String(50))
    ip_address = Column(String(255))
    
    product = relationship(
        "Product",
        foreign_keys=[product_id],
    )
    
    subscription_payment_transaction = relationship(
        "SubscriptionPaymentTransaction", back_populates="cart"
    )
    def validateCart(self,db: Session, subscriptionList: List[Subscription]=[]):
        if self.product is not None:
            self.product.validate(subscriptionList)
        return subscriptionList