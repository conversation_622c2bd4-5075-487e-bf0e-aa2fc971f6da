from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ger, <PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>, String
from sqlalchemy.orm import relationship
from sqlalchemy.dialects import mysql
from app.config.db.database import Base

class MonotonousElectricityConsumptionDaily(Base):
    __tablename__ = "monotonous_electricity_consumption_daily"
    
    country_id = Column(Integer, ForeignKey("country.id"), nullable=True)
    region_id = Column(Integer, ForeignKey("region.id"), nullable=True)
    residential_consumption_json = Column(JSON)
    residential_consumption_sum = Column(JSON)
    residential_consumption_percentage_json = Column(JSON)
    content = Column(mysql.LONGTEXT, nullable=True)
    style = Column(mysql.LONGTEXT, nullable=True)
    use_avg_residential_consumption = Column(Boolean, nullable=True)
    
    country = relationship("Country", foreign_keys=[country_id])
    region = relationship("Region", back_populates="monotonous_electricity_consumptions_daily")
