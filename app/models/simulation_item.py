from sqlalchemy import (
    Column,
    ForeignKey,
    Integer,
    String,
    Integer,
    ForeignKey,
    JSON,
)
from app.config.db.database import Base
from sqlalchemy.orm import relationship


class SimulationItem(Base):
    __tablename__ = "simulation_item"

    id = Column(Integer, primary_key=True, autoincrement=True)
    params_json = Column(JSON)
    name = Column(String(255))
    simulation_type = Column(String(255))
    created_by = Column(Integer)

    simulation_id = Column(Integer, ForeignKey("simulation.id"))

    simulation = relationship(
        "Simulation",
        back_populates="simulation_item",
    )
