from sqlalchemy import Colum<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, func, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey
from sqlalchemy.orm import relationship, column_property
from app.config.db.database import Base
from sqlalchemy.dialects import mysql
from sqlalchemy.ext.hybrid import hybrid_method

class CityError(Base):
    __tablename__ = "city_error"
    
    city = Column(String(255), index=True)
    country = Column(String(255))
    error = Column(mysql.LONGTEXT)
   
    