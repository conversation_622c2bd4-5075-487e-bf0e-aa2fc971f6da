from sqlalchemy import Column, <PERSON>, <PERSON><PERSON><PERSON>, func, Integer, Foreign<PERSON>ey
from sqlalchemy.orm import relationship, column_property
from app.config.db.database import Base
from sqlalchemy.dialects import mysql
from sqlalchemy.ext.hybrid import hybrid_method

class City(Base):
    __tablename__ = "city"
    
    name = Column(String(255), index=True)
    latitude = Column(mysql.DOUBLE(asdecimal=True), nullable=False)
    longitude = Column(mysql.DOUBLE(asdecimal=True), nullable=False)
    import_id = Column(String(255))
    country_id =  Column(Integer, ForeignKey("country.id"))
    status = Column(Boolean, default=False)
    google_place_id = Column(mysql.VARCHAR(255))
    source_file_name = Column(mysql.VARCHAR(500))
    source_row_id = Column(Integer)
    normalized_name = Column(String(255), index=True)
    
    country = relationship(
        "Country",
        foreign_keys=[country_id],
    )
    
    