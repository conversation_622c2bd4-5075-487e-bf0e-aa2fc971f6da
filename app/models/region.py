from sqlalchemy import Column, Integer, String, ForeignKey
from app.config.db.database import Base
from sqlalchemy.orm import relationship

class Region(Base):
    __tablename__ = "region"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), index=True)
    short_code = Column(String(10), unique=True) 
    country_id = Column(Integer, ForeignKey("country.id"))
    normalized_name = Column(String(255), index=True) 
    
    
    country = relationship("Country" , foreign_keys=[country_id])
    
    
    monotonous_electricity_consumption = relationship("MonotonousElectricityConsumption", back_populates="region")
    monotonous_electricity_consumptions_daily = relationship("MonotonousElectricityConsumptionDaily", back_populates="region")

