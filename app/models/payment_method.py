from sqlalchemy import Column, String, Integer, ForeignKey
from app.config.db.database import Base
from sqlalchemy.orm import relationship


class PaymentMethod(Base):
    __tablename__ = "payment_method"
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(255))

    subscription_payment_transaction = relationship(
        "SubscriptionPaymentTransaction", back_populates="payment_method"
    )