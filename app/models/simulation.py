from sqlalchemy import (
    Column,
    Enum,
    ForeignKey,
    Integer,
    Numeric,
    String,
    Integer,
    ForeignKey,
    JSON,
)
from app.config.db.database import Base
from sqlalchemy.orm import relationship
from sqlalchemy.dialects import mysql

from app.enums.location_type_enum import LocationTypeEnum, ProfileEnum
from app.enums.simulation_type_enum import TypeEnum


class Simulation(Base):
    __tablename__ = "simulation"

    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(255))
    type = Column(Enum(TypeEnum))
    location_type = Column(Enum(LocationTypeEnum))
    latitude = Column(mysql.DOUBLE(asdecimal=True), nullable=False)
    longitude = Column(mysql.DOUBLE(asdecimal=True), nullable=False)
    street_number = Column(String(255))
    street = Column(String(255))
    city = Column(String(255))
    country = Column(String(255))
    profile = Column(Enum(ProfileEnum))
    simulation_info = Column(JSON)
    customer_name = Column(String(255))
    simulation_carbon = Column(JSON)
    region_id = Column(Integer, ForeignKey("region.id"), nullable=True)  
    country_id = Column(Integer, ForeignKey("country.id"), nullable=True)
    essai_count = Column(Integer, default=1, nullable=True)

    country_rel = relationship(
        "Country",
        backref="simulations"  
    )
  
    subscription_id = Column(Integer, ForeignKey("subscription.id"))

    subscription = relationship(
        "Subscription",
        back_populates="simulation",
    )

    simulation_item = relationship(
        "SimulationItem",
        back_populates="simulation",
    )
    
    region_rel = relationship(
        "Region", 
        backref="simulations"
    )
