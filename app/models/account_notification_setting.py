from sqlalchemy import (
    <PERSON><PERSON><PERSON>,
    String,
    Integer,
    <PERSON><PERSON><PERSON>,
    <PERSON>um,
)
from app.config.db.database import Base
from sqlalchemy.orm import relationship
from app.models.customer import Customer
from app.models.notification_setting import NotificationSetting



class AccountNotificationSetting(Base):
    __tablename__ = "account_notification_setting"
    id = Column(Integer, primary_key=True, autoincrement=True)
    notification_setting_id = Column(Integer, ForeignKey("notification_setting.id"))
    customer_id = Column(Integer, ForeignKey("customer.id"))
    
    notification_setting = relationship(
        "NotificationSetting",
        foreign_keys=[notification_setting_id],
    )