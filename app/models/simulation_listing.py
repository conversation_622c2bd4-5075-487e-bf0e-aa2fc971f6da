from sqlalchemy import <PERSON>umn, JSO<PERSON>, Integer, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.dialects import mysql
from app.config.db.database import Base

class SimulationListing(Base):
    __tablename__ = "simulation_listing"
    
    city_id =  Column(Integer, ForeignKey("city.id"))
    simulation_data = Column(JSON)
    horizon_data = Column(mysql.LONGTEXT)
    simulation_input = Column(JSON) 
    simulation_image = Column(JSON)
    content = Column(mysql.LONGTEXT)
    
    city = relationship(
        "City",
        foreign_keys=[city_id],
    )