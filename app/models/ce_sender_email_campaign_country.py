

from datetime import datetime

from sqlalchemy import <PERSON>umn, <PERSON><PERSON><PERSON>, Integer, String, UniqueConstraint
from sqlalchemy.orm import relationship
from app.config.db.database import Base


class CeSenderEmailCampaignCountry(Base):
    __tablename__ = "ce_sender_email_campaign_country"
    
     
    customer_email_campaign_id=  Column(Integer, ForeignKey("customer_email_campaign.id")) 
    customer_email_sender_id=  Column(Integer, ForeignKey("customer_email_sender.id"))
    country_id=  Column(Integer, ForeignKey("country.id"))
    
    customer_email_campaign = relationship(
        "CustomerEmailCampaign",
        foreign_keys=[customer_email_campaign_id],
    )
    
    customer_email_sender= relationship(
        "CustomerEmailSender",
        foreign_keys=[customer_email_sender_id],
    )
    
    __table_args__ = (UniqueConstraint('customer_email_campaign_id', 'customer_email_sender_id','country_id', name='uq_sender_et_c'),)