from datetime import datetime
from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, Enum, Float, Integer, String, func
from app.config.db.database import Base
from sqlalchemy.orm import relationship
from sqlalchemy.orm import Session
from typing import List

from app.enums.billing_period_interval_enum import BillingPeriodIntervalEnum
 
# from app.models.product_features import ProductFeatures


class Product(Base):
    __tablename__ = "product"

    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(255))
    user_count = Column(Integer)
    monthly_credit = Column(Float)
    monthly_price = Column(Float)
    billing_period_interval = Column(Enum(BillingPeriodIntervalEnum))
    is_recommended = Column(Boolean)
    show = Column(Boolean)
    allow_new_subscribers = Column(Boolean)
    description_translation_key = Column(String(255))
    description_note_translation_key = Column(String(255))
    stripe_price_id = Column(String(255))
    ui_order=Column(Integer)
    subscription_max_count=Column(Integer,nullable=True)
    allow_trial = Column(Boolean)
    discount_stripe_id = Column(String(255))
    additional=Column(Boolean)
    type=Column(Integer,nullable=True)
    error:str
    avalaible:bool=True
    
    
    product_features = relationship(
        "ProductFeatures", back_populates="product", uselist=True
    )
    # cart = relationship(
    #     "Cart",
    #     back_populates="product",
    # )
    def validate(self,subscriptionList:List[any] =[]):
        self.avalaible=True
        if len(subscriptionList) == 0:
            return self
        actualSubscription=subscriptionList[len(subscriptionList)-1]
        actualProduct = actualSubscription.product_json
      
        if len(subscriptionList)>0 and self.subscription_max_count is not None:
            if self.subscription_max_count <= len(subscriptionList):
                self.avalaible = False
                self.error = "pvgis.subscription_max_reached"
        elif len(subscriptionList) > 1:
            self.avalaible = False
            self.error = "pvgis.waiting_next_month"
        
        if(self.additional):
            self.avalaible=True
            self.error=None
        return subscriptionList
    
    def get_credit(self):
         
        return self.monthly_credit
    
    def get_stripe_price_id(self):
         
        return self.stripe_price_id
    
    @staticmethod
    def get_credit_from_json(product_json):
        if not product_json :
            return None
        return product_json['monthly_credit']
    
    @staticmethod
    def get_price_from_json(product_json):
        if not product_json :
            return None 
        return product_json['monthly_price']