from __future__ import annotations
from datetime import datetime, timedelta
from typing import Any, Optional
from sqlalchemy import <PERSON>umn, JSON, Integer, ForeignKey, String, Boolean, DateTime
from sqlalchemy.orm import relationship
from app.config.db.database import Base
from app.models.customer import Customer


class CustomerEmailItemTracking(Base):
    EXTENDED_PROMOTION_AVAILABILITY_DURATION_DAYS = 3
    
    __tablename__ = "customer_email_item_tracking"
    
    event = Column(String(255), index=True)
    payload = Column(JSON)
    date_event = Column(DateTime)
    sent_at = Column(DateTime)
    sender_email= Column(String(255), nullable=True)
    sending_ip= Column(String(255), nullable=True)
    customer_id = Column(Integer, ForeignKey("customer.id"))
    language = Column(String(5), nullable=True)
    device_used = Column(String(255), nullable=True)
    brevo_template = Column(Integer, nullable=True)
    workflow_id = Column(Integer, nullable=True, default=None)
    template_id = Column(Integer, nullable=True, default=None)
    current_task = Column(Integer, nullable=True, default=None)
    campaign_number_brevo = Column(Integer, nullable=True, default=None)
    
    customer = relationship(
        "Customer",
        foreign_keys=[customer_id],
    )