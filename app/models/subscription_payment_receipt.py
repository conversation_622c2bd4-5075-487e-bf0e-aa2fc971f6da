from sqlalchemy import (
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>an,
    Column,
    DateTime,
    String,
    Integer,
    ForeignKey,
    Enum,
    Float
)
from app.config.db.database import Base
from sqlalchemy.orm import relationship
 
from app.enums.receipt_status_enum import ReceiptStatusEnum
from app.models.payment_gateway import PaymentGateway
from sqlalchemy.dialects import mysql

class SubscriptionPaymentReceipt(Base):
    __tablename__ = "subscription_payment_receipt"

    id = Column(Integer, primary_key=True)
    reference = Column(String(255))
    stripe_reference = Column(String(255))
    stripe_object_id = Column(String(255))
    subscription_stripe_id = Column(String(255), nullable=True)
    amount_paid = Column(mysql.DOUBLE(asdecimal=True), nullable = True) # Same as invoice if None
    remaining_to_pay= Column(mysql.DOUBLE(asdecimal=True), nullable = True)
    amount_already_paid= Column(mysql.DOUBLE(asdecimal=True), nullable = True)
    refunded_stripe_object_id = Column(String(255), nullable = True)
    subscription_payment_transaction_id = Column(Integer, ForeignKey("subscription_payment_transaction.id"), nullable=True)
    status = Column(Enum(ReceiptStatusEnum))
    subscription_payment_transaction = relationship(
        "SubscriptionPaymentTransaction",
        foreign_keys=[subscription_payment_transaction_id],
        back_populates="subscription_payment_receipts",
    )
    
    
