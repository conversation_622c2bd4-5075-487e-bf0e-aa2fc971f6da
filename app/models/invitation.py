from sqlalchemy import (
    Column,
    DateTime,
    Foreign<PERSON><PERSON>,
    Integer,
    String,
    func, 
    Text
)
from sqlalchemy.orm import column_property
from datetime import datetime
from app.config.db.database import Base
from sqlalchemy.orm import relationship


class Invitation(Base):
    __tablename__ = "invitation"

    customer_id = Column(Integer, ForeignKey("customer.id"))  # Recipient
    user_sender_id = Column(Integer)  # Sender
    invitation_link = Column(Text)  # Renommé link_invitation à invitation_link
    invitation_sent_at = Column(DateTime, default=func.now())  # Renommé sent_at à invitation_sent_at
    invitation_accepted_at = Column(DateTime)  # <PERSON><PERSON><PERSON> responded_at à invitation_accepted_at
    email = Column(String(255)) 

    # relation
    customer = relationship("Customer", foreign_keys=[customer_id], backref="invitations_received")

    account_information = relationship(
        "AccountInformation", 
        foreign_keys=[customer_id],
        primaryjoin="Invitation.customer_id == AccountInformation.customer_id",
        backref="customer_company", 
        uselist=False  
    )

 