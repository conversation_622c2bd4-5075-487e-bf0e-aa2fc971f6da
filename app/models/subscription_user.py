from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, String
from app.config.db.database import Base
from sqlalchemy.orm import relationship


class SubscriptionUser(Base):
    __tablename__ = "subscription_user"

    first_name = Column(String(255))
    last_name = Column(String(255))
    is_active = Column(Boolean)
    auth_user_id = Column(Integer, index=True)
    email = Column(String(255))
    pseudo = Column(String(255))

    subscription_id = Column(Integer, ForeignKey("subscription.id"))

    subscription = relationship("Subscription", back_populates="subscription_user")