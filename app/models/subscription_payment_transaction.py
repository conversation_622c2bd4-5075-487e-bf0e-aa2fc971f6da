from sqlalchemy import (
    JSO<PERSON>,
    Boolean,
    Column,
    DateTime,
    String,
    Integer,
    ForeignKey,
    Enum,
    Float
)
from app.config.db.database import Base
from sqlalchemy.orm import relationship

from app.enums.payment_status_enum import PaymentStatusEnum
from app.models.payment_gateway import PaymentGateway
from sqlalchemy.dialects import mysql

class SubscriptionPaymentTransaction(Base):
    __tablename__ = "subscription_payment_transaction"

    id = Column(Integer, primary_key=True)
    reference = Column(String(255)) 
    paid = Column(Boolean)
    amount_paid=Column(mysql.DOUBLE(asdecimal=True))
    currency = Column(String(10))
    payment_date = Column(DateTime)
    payment_method_id = Column(Integer, ForeignKey("payment_method.id"))
    subscription_id = Column(Integer, ForeignKey("subscription.id"))
    subscription_stripe_id = Column(String(255))
    next_payment_date = Column(DateTime)
    payment_transaction_json = Column(JSON)
    payment_status = Column(Enum(PaymentStatusEnum))
    cart_id = Column(Integer, ForeignKey("cart.id"))
    payment_gateway_id = Column(Integer, ForeignKey("payment_gateway.id"))
    payment_gateway = relationship("PaymentGateway")
    stripe_object_id = Column(String(255), nullable=True, unique=True)
    stripe_terminal_status = Column(String(255), nullable=True)
    is_new_subscription_payment = Column(Boolean)
    customer_json = Column(JSON)

    subscription = relationship(
        "Subscription", back_populates="subscription_payment_transaction"
    )
    cart = relationship(
        "Cart", back_populates="subscription_payment_transaction"
    )
    payment_method = relationship(
        "PaymentMethod", back_populates="subscription_payment_transaction"
    )
    subscription_payment_receipts = relationship(
        "SubscriptionPaymentReceipt", back_populates="subscription_payment_transaction",
        uselist=True
    )
    
    invoice_receipt_displayed_name= None
