from datetime import datetime
from sqlalchemy import (
     
    Column,
    Integer,
    Foreign<PERSON>ey,
    DateTime,
    JSON,
    Enum,
    String,
    select, 
    func, 
    literal_column,
    Boolean,
    case
)
from app.config.db.database import Base
from sqlalchemy.orm import column_property, relationship
from app.enums import billing_period_interval_enum
from app.enums.billing_period_interval_enum import BillingPeriodIntervalEnum
from app.enums.subscription_status_enum import SubscriptionStatusEnum

from dateutil.relativedelta import relativedelta

class Subscription(Base):
    __tablename__ = "subscription"

    id = Column(Integer, primary_key=True, autoincrement=True)
    product_json = Column(JSON)
    credit_balance = Column(Integer)
    created_by = Column(Integer)
    disabled_at = Column(DateTime)
    start_date = Column(DateTime)
    expired_date = Column(DateTime)
    renewal_frequency = Column(Integer, default=1)
    subscription_status = Column(Enum(SubscriptionStatusEnum))
    is_auto_renew = Column(Boolean, default= True)
   
    product_id = Column(Integer, ForeignKey("product.id"))
    customer_id = Column(Integer, ForeignKey("customer.id"))
    coupon_end = Column(DateTime)
    additional_credit_balance = Column(Integer)
    subscription_stripe_id = Column(String(255))
    
    subscription_payment_transaction = relationship(
        "SubscriptionPaymentTransaction",
        back_populates="subscription",
    )
    
    subscription_user = relationship(
        "SubscriptionUser",
        back_populates="subscription",
    )

    simulation = relationship(
        "Simulation",
        back_populates="subscription",
    )

    customer = relationship(
        "Customer",
        foreign_keys=[customer_id],
    )
    
    product = relationship(
        "Product",
        foreign_keys=[product_id],
    )
    
    stripe_subscription = None
    
     
    # @classmethod
    # def set_expired_date(cls, instance):
    #     billing_period_interval = None
        
    #     if isinstance(instance.product_json, dict):
    #         billing_period_interval = (
    #             BillingPeriodIntervalEnum(instance.product_json['billing_period_interval'])
    #             if 'billing_period_interval' in instance.product_json
    #             else BillingPeriodIntervalEnum.month
    #         )
    #     else:
    #         billing_period_interval = (
    #             BillingPeriodIntervalEnum(instance.product_json.billing_period_interval)
    #             if instance.product_json.billing_period_interval
    #             else BillingPeriodIntervalEnum.month
    #         )
        
        
    #     instance.expired_date =  cls.calculate_expired_date(instance.start_date, billing_period_interval)
    #     return instance.expired_date
    
    @staticmethod
    def calculate_expired_date(start_date: datetime, billing_period_interval:BillingPeriodIntervalEnum):
        
        renewal_frequency = 1
        
        relative_to_add =  relativedelta(**{
            **({"years": renewal_frequency} if billing_period_interval == BillingPeriodIntervalEnum.year else {}),
            **({"months": renewal_frequency} if billing_period_interval == BillingPeriodIntervalEnum.month else {})
        } 
        )
        expired_date =   start_date + relative_to_add
        return expired_date
        
            
    
