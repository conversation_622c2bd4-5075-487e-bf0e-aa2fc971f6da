from sqlalchemy import (
    Colum<PERSON>,
    String,
    Integer,
    <PERSON><PERSON><PERSON>,
    Enum,
)
from app.config.db.database import Base
from sqlalchemy.orm import relationship
from app.models.customer import Customer
from app.enums.type_notification_setting_enum import TypeNotificationSettingEnum


class NotificationSetting(Base):
    __tablename__ = "notification_setting"
    id = Column(Integer, primary_key=True, autoincrement=True)
    key = Column(String(255), unique=True, index=True)
    name = Column(String(255))
    type = Column(Enum(TypeNotificationSettingEnum))