from __future__ import annotations 
from sqlalchemy import <PERSON>umn, JSON, Integer, ForeignKey, String, Boolean, DateTime
from sqlalchemy.orm import relationship 
from app.config.db.database import Base 


class CustomerEmailCampaign(Base):
    
    
    __tablename__ = "customer_email_campaign"
    
    name = Column(String(255))
    is_active = Column(Boolean, default=True)
    key =  Column(String(255))
    
    customer_email_tasks = relationship(
        "CustomerEmailTask",
        back_populates="customer_email_campaign", 
        uselist=True,
    )
    
    ce_sender_email_campaign_countries = relationship(
        "CeSenderEmailCampaignCountry",
        back_populates="customer_email_campaign",
        uselist=True,
    )
    
    ce_campaign_country_data = relationship(
        "CeCampaignCountryData",
        back_populates="customer_email_campaign",
        uselist=True,
    )
