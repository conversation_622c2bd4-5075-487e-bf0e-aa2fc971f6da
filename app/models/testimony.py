from sqlalchemy import Column, String, <PERSON>olean, func, Integer, ForeignKey, Text
from sqlalchemy.orm import relationship, column_property
from app.config.db.database import Base


class Testimony(Base):
    __tablename__ = "testimony"

    name = Column(String(255), index=True)
    flag = Column(String(255))
    base_language = Column(String(10))
    languages = Column(String(100))
    country_id = Column(Integer, ForeignKey("country.id"))
    testimony = Column(Text)
    is_default = Column(Boolean(), default=False)
    country = relationship(
        "Country",
        foreign_keys=[country_id],
    )
