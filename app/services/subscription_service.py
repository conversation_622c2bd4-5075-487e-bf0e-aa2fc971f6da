from fastapi import HTTPException
from app import crud
from app.enums.subscription_status_enum import SubscriptionStatusEnum


def get_user_and_subscription_user(db, user_id, throw_if_not_found = True, isHttpException = True):
    
    subscription_conditions = [
        # why we will need check disabled is null of subscription ?
        # {"key": "subscription.disabled_at", "operator":"isNull"},
        {"key": "subscription.deleted_at", "operator":"isNull"},
        {"key": "subscription.subscription_status", "value": SubscriptionStatusEnum.ACTIVE, "operator": "=="}
    ]
    found_customer = crud.customer.get_first_where_array_v2(
        db=db,
        where=[{"key": "auth_user_id", "value": user_id, "operator": "=="}],
        relations=[],
        base_columns=["deleted_at","to_delete_at"]
    )
    found_subscription_user = crud.subscription_user.get_first_where_array_v2(
        db=db, 
        where=[
            {"key": "auth_user_id", "value": user_id, "operator": "=="},
            {"key": "is_active", "value": 1, "operator": "=="},
            *subscription_conditions
        ],
        relations=[r"subscription.customer{deleted_at,to_delete_at}"], 
        base_columns=[]
    ) 
    if throw_if_not_found and not found_customer and not found_subscription_user:
        if isHttpException:
            raise HTTPException(
                status_code=400, detail="auth.login.could_not_authenticate_create_account"
            )
        raise Exception("auth.login.could_not_authenticate_create_account")
    return found_customer, found_subscription_user