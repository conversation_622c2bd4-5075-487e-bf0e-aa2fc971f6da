from __future__ import annotations
from typing import Any
from app.enums.billing_period_interval_enum import BillingPeriodIntervalEnum
from app.enums.subscription_action_type_enum import SubscriptionActionTypeEnum
from app.models.subscription import Subscription
from app.utils.utils import send_email
from app.crud.crud_cart import cart as cart_crud
import stripe
from sqlalchemy.orm import Session
from datetime import datetime
from app.config.settings import get_settings
from dateutil.relativedelta import relativedelta

settings = get_settings()


def get_welcome_discount_validity_period():
    coupon = stripe.Coupon.retrieve(settings.STRIPE_COUPON_FIFTY_ID) 
    duration = coupon.get("duration_in_months", 0)
    return duration

def update_stripe_subscription(db:Session,customer_stripe_id: str, cart_reference: str, subscription_stripe_ob, new_start_date_tmstp = None, trial_now = False, isUpdgrade = False):
    
    
    # Retrieve the cart and associated product details
    cart= cart_crud.get_first_where_array(db=db,where=[{"key":"cart_reference","operator":"==","value":cart_reference}],relations=["product"])
    new_price_id = cart.product.get_stripe_price_id()
     
    # print(subscription)
    # Check if the subscription was retrieved successfully
    if subscription_stripe_ob and subscription_stripe_ob.get('id'):
        previous_subscription_id = subscription_stripe_ob['id']
        previous_billing_period = BillingPeriodIntervalEnum(subscription_stripe_ob['items']['data'][0]['price']['recurring']['interval'])
        
        previous_cycle_anchor = None 
        
        #If the subscription is in trial, use the start date as the cycle anchor for we dont have trials that go beyound one cycle
        # we cant use the billing cycle anchor because its value is the anchor of the cycle that follows the trial
        if subscription_stripe_ob['status'] == 'trialing':
            previous_cycle_anchor = datetime.fromtimestamp(subscription_stripe_ob['start_date'])
        else:
            previous_cycle_anchor = datetime.fromtimestamp(subscription_stripe_ob['current_period_start'])
        
        trial_end = previous_cycle_anchor + (
            relativedelta(years=1) if cart.product.billing_period_interval == BillingPeriodIntervalEnum.year else relativedelta(months=1)
        )
         
        # Void previous open invoice
        void_latest_invoice(subscription_stripe_ob)
        
     
        # Update the subscription with the new plan
        if trial_now:
            # print("--------------------------------")
            # print("We are 2 to 1")
            # print("--------------------------------")
            
            if not isUpdgrade:
                stripe.Subscription.modify(
                    previous_subscription_id,
                    metadata={"cartReference": cart_reference},
                    items=[
                        {
                            'id': subscription_stripe_ob['items']['data'][0]['id'],
                            'price': new_price_id,  # Update to the new plan's price ID
                        }
                    ],
                    # proration_behavior='none',  # Payment has already been done
                    # trial_end='now',
                    # billing_cycle_anchor='unchanged', 
                    # proration_behavior='create_prorations',
                    # payment_behavior='pending_if_incomplete',
                )
                print("---------------------------------------------------")
                print("DOWNGRADE")
                print("---------------------------------------------------")
            else:
                print("---------------------------------------------------")
                print("UPGRADE")
                print("---------------------------------------------------")
                
                # DONT NEED TO UPDATE, THE TRANSACTION IS ALREADY DONE
                # IF WE MODIFY, THERE WILL BE NEW TRANSACTION IN STRIPE
                # ABONNEMENT NE CHANGE PAS - BUG
                
                stripe.Subscription.modify(
                    previous_subscription_id,
                    items=[{
                        'id': subscription_stripe_ob['items']['data'][0]['id'],
                        'price': new_price_id,
                    }],
                    proration_behavior='none',
                    **(
                        # Add trial end to avoid charging the customer twice
                        {"trial_end": int(trial_end.timestamp())} 
                        if BillingPeriodIntervalEnum(previous_billing_period) != cart.product.billing_period_interval
                        else {}
                    ),
                )

                # stripe.Subscription.modify(
                #     previous_subscription_id,
                #     metadata={"cartReference": cart_reference},
                #     items=[
                #         {
                #             'id': subscription_stripe_ob['items']['data'][0]['id'],
                #             'price': new_price_id,  
                #         }
                #     ],
                #     # start_date='now',
                #     # end_behavior='release',
                    
                #     billing_cycle_anchor='unchanged',
                #     proration_behavior='none',
                #     payment_behavior='pending_if_incomplete',
                    
                #     # proration_behavior='none',  
                #     # payment_behavior='pending_if_incomplete',
                #     # trial_end='now',
                #     # proration_behavior='create_prorations',
                #     # payment_behavior='pending_if_incomplete',
                # )
        else:
            stripe.Subscription.modify(
                previous_subscription_id,
                metadata={"cartReference": cart_reference},
                items=[
                    {
                        'id': subscription_stripe_ob['items']['data'][0]['id'],
                        'price': new_price_id,  # Update to the new plan's price ID
                    }
                ],
                proration_behavior='none',  # Payment has already been done
                **(
                    # Add trial end to avoid charging the customer twice
                    {"trial_end": int(trial_end.timestamp())} 
                    if BillingPeriodIntervalEnum(previous_billing_period) != cart.product.billing_period_interval
                    else {}
                ),
                **(
                    
                    # Update the billing cycle anchor to the new start date
                    {"billing_cycle_anchor":new_start_date_tmstp} 
                    if new_start_date_tmstp
                    else {}
                ) 
            )
        
        
        
        return subscription_stripe_ob
    else:
        # Handle cases where the subscription retrieval failed
        print(f"Failed to retrieve subscription with ID: {customer_stripe_id}")
        
def create_future_subscription(
                               customer_stripe_id: str, 
                               metadata: Any,
                               future_subscription_inst: Subscription,
                               ):
    trial_end =  int(future_subscription_inst.start_date.timestamp())
    product_json = future_subscription_inst.product_json
    metadata['curSubId'] = future_subscription_inst.id
    subscription_stripe_ob = stripe.Subscription.create(
        customer=customer_stripe_id,
        metadata=metadata,
        items=[
            {
                'price': product_json['stripe_price_id'],
            }
        ],
        trial_end=trial_end,
        proration_behavior='none',   
    )
    return subscription_stripe_ob
        
def get_latest_active_subscription(customer_stripe_id: str):
    subs = []
    
    # Retrieve the list of subscriptions for the customer
     
    subscriptions = stripe.Subscription.list(
        customer=customer_stripe_id,
        status='active',  # Filter by active status
        limit=1           # Limit to the latest subscription
    )

    # Check if there are any active subscriptions
    if len(subscriptions['data']) > 0:
        # Return the latest active subscription
        subs.append(subscriptions['data'][0])
    
    subscriptions = stripe.Subscription.list(
        customer=customer_stripe_id,
        status='trialing',   
        limit=1           # Limit to the latest subscription
    )
    if len(subscriptions['data']) > 0:
        # Return the latest trialing subscription
        subs.append(subscriptions['data'][0])
    
    
    subscriptions = stripe.Subscription.list(
        customer=customer_stripe_id,
        status='past_due',   
        limit=1           # Limit to the latest subscription
    )
    if len(subscriptions['data']) > 0:
        # Return the latest past_due subscription
        subs.append(subscriptions['data'][0])
    
    # Sort the subscriptions by the current period start date DESC
    sorted_subs =  sorted(subs, key=lambda row: 
            row["current_period_start"] if "current_period_start" in row else
            row["items"]["data"][0]["current_period_start"]
                          , reverse= True)
    return next(iter(sorted_subs), None)

#This function is only temporary, by the time one can eventually get a lot of subs, 
# the previous subscription_stripe_id should be already available in our database
def get_prev_latest_active_subscription_or_throw(customer_stripe_id: str):
    # Retrieve the list of subscriptions for the customer
    subscriptions_active = stripe.Subscription.list(
        customer=customer_stripe_id,
        status='active',   
        limit=2            
    )
 
    
    subscriptions_trialing = stripe.Subscription.list(
        customer=customer_stripe_id,
        status='trialing',   
        limit=2            
    )
    
    data = [*subscriptions_active['data'], *subscriptions_trialing['data']]
    sorted_data = sorted(data, key=lambda x: x["created"], reverse=True)
     

    if len(sorted_data) > 1:
        return sorted_data[1]  
    
    raise Exception('No 2nd latest sub found')

def get_last_active_start_end_date(customer_stripe_id: str):
    last_subscription=get_latest_active_subscription(customer_stripe_id)
    if not last_subscription:
        return None
    start_date = last_subscription["current_period_start"] if "current_period_start" in last_subscription else last_subscription["items"]["data"][0]["current_period_start"]
    end_date =  last_subscription["current_period_end"] if "current_period_end" in last_subscription else last_subscription["items"]["data"][0]["current_period_end"]

    # Convert the Unix timestamps to datetime objects
    start_datetime = datetime.fromtimestamp(start_date)
    end_datetime = datetime.fromtimestamp(end_date)
    return {"start_date":start_datetime,"end_date":end_datetime}

 

def get_default_card(customer_id):

    payment_methods = stripe.PaymentMethod.list(
        customer=customer_id,
        type="card"
    )
    
    # Return details of the first payment method if available
    if payment_methods.data:
        card = payment_methods.data[0].card
        return {
            "brand": card.brand,
            "last4": card.last4,
            "exp_month": card.exp_month,
            "exp_year": card.exp_year
        }
    
    # Return None if no cards are found
    return None

def send_email_failed_webhook(webhook_event_type, webhook_data, error_details):
    
    email_param = {
        "templateVars": {
            "webhook_event_type": webhook_event_type,
            "webhook_data": webhook_data,
            "error_details":error_details,
            "api_url":settings.PVGIS_API_BASE_URL,
        },
        "mailVars": {
            "from": settings.SMPT_USER_MAIL,
            "to": settings.WEBHOOK_ADMIN_EMAIL,
        },
        "sentByApp": "app",
        "sentByProcess": "Webhook Error",
        "sentToUser": "",
        "type": "MAIL",
    }
    lang = "en"
    template_name = "pvgis-com%2F" + "failed-webhook"
    try:
        send_email(email_param, lang, template_name)
    except:
        pass
    

def get_last_relevant_payment_intent(stripe_customer_id,amount_to_refund, starting_after = None):
    # List recent invoices for the subscription
    invoices_res = stripe.Invoice.list(
        customer=stripe_customer_id,
        limit=10,  
        status="paid",
        **{} if not starting_after else {"starting_after":"starting_after"}
    )
    invoices = invoices_res['data']
    for invoice in invoices:
        if invoice['amount_paid'] >= amount_to_refund:
            return invoice  # Found the latest non-zero paid invoice
    if len(invoices) > 0:
        return get_last_relevant_payment_intent(stripe_customer_id,amount_to_refund, invoices[-1]['id'])
    return None

 

def get_charges_to_refund(customer_stripe_id, total_amount_to_refund, 
                          cur_recursive_amount_to_refund = None, starting_after_charge_stripe_id=None):
    if cur_recursive_amount_to_refund is None:
        cur_recursive_amount_to_refund = total_amount_to_refund
    
    #convert in cents
    total_amount_to_refund_in_cents = total_amount_to_refund * 100
    cur_recursive_amount_to_refund_in_cents = cur_recursive_amount_to_refund * 100
    
    
    charges_to_refund = []
    charges_res = stripe.Charge.list(
        customer=customer_stripe_id,
        limit=100, 
        starting_after=starting_after_charge_stripe_id,
        expand=["data.payment_intent.invoice"],
    )
    for charge in charges_res['data']: 
        payment_intent = charge['payment_intent']
        payment_intent_metadata = payment_intent['metadata']
        invoice = payment_intent['invoice']
        
        # Filtering if the charge is relevant:
        is_relevant_charge = (
            # Is a subscription creation or renewal charge
            (
                invoice
                and (
                    invoice['billing_reason'] == 'subscription_cycle'
                    or invoice['billing_reason'] == 'subscription_create'
                )
            )
            # Is an upgrade charge
            or (
                'isUpgrade' in payment_intent_metadata
                or (
                    'subscriptionActionType' in payment_intent_metadata  
                    and payment_intent_metadata['subscriptionActionType'] == SubscriptionActionTypeEnum.UPGRADE.value
                )
            )
        )
        
        if not is_relevant_charge:
            continue
        
        cur_refundable_amount_in_cents = charge['amount_captured'] - charge['amount_refunded']
        if cur_refundable_amount_in_cents > 0:
            cur_amount_to_refund_in_cents = min(cur_refundable_amount_in_cents, cur_recursive_amount_to_refund_in_cents)
            amount_already_refunded_in_cents = total_amount_to_refund_in_cents - cur_recursive_amount_to_refund_in_cents
            cur_recursive_amount_to_refund_in_cents -= cur_amount_to_refund_in_cents
            amount_to_refund_later_in_cents = cur_recursive_amount_to_refund_in_cents
          
            charges_to_refund.append({
                'charge': charge, 
                'amount_to_refund': cur_amount_to_refund_in_cents/100,
                'amount_to_refund_later': amount_to_refund_later_in_cents/100,
                'amount_already_refunded': amount_already_refunded_in_cents/100,
                })
        if cur_recursive_amount_to_refund_in_cents <= 0:
            break
    if charges_res['has_more'] and cur_recursive_amount_to_refund_in_cents > 0:
        charges_to_refund.extend(get_charges_to_refund(customer_stripe_id, total_amount_to_refund,cur_recursive_amount_to_refund_in_cents/100, charges_res['data'][-1]['id']))

    if cur_recursive_amount_to_refund_in_cents > 0:
        raise Exception('Not enough charges to refund')
    
    return charges_to_refund


def void_latest_invoice(subscription_stripe_ob):
    # Void previous open invoice
    previous_invoice_id = subscription_stripe_ob.get('latest_invoice', None)
    if previous_invoice_id :
        previous_invoice = stripe.Invoice.retrieve(previous_invoice_id)
        if previous_invoice["status"] in ["open", "draft"]:
            stripe.Invoice.void_invoice(previous_invoice)
            
def update_stripe_customer_email(customer_id, new_email):
    """
    Update the email address of a Stripe customer.

    Args:
        customer_id (str): The Stripe customer ID.
        new_email (str): The new email address to set.
    """
    try:
        updated_customer = stripe.Customer.modify(
            customer_id,
            email=new_email
        )
        return updated_customer
    except stripe.error.StripeError as e:
        print(f"Stripe error occurred: {e.user_message}")
        return None
