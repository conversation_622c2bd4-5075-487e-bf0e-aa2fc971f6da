import io
from PIL import Image
import os
import traceback
import numpy as np
import pymysql
import pytz
import requests
import json
import pandas as pd
from app.config.settings import get_settings
from datetime import datetime
settings = get_settings()

google_maps_api_key = settings.GOOGLE_MAPS_API_KEY #'AIzaSyBWDrBbwE1MaX-nUdzWm8vIYKynqa0XCgo'
file_path = f"{settings.BASE_DIR}/{settings.CITY_FILE_PATH}"   
document_service_url = f"{settings.FS_BASE_URL}/document"
google_geocode_url = settings.GOOGLE_GEOCODE_URL
simulation_url = f"{settings.PVGIS_API_BASE_URL}/api/v5_3/pvgis_grid_connected_or_tracking" 
extent_url = f"{settings.PVGIS_API_BASE_URL}/api/v5_3/extent"
static_google_maps_url = "https://maps.googleapis.com/maps/api/staticmap?center={lat},{lng}&zoom={zoom}&size={size}&maptype={map_type_id}&markers=color:red%7Clabel:+%7C{lat},{lng}&key={google_maps_api_key}"
horizon_url = f"{settings.PVGIS_API_BASE_URL}/api/v5_3/horizon_profile"
timezone_url = "https://maps.googleapis.com/maps/api/timezone/json"

def get_col_dataframe(main_data, col_id):
    results = None
    if len(main_data.columns) > col_id:
        results = main_data.iloc[:, col_id]
    else: 
        results = pd.DataFrame({f'{col_id}': [np.nan] * len(main_data)})
    return results
    
def integrate_cities_by_chunk(city_migrate):
    chunk_size = city_migrate.chunk_size
    connection = None 
    cursor = None 
    try: 
        connection = pymysql.connect(
            host=settings.MYSQL_HOST,
            user=settings.MYSQL_USER,
            password=settings.MYSQL_PASSWORD,
            database=settings.MYSQL_DATABASE,
            port=settings.MYSQL_PORT,
            cursorclass=pymysql.cursors.DictCursor
        )
       
        cursor = connection.cursor()
       
        last_inserted_file_row_id = get_last_inserted_file_row_id(cursor, file_path)

        if last_inserted_file_row_id is None:
            last_inserted_file_row_id = -1

      
        sheet_name = 0  
        start_row = last_inserted_file_row_id + 1  # Replace with your start row 

        data = read_excel_rows(file_path, sheet_name, start_row, chunk_size)
       
        if data is not None:
            data = data.fillna("")
            
            # Extract the necessary columns
            city_names = get_col_dataframe(data, 1)   
            country_names = get_col_dataframe(data, 2) 
            latitudes = get_col_dataframe(data, 4)  
            longitudes =   get_col_dataframe(data, 5)
            
            #not used for now
            success_count = 0
            failure_count = 0
            # cur_first_inserted_file_row_id = start_row
            # cur_last_inserted_file_row_id = data.shape[0]+cur_first_inserted_file_row_id-1
            i = start_row
            import_id = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            for city_name, country_name, latitude, longitude in zip(city_names, country_names, latitudes, longitudes):
                city_name = city_name.strip()
                country_name = country_name.strip()
                # latitude = None if np.isnan(latitude) else latitude
                # longitude = None if np.isnan(longitude) else longitude
                latitude = None if (isinstance(latitude, float) and np.isnan(latitude)) else latitude
                longitude = None if (isinstance(longitude, float) and np.isnan(longitude)) else longitude
                result = integrate_row(
                        cursor,
                        import_id,
                        city_name,
                        country_name,
                        latitude,
                        longitude,
                        i
                    )
                
                
                
                if result['is_success']:
                    connection.commit()
                    success_count += 1
                else:
                    connection.rollback()
                    insert_city_error(cursor,city_name, country_name, result['error_traceback'])
                    connection.commit()
                    failure_count += 1
                i += 1  
        else:
            print("No data to insert in the excel") 
    except Exception as e:
        if connection:
            connection.rollback()
        raise e 
    finally:
        if connection is not None:
            connection.close()
 
def save_chunk_report(cursor, chunk_report):
    cursor.execute(
    """
        INSERT INTO city_migration_report (
            first_inserted_file_row_id, 
            last_inserted_file_row_id, 
            success_count, 
            failure_count,
            chunk_size, 
            created_at
        )
        VALUES (%s,%s, %s, %s, %s, NOW())
    """,
        (
            chunk_report['first_inserted_file_row_id'], 
            chunk_report['last_inserted_file_row_id'], 
            chunk_report['success_count'], 
            chunk_report['failure_count'],
            chunk_report['chunk_size'],
        )
    )
    
    
    
    
    
def read_excel_rows(file_path, sheet_name=0, start_row=0, num_rows=None, header=None, custom_converters = None): 
    try:
        # Read the Excel file
        skiprows = start_row
        if header != None:
            df_head = pd.read_excel(file_path, nrows=0)
            valid_converters = ({k: v for k, v in custom_converters.items() if k in df_head.columns} 
                if custom_converters else None)
            if start_row == 1:
                skiprows = 0
            else:
                skiprows = range(1,start_row)
                
        
        data = pd.read_excel(file_path, sheet_name=sheet_name, 
                             skiprows=skiprows if header != None else start_row, 
                             nrows=num_rows, header=header, converters=valid_converters)
        data = data.applymap(lambda x: x.strip() if isinstance(x, str) else x)
        return data
    except Exception as e:
        print(f"An error occurred: {e}")
        return None

def get_last_inserted_file_row_id(cursor, source_file_path):
    cursor.execute("""
        SELECT source_row_id from city where source_file_name = %s order by created_at DESC 
    """, (source_file_path))
    res = cursor.fetchall()
    if len(res) == 0:
        return None  
    return res[0]['source_row_id']


def integrate_row(
    cursor,
    import_id, 
    city_name, 
    country_name, 
    latitude,
    longitude,
    source_row_id):
    try:
        process_city(cursor, 
                     import_id, 
                     city_name, 
                     country_name, 
                     latitude,
                     longitude,
                     source_row_id) 
        return {
            "is_success":True
            }
    except Exception as e: 
        #raise e
        error_traceback = traceback.format_exc()
        return {
            "is_success":False,
            "error_traceback": error_traceback 
        }
        
    
    

def insert_city_error(cursor, city, country, error): 
    cursor.execute("""
        INSERT INTO city_error (city,country,error)
        VALUES (%s, %s, %s)
    """, (city, country, error)) 
    


def process_city (
    cursor, 
    import_id, 
    city_name, 
    country_name, 
    provided_latitude,
    provided_longitude,
    source_row_id): 
         
        #INSERT CITY
        lat, lng, country_iso2, google_place_id,city_id = insert_city(
            cursor, 
            import_id, 
            city_name, 
            country_name, 
            provided_latitude,
            provided_longitude,
            source_row_id)
        
        #INSERT SIMULATION LISTING
        simulation_listing_id = insert_simulation_listing(cursor,import_id,  lat, lng, city_id)
        print ("staring uploading images: ")
        #UPLOAD IMAGE 
        files_info = upload_image(simulation_listing_id, lat, lng)
        print ("after uploading images: ")
        
        print(files_info)
        #UPDATE SIMULATION LISTING IMG DATA
        update_simulation_listing_image_data(cursor, simulation_listing_id, files_info)
        
        
    
    
def update_simulation_listing_image_data(cursor, simulation_listing_id, files_info):
    cursor.execute(
        "UPDATE simulation_listing SET simulation_image = %s WHERE id = %s",
        (json.dumps({"map": files_info}), simulation_listing_id)
    )
         
    
def upload_image(simulation_listing_id, lat, lng):
   
    static_map_content = get_static_map(lat, lng)
    jpeg_content = convert_png_to_jpeg(static_map_content)
    files = {
        'files': ('map_image.jpeg', jpeg_content, 'image/png')
    }
    form_data = {
        'hook': 'solar-panel-system-calculator',
        'registeredResource[resourceName]': 'simulation-listing',
        'registeredResource[resourceId]': str(simulation_listing_id),
        'registeredResource[type]': 'string',
        'documentInfo[visible]': 'true',
        'documentInfo[description]': 'simulation image',
        'documentInfo[maxFile]': '1',
        'documentInfo[isMain]': 'true',
    }

   
    doc_response = requests.post(document_service_url, files=files, data=form_data)

    if doc_response.status_code != 200:
        raise Exception('Error when uploading image')

    # Parse the response from the document service 
    doc_response_json = doc_response.json()
    document_info = doc_response_json.get('documentInfo', {})
    files_info = document_info.get('file', [])
    if not files_info:
       raise Exception('Error when retrieving image data')
    return files_info[0]

def convert_png_to_jpeg(png_content):
    # Open the PNG image from bytes
    image = Image.open(io.BytesIO(png_content))
    if image.mode == 'P':
        image = image.convert('RGB')
    # Convert the image to RGB mode (JPEG does not support transparency)
    if image.mode in ('RGBA', 'LA'):
        background = Image.new('RGB', image.size, (255, 255, 255))
        background.paste(image, mask=image.split()[-1])  # Paste using alpha channel as mask
        image = background
    
    # Convert the image to JPEG format
    jpeg_io = io.BytesIO()
    image.save(jpeg_io, format='JPEG')
    jpeg_io.seek(0)
    
    return jpeg_io.getvalue()


def insert_city(
    cursor, 
    import_id, 
    city_name, 
    country_name,
    provided_latitude,
    provided_longitude,
    source_row_id):
    if len(city_name) == 0 or len(country_name) == 0:
        raise Exception("Missing city or country name")
    
    lat, lng, country_iso2, google_place_id = get_lat_lng_iso2_googleplaceid(city_name, country_name)
    if ( (lat == None and provided_latitude == None)
        or (lng == None and provided_longitude == None)
        or country_iso2 == None 
        or google_place_id == None
        ):
    
        raise Exception("Invalid lat/lng/country_iso2/google_place_id")
    
    if provided_latitude != None :
        lat = provided_latitude
    if provided_longitude != None :
        lng = provided_longitude
    cursor.execute("""
        SELECT id
        FROM country where code_alpha_2 = %s
    """, (country_iso2))
    country_rows = cursor.fetchall()
    if len(country_rows) == 0:
        raise Exception("No country found")
    
    normalized_city_name = city_name.lower().replace(' ', '-')
    
    cursor.execute("""
        INSERT INTO city (name, latitude, longitude, google_place_id, country_id, source_file_name, source_row_id, import_id, normalized_name)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
    """, (city_name, lat, lng, google_place_id, country_rows[0]['id'], file_path, source_row_id, import_id, normalized_city_name))
    
    city_id = cursor.lastrowid
    return lat, lng, country_iso2, google_place_id,city_id

def insert_simulation_listing(cursor,import_id, lat, lng, city_id):
    pvgis_database = get_database(lat, lng)
    if not pvgis_database:
        raise Exception("No PVGIS DATABASE found")
    
    simulation = fetch_simulation(lat, lng, pvgis_database)
    horizon_data = fetch_horizon_data(lat, lng)
    cursor.execute(
        "INSERT INTO simulation_listing (city_id, simulation_data, simulation_input, import_id, horizon_data) VALUES (%s, %s, %s, %s, %s)",
        (city_id, json.dumps(simulation["res"]), json.dumps(simulation["input"]), import_id, horizon_data['res'])
    )
    simulation_listing_id = cursor.lastrowid
    return simulation_listing_id

def get_lat_lng_iso2_googleplaceid(*args):
   
    api_key = google_maps_api_key
    params = {
        "address": ", ".join(args),
        "key": api_key
    }
    response = requests.get(google_geocode_url, params=params)
    if response.status_code == 200:
        results = response.json().get("results")
        if results:
            for result in results: 
                if (
                    'locality' in result['types']
                    or 'administrative_area_level_4' in result['types']
                    or 'administrative_area_level_3' in result['types']
                    or 'administrative_area_level_2' in result['types']
                    or 'political' in result['types']
                    ): 
                    location = result["geometry"]["location"]
                    lat = location["lat"]
                    lng = location["lng"]
                    address_components = results[0].get("address_components", [])
                    # Find ISO2 country code
                    country_iso2 = next(
                        (comp["short_name"] for comp in address_components if "country" in comp["types"]),
                        None
                    )
                    google_place_id = result['place_id']  
                    return lat, lng, country_iso2, google_place_id
    return None, None, None, None


def fetch_simulation(lat, lon, database):
    """Fetch the simulation data for a given city."""
    
   
    params = {
        "lat": lat,
        "lon": lon,
        "usehorizon": 1,
        "raddatabase": database,
        "peakpower": 1,
        "pvtechchoice": "crystSi",
        "mountingplace": "free",
        "loss": 3,
        "optimalinclination": 1,
        "optimalangles": 1,
        "angle": 0,
        "aspect": 0,
        "inclined_axis": 0,
        "vertical_axis": 0,
        "twoaxis": 0,
        "outputformat": "json",
        "browser": 0
    }
   
    response = requests.post(simulation_url,  
        headers={"Content-Type": "application/json"},
        json=params)
    response.raise_for_status()
    return { "res":response.json(), "input": params }
     

def get_database(lat, lon):
    """Fetch the appropriate database for given lat and lon."""
     
    database = ["PVGIS-SARAH3", "PVGIS-ERA5"]
    try:
        response = requests.post(
            extent_url,
            headers={"Content-Type": "application/json"},
            json={"lat": lat, "lon": lon, "database": ",".join(database)}
        )
        response.raise_for_status()
        val = response.json()
        databases = [db for db, available in zip(database, val) if available]
        return databases[0] if databases else None
    except Exception as e:
        
        return None
    
def get_static_map(lat, lng): 
    zoom = 19
    size = "1200x300"
    map_type_id = "satellite"
    f_static_google_maps_url = static_google_maps_url.format(
        lat=lat,
        lng=lng,
        zoom=zoom,
        size=size,
        map_type_id=map_type_id,
        google_maps_api_key=google_maps_api_key
    )
    response = requests.get(f_static_google_maps_url)

    if response.status_code != 200:
        return None 
    return response.content



# TODO: WILL WORK ONLY FOR THE FIRST UPDATE (AFTER THE horizon_data COLUMN CREATION)
def bulk_update_all_horizon_data(chunk_size):
    connection = None
    try: 
        connection = pymysql.connect(
            host=settings.MYSQL_HOST,
            user=settings.MYSQL_USER,
            password=settings.MYSQL_PASSWORD,
            database=settings.MYSQL_DATABASE,
            port=settings.MYSQL_PORT, 
        )
        cursor = connection.cursor()
        cursor.execute("""
            SELECT id, simulation_input
            FROM simulation_listing 
            WHERE horizon_data IS NULL
            ORDER BY id
            LIMIT %s
        """, (chunk_size))
        rows = cursor.fetchall()
        for simulation_listing_id, simulation_input in rows:
            
            try:
                # Parse the current simulation_image
                simulation_input_data = json.loads(simulation_input)

                # Fetch horizon data
                horizon_data = fetch_horizon_data(simulation_input_data['lat'], simulation_input_data['lon'])

            
                # Update the simulation_image column with the new format
                cursor.execute(
                    "UPDATE simulation_listing SET horizon_data = %s WHERE id = %s",
                    (horizon_data['res'], simulation_listing_id)
                )
                connection.commit()

                print(f"Updated simulation_image for simulation_listing_id {simulation_listing_id}")

            except Exception as e:
                print(f"Error parsing simulation_image for simulation_listing_id {simulation_listing_id}: {e}")
                continue
            
    except Exception as e:
        connection.rollback()
        raise e 
    finally:
        if connection is not None:
            connection.close()

def fetch_horizon_data(lat, lon):
    """Fetch the simulation horizon data"""
    params = {
        "lat": lat,
        "lon": lon,
        "browser": 0,
        "outputformat": "basic"
    }
    try:
        response = requests.post(horizon_url,  
            headers={"Content-Type": "application/json"},
            json=params)
        response.raise_for_status()
        return { "res":response.json(), "input": params }
    except Exception as e:
        print(f"Error fetching simulation for lat={lat}, lon={lon}: {e}")
        return None


# Function to get timezone offset for a given country code
def get_timezone_offset(country_code):
    try:
        timezones = pytz.country_timezones(country_code)
        if len(timezones) == 0: 
            raise Exception('No timezone for country '+country_code)
        tz = pytz.timezone(timezones[0])
        local_time = datetime.now(tz)
        offset = local_time.strftime('%z')
        return offset[:3] + ':' + offset[3:]
    except Exception as e:
        print(f"Error fetching timezone offset for {country_code}: {str(e)}")
        return None
    
def update_country_timezone_offset():
    # Connect to the MySQL database
    conn = None 
    try:
        conn = pymysql.connect(
                host=settings.MYSQL_HOST,
                user=settings.MYSQL_USER,
                password=settings.MYSQL_PASSWORD,
                database=settings.MYSQL_DATABASE,
                port=settings.MYSQL_PORT, 
            )
        cursor = conn.cursor()

        # Get all the country codes from the country table
        cursor.execute("SELECT code_alpha_2 FROM country")
        country_codes = cursor.fetchall()

        # Iterate over the country codes and update the timezone_offset
        for country_code in country_codes:
            code = country_code[0]  # Get the code_alpha_2 (e.g., 'US', 'IN', 'GB')
            
            # Fetch timezone offset using the API
            timezone_offset = get_timezone_offset(code)
            
            if timezone_offset is not None:
                # Update the timezone_offset in the database
                update_query = "UPDATE country SET timezone_offset = %s WHERE code_alpha_2 = %s"
                cursor.execute(update_query, (timezone_offset, code))
                print(f"Updated timezone_offset for {code}: {timezone_offset}")
            

        # manual cases:
        manual_data = [
            ('XK', '+01:00'),
            ('BV', '+01:00'),
            ('HM', '+05:00'),
        ]
        for country_code, offset in manual_data:
            update_query = "UPDATE country SET timezone_offset = %s WHERE code_alpha_2 = %s"
            cursor.execute(update_query, (offset, country_code))
            print(f"Updated timezone_offset for {country_code}: {offset}")

        # Commit the changes and close the connection
        conn.commit()
    except Exception as e:
        if conn:
            conn.rollback() 
        raise e
    finally:
        if conn:
            conn.close()
    

def update_country_default_customer_data(db):
    current_working_directory = os.getcwd()
     
    file_path = "city-management/countries-default-user-name.xlsx"
    full_file_path = os.path.join(current_working_directory, file_path) 
    df = pd.read_excel(full_file_path, usecols=[0, 4, 5]) 
     
    conn = None
    
    try:
        conn = pymysql.connect(
            host=settings.MYSQL_HOST,
            user=settings.MYSQL_USER,
            password=settings.MYSQL_PASSWORD,
            database=settings.MYSQL_DATABASE,
            port=settings.MYSQL_PORT, 
        )
        cursor = conn.cursor()
        for index, row in df.iterrows():
            sender_email = row[2] if row[2] else "<EMAIL>"
            default_customer_data = {
                "first_name": row[1]
            }
            email_sender_json = {
                "email": sender_email, 
                "name": sender_email.split('@')[0].capitalize()
            }
            update_query = """
                    UPDATE country
                    SET default_customer_json = %s, email_sender_json = %s
                    WHERE id = %s
                """
            cursor.execute(update_query, (
                    json.dumps(default_customer_data), 
                    json.dumps(email_sender_json), 
                    int(row[0])
                )
            )
        conn.commit() 
    except Exception as e:
        if conn:
            conn.rollback() 
        raise e
    finally:
        if conn:
            conn.close()
   
def update_customer_timezone():
    conn = None
    
    try:
        conn = pymysql.connect(
            host=settings.MYSQL_HOST,
            user=settings.MYSQL_USER,
            password=settings.MYSQL_PASSWORD,
            database=settings.MYSQL_DATABASE,
            port=settings.MYSQL_PORT, 
        )
        cursor = conn.cursor()
        cursor.execute("SELECT cu.id,cu.city,COALESCE(co.name, cu.country) FROM customer cu left join country co on cu.country_id = co.id  where cu.timezone_offset is null")
        customers = cursor.fetchall()
        updated_rows = 0
        for customer_id, city_name, country_name in customers:
            timezone_offset = fetch_timezone_offset(country_name, city_name)
            if timezone_offset  : 
                cursor.execute('UPDATE customer set timezone_offset = %s where id = %s',(timezone_offset, customer_id)) 
                conn.commit() 
                updated_rows+=1
            print(f"UPDATED customer timezone offset for: {country_name}, {city_name} : {timezone_offset}" )
        print("Total UPDATED customer timezone offset count: "+str(updated_rows))
        conn.commit() 
    except Exception as e:
        if conn:
            conn.rollback() 
        raise e
    finally:
        if conn:
            conn.close()
            

def fetch_timezone_offset(city_name, country_name):
    
    try:
        lat, lng, country_iso2, google_place_id = get_lat_lng_iso2_googleplaceid(city_name, country_name)
        if lat is None:
            return None
        timezone_params = {
            "location": f'{lat},{lng}',
            "timestamp":datetime.now().timestamp(),
            "key": google_maps_api_key
        }
        response = requests.post(timezone_url,  
            headers={"Content-Type": "application/json"},
            params=timezone_params)
        response.raise_for_status()
        tz = pytz.timezone(response.json()["timeZoneId"])
        local_time = datetime.now(tz)
        offset = local_time.strftime('%z')
        return offset[:3] + ':' + offset[3:]
    except Exception as e:
        print(f"Error fetching timezone for: {city_name}, {country_name}") 
        return None