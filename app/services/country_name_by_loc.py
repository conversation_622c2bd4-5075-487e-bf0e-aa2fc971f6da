import requests
from app.crud.ip_location_service import google_geocode_by_loc
def get_country_name_from_coordinates(latitude: float, longitude: float) -> str:
    # response = requests.get(f"https://api.bigdatacloud.net/data/reverse-geocode-client?latitude={latitude}&longitude={longitude}&localityLanguage=en")
    data = google_geocode_by_loc(latitude, longitude)
    print(f"Country find : {data}") 
    return data