def calculate_consumption_sum(data: dict) -> dict:
    if not data:
        return {"summer": 0.0, "winter": 0.0}

    summer_sum = sum(hour.get("summer", 0.0) for hour in data.values())
    winter_sum = sum(hour.get("winter", 0.0) for hour in data.values())

    return {"summer": round(summer_sum, 2), "winter": round(winter_sum, 2)}



def calculate_consumption_percentage(residential_consumption_json: dict, residential_consumption_sum: dict) -> dict:
    if not residential_consumption_sum or not residential_consumption_json:
        return {hour: {"summer": 0.0, "winter": 0.0} for hour in residential_consumption_json}

    summer_total = residential_consumption_sum.get("summer", 0.0)
    winter_total = residential_consumption_sum.get("winter", 0.0)

    consumption_percentage_json = {}
    for hour, consumption in residential_consumption_json.items():
        summer_val = consumption.get("summer", 0.0)
        winter_val = consumption.get("winter", 0.0)

        summer_percentage = (summer_val / summer_total * 100) if summer_total != 0 else 0.0
        winter_percentage = (winter_val / winter_total * 100) if winter_total != 0 else 0.0

        consumption_percentage_json[hour] = {
            "summer": round(summer_percentage, 2),
            "winter": round(winter_percentage, 2)
        }

    return consumption_percentage_json

