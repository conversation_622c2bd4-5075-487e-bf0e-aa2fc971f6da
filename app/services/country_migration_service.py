import io
from typing import List
from PIL import Image
import os
import traceback
import numpy as np
import pymysql
import pytz
import requests
import json
import pandas as pd
from sqlalchemy.orm import Session
from app.config.settings import get_settings
from datetime import datetime
import pycountry
from sqlalchemy import text
from app.models.ce_campaign_country_data import CeCampaignCountryData
from app.models.country import Country
from app.models.customer_email_campaign import CustomerEmailCampaign
from app.models.customer_email_sender import CustomerEmailSender
from app.models.customer_email_task import CustomerEmailTask
from app.models.ce_sender_email_campaign_country import CeSenderEmailCampaignCountry
from app.schemas.customer_email_sender import CustomerEmailSenderCreate
from app.schemas.ce_sender_email_campaign_country import CeSenderEmailCampaignCountryCreate
from app.services.city_migration_service import get_lat_lng_iso2_googleplaceid, read_excel_rows
settings = get_settings()

 
senders_file_path = f"{settings.BASE_DIR}/data/senders.xlsx"

country_codes =   { 
        "France": ['fr'],
        "United States": ['us'],
        "Germany": ['de'],
        "Spain": ['es'],
        "India": ['in'],
        "Italy": ['it'], 
        "Netherlands": ['nl'],
        "Portugal": ["pt"],
        "Romania": ["ro"],
        "China": ["cn"],
        "Russia": ["ru"],
        "Arabic countries":["bd","dz", "bh", "km", "dj", "eg", "iq", "jo", "kw", "lb", "ly", "mr", "ma", "om", "ps", "qa", "sa", "so", "sd", "sy", "tn", "ae", "ye"]
    }
 

def get_col_dataframe(main_data, col_id):
    results = None
    if len(main_data.columns) > col_id:
        results = main_data.iloc[:, col_id]
    else: 
        results = pd.DataFrame({f'{col_id}': [np.nan] * len(main_data)})
    return results
    
def integrate_senders(db: Session, email_campaign_key: str ):
     

        sheet_name = 0  
        start_row = 0 
        data = read_excel_rows(senders_file_path, sheet_name, start_row, header=0)
       
        if data is   None:
            return 
        data = data.fillna("")
        
        # Extract the necessary columns
        country_names = get_col_dataframe(data, 1) 
        sender_names = get_col_dataframe(data, 2) 
        sender_emails = get_col_dataframe(data, 3) 
        
        email_campaign= db.query(CustomerEmailCampaign).filter(CustomerEmailCampaign.key == email_campaign_key).first()
       
         
       
        for country_name, sender_name, sender_email in zip(country_names,sender_names,sender_emails):
            country_name = str(country_name).strip()
            cur_country_codes = None
            if country_name in country_codes:
                cur_country_codes = country_codes[country_name]
            else:
                (lat, lng, country_iso2, google_place_id) = get_lat_lng_iso2_googleplaceid(country_name)
                cur_country_codes = [country_iso2]
                 
            inserted_sender = db.query(CustomerEmailSender).filter(CustomerEmailSender.email == sender_email).first()
            
            #insert sender if doesnt exist
            if not inserted_sender:
                sender = CustomerEmailSender(name=sender_name,email=sender_email)
                db.add(sender)
                db.flush()
                inserted_sender = db.query(CustomerEmailSender).filter(CustomerEmailSender.email == sender_email).first()
            
            countries = db.query(Country).filter(Country.code_alpha_2.in_(cur_country_codes)).all()
            
            for country in countries: 
                senderTaskCountry = CeSenderEmailCampaignCountry(
                    customer_email_sender_id=inserted_sender.id, 
                    country_id= country.id,
                    customer_email_campaign_id=email_campaign.id
                    )
                db.add(senderTaskCountry)
            db.commit()
             


def integrate_senders_and_default_data(db: Session, email_campaign_key:str ):
     
    try:
        sheet_name = 0  
        start_row = 0 
        data = read_excel_rows(senders_file_path, sheet_name, start_row, header=[0,1])
       
        if data is   None:
            return 
        data = data.fillna("")
        
        # Extract the necessary columns
        country_names = get_col_dataframe(data, 1) 
        language_names = get_col_dataframe(data, 7) 
        sender_emails = get_col_dataframe(data, 8) 
        default_customer_names = get_col_dataframe(data, 9)
        
        email_campaign= db.query(CustomerEmailCampaign).filter(CustomerEmailCampaign.key == email_campaign_key).first()

        db.execute(text('DELETE FROM ce_campaign_country_data WHERE customer_email_campaign_id = :campaign_id'), {"campaign_id": email_campaign.id})
        db.execute(text('DELETE FROM ce_sender_email_campaign_country WHERE customer_email_campaign_id = :campaign_id'), {"campaign_id": email_campaign.id})

        success_count = 0
        skipped = []
        country_codes_by_name = {
            "Ireland": "IE"
        }
        for country_name, language_name, sender_email, default_customer_name in zip(country_names,language_names,sender_emails,default_customer_names):
           
            country_name = country_name.strip() if country_name else None
            language_name = language_name.strip() if language_name else None
            sender_email = sender_email.strip() if sender_email else None
            default_customer_name = default_customer_name.strip() if default_customer_name else None
            
            cur_row_dict = {
                    "country_name": country_name,
                    "language_name":language_name,
                    "sender_email": sender_email,
                    "default_customer_name": default_customer_name
                }
            
            if not sender_email :
                skipped.append({"data": cur_row_dict, "reason": "No email found"})
                continue
            
            cur_country_code = None
            if country_name in country_codes_by_name:
                cur_country_code = country_codes_by_name[country_name]
            else:
                (lat, lng, country_iso2, google_place_id) = get_lat_lng_iso2_googleplaceid(country_name)
                cur_country_code = country_iso2
                 
            inserted_sender = db.query(CustomerEmailSender).filter(CustomerEmailSender.email == sender_email).first()
            
            #insert sender if doesnt exist
            if not inserted_sender:
                sender_name = sender_email.split('@')[0].capitalize()
                sender = CustomerEmailSender(name=sender_name,email=sender_email)
                db.add(sender)
                db.flush()
                inserted_sender = db.query(CustomerEmailSender).filter(CustomerEmailSender.email == sender_email).first()
             
            country = db.query(Country).filter(Country.code_alpha_2 == cur_country_code).first()
            
            if not country :
                skipped.append({"data": cur_row_dict, "reason": "No country found"})
                continue
            
            # adding relation sender-task-country
            senderTaskCountry = CeSenderEmailCampaignCountry(
                        customer_email_sender_id=inserted_sender.id, 
                        country_id= country.id,
                        customer_email_campaign_id=email_campaign.id
                        )
            db.add(senderTaskCountry)
            
            language_iso_2 = get_language_iso_2_by_name(language_name)
            if not country :
                skipped.append({"data": cur_row_dict, "reason": "No country found"})
                continue
            # adding  campaign-country-data
            campaign_country_data = CeCampaignCountryData ( 
                country_id= country.id,
                customer_email_campaign_id=email_campaign.id,
                default_customer_json = {"first_name": default_customer_name},
                default_language = language_iso_2
            )
            db.add(campaign_country_data)
            
            
            success_count += 1
        
        db.commit()
        return {
            "success_count":success_count,
            "skipped":skipped,
        }
    except Exception as e:
        db.rollback()
        raise e

def get_language_iso_2_by_name(language_name):
    try:
        return pycountry.languages.lookup(language_name).alpha_2
    except Exception as e:
        return None