from locale import currency
import traceback
import uuid
from sqlalchemy import func, select
from sqlalchemy.orm import Session
import pandas as pd 
from app import crud
from app.config.settings import get_settings
from app.crud.crud_customer_migration_error import  customer_migration_error
from app.models.customer import Customer
from app.models.customer_migration_error import CustomerMigrationError
from app.schemas.customer_migration_error import CustomerMigrationErrorCreate
from app.services.city_migration_service import read_excel_rows
settings = get_settings()

installer_file_name = settings.INSTALLERS_FILE_NAME
installer_file_path = f"{settings.BASE_DIR}/data/{installer_file_name}"  

def is_readable_and_non_empty_row(row):
    return  ( 
                not row.isnull().all()
                and not row.apply(lambda x: pd.isna(x) or x is pd.NaT).all()
            )
def get_last_inserted_customer_file_row_id(db: Session, file_path: str, source_has_header = True):
    stmt = select([func.max(Customer.source_row_id)]).where(Customer.source_file_name == file_path)
    (source_row_id,) = db.execute(stmt).first()
    if source_row_id != None:
        return source_row_id
    if source_has_header:
        return 0
    return  -1
    
def integrate_installers_by_chunk(db: Session, chunk_size: int):
    header_row_id = 0
    sheet_name = 0  
    
    source_has_header = (header_row_id != None)
    last_inserted_file_row_id = get_last_inserted_customer_file_row_id(db, installer_file_name, source_has_header)
    start_row = last_inserted_file_row_id + 1   
    custom_converters = {
        "Country Code 2": str,
        "Region": str,
        "Email": str,
        "Address": str,
        "Post Code": str,
        "Area 1": str,
        "Phone": str,
        "Company": str,
        "Website": str,
        "Id": int,
    }
    df = read_excel_rows(installer_file_path, sheet_name, start_row, chunk_size, header=header_row_id, custom_converters=custom_converters)
    
    import_id = str(uuid.uuid4())
    cur_row_id = start_row
    country_codes_by_name = {
        "Ireland": "IE"
    }
    
    installer_email_task = crud.customer_email_task.load_by_key(db, "installer_invitation")
    
    for index, row in df.iterrows():
        # print(row)
        if not is_readable_and_non_empty_row(row):
            continue
        try:
            crud.bulk_upload.bulk_upload_installer(
                            db=db,
                            import_id=import_id,
                            source_file_name=installer_file_name,
                            source_row_id=cur_row_id, 
                            row=row, 
                            user_id=None,
                            country_codes_by_name=country_codes_by_name,
                            installer_email_campaign = installer_email_task.customer_email_campaign
                        )
            # Delete customer migration error if any
            db.query(CustomerMigrationError).filter(
                CustomerMigrationError.email == row["Email"],
                CustomerMigrationError.company == row["Company"]
            ).delete()
            
            db.commit()
        except Exception as e:
            db.rollback()
            error_traceback = traceback.format_exc()
            customer_migration_error.create(db=db, obj_in=CustomerMigrationErrorCreate(
                email=row["Email"],
                company=row["Company"],
                import_id=import_id,
                source_row_id= cur_row_id,
                error=error_traceback,   
            ))
        finally:
            cur_row_id += 1