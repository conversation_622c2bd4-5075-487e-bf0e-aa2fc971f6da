import json
import requests
from app.config.settings import get_settings
from typing import Dict, Any, List
from app import crud
from fastapi.encoders import jsonable_encoder

settings = get_settings()
# send notification
def send_notif(event_code: str, users:List[Any], mailLanguage: str = "en", roles: List[Any] = [], isCmsTemplate = False):
    try:
        notif_data = json.dumps(
            {
                "eventCode": event_code, 
                "recipientUsers": users, 
                "recipientRoles": roles, 
                "mailLanguage": mailLanguage,
                "sentEmailCms": isCmsTemplate
                }
        )
        print("notif_data----------------------------------")
        print(notif_data)
        request = requests.post(
            settings.NOTIFICATION_API+"/notify/v2",
            data=notif_data,
            headers={"Content-Type": "application/json"},
        )

        request.raise_for_status()

        if request.status_code == 200:
            print("POST request was successful!")
        else:
            print("POST request failed with status code:", request.status_code)
        return request
    except Exception as e:
        print(f"Request failed: {e}")
        return
