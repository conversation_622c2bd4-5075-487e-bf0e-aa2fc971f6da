import requests
from app.config.settings import get_settings
import json
from urllib.parse import quote
import re
from sqlalchemy.orm import Session
from app.models.customer_email_task import CustomerEmailTask
from fastapi import HTTPException
from app.services.brevo.brevo import (
    update_template
)
from app.services.brevo.utils.brevo import (
    _first_row_with_id
)
from fastapi.responses import StreamingResponse, PlainTextResponse

settings = get_settings()

id_to_lang = {
    1: "en", 2: "fr", 3: "de", 4: "zh", 5: "es", 6: "hi", 7: "ur", 8: "ar", 9: "pt",
    10: "nl", 11: "ru", 13: "ro", 14: "it", 15: "bn", 16: "ja", 17: "id", 18: "tr",
    19: "ko", 20: "af", 21: "sq", 22: "am", 23: "hy", 24: "az", 25: "eu", 26: "be",
    27: "bs", 28: "bg", 29: "ca", 30: "ceb", 31: "co", 32: "hr", 33: "cs", 34: "da",
    35: "eo", 36: "et", 37: "fi", 38: "fy", 39: "gl", 40: "ka", 41: "el", 42: "gu",
    43: "ht", 44: "ha", 45: "haw", 46: "iw", 47: "hmn", 48: "hu", 49: "is", 50: "ig",
    51: "ga", 52: "jw", 53: "kn", 54: "kk", 55: "km", 56: "ku", 57: "ky", 58: "lo",
    59: "la", 60: "lv", 61: "lt", 62: "lb", 63: "mk", 64: "mg", 65: "ms", 66: "ml",
    67: "mt", 68: "mi", 69: "mr", 70: "mn", 71: "my", 72: "ne", 73: "no", 74: "ny",
    75: "ps", 76: "fa", 77: "pl", 78: "pa", 79: "sm", 80: "gd", 81: "sr", 82: "st",
    83: "sn", 84: "sd", 85: "si", 86: "sk", 87: "sl", 88: "so", 89: "su", 90: "sw",
    91: "sv", 92: "tl", 93: "tg", 94: "ta", 95: "te", 96: "th", 97: "uk", 98: "uz",
    99: "vi", 100: "cy", 101: "xh", 102: "yi", 103: "yo", 104: "zu"
}

def regroupe_generate_template(cms_key: str):
    if not cms_key or not isinstance(cms_key, str) or not cms_key.strip():
        raise HTTPException(status_code=400, detail="Invalid or empty cms_key")

    dataCms = get_data_cms_campaign(cms_key)
    if not isinstance(dataCms, dict) or not dataCms.get("id") or not dataCms.get("content"):
        raise HTTPException(status_code=502, detail="CMS returned empty/invalid response, empty data")
    
    cms_id = dataCms["id"]
    dataTs = get_ts_data_campaign(cms_id, column_name="content")
    first_ts = _first_row_with_id(dataTs)
    if not first_ts:
        raise HTTPException(status_code=502, detail="TS returned empty/invalid response, empty data")

    ts_id = first_ts["id"]
    allLanguage = get_all_langue_template(ts_id)
    if not allLanguage:
        raise HTTPException(status_code=502, detail="Language templates are empty")

    template = generate_the_template(dataCms.get("content"), allLanguage)
    if not isinstance(template, str) or not template.strip():
        return PlainTextResponse("", status_code=204)
    
     # try:
    #     # WE CAN UPDATE DIRECTLY THE TEMPLATE IN BREVO, PARAMS THE TEMPLATE ID
    #     # EXAMPLE : 21
    #     update_template(21, html_content=template, is_active=True)
    # except Exception as exc:
    #     print(f"[WARNING] update_template failed: {type(exc).__name__}: {exc}")
    
    return template

def generate_the_template(contentEn, data, default_lang="en"):
    template = f"""
        <!-- EN LANGUAGE -->
        {{% if contact.LANGUAGE == 'en' %}}
        <html lang="{{{{ contact.LANGUAGE }}}}">
        <body>
            {transform_template(contentEn)}
        </body>
        </html>
        {{% endif %}}
        <!-- EN LANGUAGE -->
    """

    for item in data:
        lang_code = id_to_lang.get(item["languageId"], default_lang)
        content = item.get("translation") or  item.get("autoTranslatedValue") or ""

        block = f"""
            <!-- {lang_code.upper()} LANGUAGE -->
            {{% if contact.LANGUAGE == '{lang_code}' %}}
            <html lang="{{{{ contact.LANGUAGE }}}}">
            <body>
                {transform_template(content)}
            </body>
            </html>
            {{% endif %}}
            <!-- {lang_code.upper()} LANGUAGE -->
            
        """
        template += block + "\n\n"
    
    return template
def generate_subject_customer(data, customer_language):
    subject = ""
    for item in data:
        lang_code = id_to_lang.get(item["languageId"], "en")
        if customer_language == lang_code:
            subject = item.get("translation") or  item.get("autoTranslatedValue") or ""
            
    return subject

def get_subject_campaign(
    db: Session,
    campaign_task, 
    language,
    current_task, 
):
    # # PRO MIX : 8 to 19
    # pro_mix_task = [8,9,10,11,12,13,14,15,16,17,18,19]
    # pro_mix_task.length = 12
    # # SPIN 1 : 20 to 27
    # spin_1_task = [20,21,22,23,24,25,26,27]
    # spin_1_task.length = 8
    # # SPIN 2 PROJET : 28 to 35
    # spin_2_projet_task = [28,29,30,31,32,33,34,35]
    # spin_2_projet_task.length = 8
    # # SPIN 3 CONTROL : 36 to 43
    # spin_3_control_task = [36,37,38,39,40,41,42,43]
    # spin_3_control_task.length = 8
    subject = ""
    try:
        if current_task == -1:
            valid_task_in_idb_pvgis =  campaign_task
            email_task = db.query(CustomerEmailTask).filter(CustomerEmailTask.id == campaign_task).first()
        else:
            # if current_task == 0:
            #     valid_task_in_idb_pvgis = campaign_task + 1
            # else:
            valid_task_in_idb_pvgis = campaign_task + current_task + 1
            email_task = db.query(CustomerEmailTask).filter(CustomerEmailTask.id == valid_task_in_idb_pvgis).first()
        # print("------------------------------")
        # print(f"campaign_task: {campaign_task}")
        # print(f"current_task: {current_task}")
        # print(f"valid_task_in_idb_pvgis: {valid_task_in_idb_pvgis}")
        # print("------------------------------")
        if not email_task or not email_task.cms_key:
            raise HTTPException(status_code=404, detail=f"CustomerEmailTask not found")
        
        dataCms = get_data_cms_campaign(email_task.cms_key)
        if not isinstance(dataCms, dict) or not dataCms.get("id") or not dataCms.get("title"):
            raise HTTPException(status_code=502, detail="CMS returned empty/invalid response")
            
        cms_id = dataCms.get("id")
        dataTs = get_ts_data_campaign(cms_id, column_name="title")
        first_ts = _first_row_with_id(dataTs)
        if not first_ts:
            raise HTTPException(status_code=502, detail="TS returned empty/invalid response")
        
        ts_id = first_ts["id"]
        allLanguage = get_all_langue_template(ts_id)
        if not allLanguage:
            raise HTTPException(status_code=502, detail="Language templates are empty")
        
        subject = generate_subject_customer(allLanguage, language)
        if not subject:
            subject = dataCms.get("title")
    except Exception:
        return subject
    
    return subject

def transform_template(html: str) -> str:
    replacements = {
        r"<%=\s*templateVars\.firstname\s*%>\s*<%=\s*templateVars\.lastname\s*%>": "{{ contact.FULL_NAME | default : ''}}",
        r"<%=\s*templateVars\.pvgis24ExtranetUrl\s*%>": "{{ contact.PVGIS24_EXTRANETURL | default : '#'}}",
        r"<%=\s*templateVars\.homePageUrl\s*%>": "{{ contact.HOME_PAGE_URL| default : '#'}}",
        r"<%=\s*templateVars\.year\s*%>": "{{ contact.YEAR}}",
        r"<%=\s*templateVars\.pvgis_ui_url\s*%>": "{{ contact.PVGIS_UI_URL | default : '#'}}",
        r"<%=\s*templateVars\.unsubscribeUrl\s*%>": "{{ contact.UNSUBSCRIBEURL | default : '#'}}",
        r"<%=\s*templateVars\.pvgis24SubscriptionUrl\s*%>": "{{ contact.PVGIS24_SUBSCRIPTIONURL | default : '#'}}",
        r"<%=\s*templateVars\.pvgis24PremiumSubscriptionUrl\s*%>": "{{ contact.PVGIS24_PREMIUM_SUBSCRIPTIONURL | default : '#'}}",
        r"<%=\s*templateVars\.subscribeUrl\s*%>": "{{ contact.SUBSCRIBE_URL | default : '#'}}",
        r"<%=\s*templateVars\.pvgis24Url\s*%>": "{{ contact.PVGIS24_URL | default : '#'}}",
        r"<%=\s*templateVars\.testomony_link\s*%>": "{{ contact.TESTOMONY_LINK | default : '#'}}",
        r"<%=\s*templateVars\.pvgis_offer_link\s*%>": "{{ contact.PVGIS_OFFER_LINK | default : '#'}}",
        r"<%=\s*templateVars\.reactivation_url\s*%>": "{{ contact.PVGIS24_EXTRANETURL | default : '#'}}",
    }

    for pattern, replacement in replacements.items():
        html = re.sub(pattern, replacement, html)

    return html


def get_data_cms_campaign(key :str):
    url = f"{settings.CMS_URL}/content-management-system/key/{key}"
    headers = {"accept": "*/*"}
    response = requests.get(url, headers=headers)

    if response.status_code == 200:
        try:
            return response.json() 
        except ValueError:
            return {"data": response.text} 
    else:
        return {
            "error": f"Failed with status code {response.status_code}",
            "details": response.text
        }
        
def get_ts_data_campaign(row_id: int, column_name: str = "content"):
    params = {
        "limit": 1000,
        "offset": 0,
        "where": json.dumps({
            "and": {
                "rowId": {"operator": "==", "value": row_id},
                "columnName": {"operator": "==", "value": column_name}
            }
        }),
        "groupBy": "[]",
        "type": "false"
    }

    url = (
        f"{settings.TRANSLATION_URL}/resources/V4"
        f"?limit={params['limit']}"
        f"&offset={params['offset']}"
        f"&where={quote(params['where'])}"
        f"&groupBy={quote(params['groupBy'])}"
        f"&type={params['type']}"
    )

    headers = {"accept": "*/*"}
    try:
        response = requests.get(url, headers=headers, timeout=15)
    except requests.RequestException as e:
        return {"error": "Request failed", "details": str(e)}, 502

    try:
        payload = response.json()
    except ValueError:
        payload = {"raw": response.text}

    if response.ok:
        return payload
    else:
        return {"error": "Upstream error", "status": response.status_code, "payload": payload}, response.status_code
    
    
import requests

def get_all_langue_template(resource_id):
    url = f"{settings.TRANSLATION_URL}/translation/V4"
    params = {
        "limit": 1000,
        "offset": 0,
        "where": f'{{"resourceId":{{"operator":"==","value":{resource_id}}}}}',
        "groupBy": "[]",
        "type": "false"
    }
    headers = {
        "accept": "*/*"
    }

    response = requests.get(url, headers=headers, params=params)

    if response.status_code == 200:
        try:
            return response.json() 
        except ValueError:
            return {"data": response.text} 
    else:
        return {
            "error": f"Failed with status code {response.status_code}",
            "details": response.text
        }