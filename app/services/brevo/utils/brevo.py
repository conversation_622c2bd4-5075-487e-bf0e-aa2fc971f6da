
from sqlalchemy.orm import Session
from app.models.customer_email_item import CustomerEmailItem
from app.crud import crud_customer_email_item
from app.schemas.customer_email_item import CustomerEmailItemCreate
import re
import quopri
import json
from typing import Any, Dict, Iterable, Optional
from datetime import datetime, timedelta, timezone
try:
    from zoneinfo import ZoneInfo
except ImportError:
    ZoneInfo = None

def decode_x_mailin_custom(x_mailin_custom: str):
    """
    Decode Brevo's X-Mailin-Custom header, which comes quoted-printable encoded.
    """
    if not x_mailin_custom:
        return {}

    parts = re.findall(r'=\?UTF-8\?Q\?(.*?)\?=', x_mailin_custom)
    if not parts:
        return {}

    decoded_parts = [quopri.decodestring(part).decode('utf-8') for part in parts]
    decoded_str = ''.join(decoded_parts)

    decoded_str = decoded_str.replace('_', ' ')

    try:
        return json.loads(decoded_str)
    except json.JSONDecodeError:
        print("Unable to parse metadata JSON:", decoded_str)
        return {}


def _as_aware(dt: datetime, default_tz: str = "UTC") -> datetime:
    """
    Make a datetime timezone-aware. If naive, assume it's in UTC (or default_tz).
    """
    if dt is None:
        return None
    if dt.tzinfo is not None:
        return dt
    tz = timezone.utc if (ZoneInfo is None) else ZoneInfo(default_tz)
    return dt.replace(tzinfo=tz)

def should_send_next_email(current_task: int, last_sent_at: datetime, *,
                           now: datetime,
                           rules: dict[int, timedelta]) -> tuple[bool, int]:
    """
    Decide if we should send the next email now, and return (eligible, next_email_number).
    Rules map NEXT email number -> required waiting timedelta since last_sent_at.
    - If current_task in (-1, None): next_email_number = 0 (first email). Eligible immediately if rules[0] == 0.
    - If last_sent_at is None and next_email_number > 0: not eligible (we don't have a last timestamp).
    """
    next_email_number = 0 if current_task in (-1, None) else (current_task + 1)
    required_delay = rules.get(next_email_number, timedelta(0))

    if last_sent_at is None:
        return (True, next_email_number)
    
    diff = now - last_sent_at
    # print("--------------------------------")
    # print("diff ", diff)
    # print("required_delay ", required_delay)
    # print("--------------------------------")
    return (diff >= required_delay, next_email_number)


def _first_row_with_id(rows: Any) -> Optional[Dict[str, Any]]:
    """Return the first dict in rows that has a non-empty 'id' key."""
    if not isinstance(rows, Iterable):
        return None
    for r in rows or []:
        if isinstance(r, dict) and r.get("id"):
            return r
    return None