import os
import json
import sib_api_v3_sdk
from sib_api_v3_sdk.rest import ApiException
import time
from typing import Optional
import requests

API_KEY = os.getenv("BREVO_API_KEY")

configuration = sib_api_v3_sdk.Configuration()
configuration.api_key['api-key'] = API_KEY

email_api = sib_api_v3_sdk.TransactionalEmailsApi(sib_api_v3_sdk.ApiClient(configuration))
contacts_api = sib_api_v3_sdk.ContactsApi(sib_api_v3_sdk.ApiClient(configuration))

def send_email_with_sender(template_id: int, to_email: str, sender: dict):
    """
    Envoie un email avec un template Brevo en forçant l'expéditeur.
    
    :param template_id: ID du template Brevo
    :param to_email: Adresse du destinataire
    :param sender: dict {"name": "...", "email": "..."} 
    """
    
    email_data = {
        "to": [{"email": to_email}],
        "template_id": template_id,
        "sender": sender,
        "reply_to": {
            "email": sender["email"],
            "name": sender.get("name", "")
        }
    }

    email = sib_api_v3_sdk.SendSmtpEmail(**email_data)
    
    try:
        response = email_api.send_transac_email(email)
        print(f"Email envoyé à {to_email} avec le template {template_id}")
        return response
    except ApiException as e:
        raise Exception(f"Brevo API Error (send_email_simple): {e}")


def send_email(to_email: str, template_id: int, params: dict, metadata: dict = None):
    """
    Send an email via Brevo with optional metadata.
    """
    email_data = {
        "to": [{"email": to_email}],
        "template_id": template_id,
        "params": params
    }

    if metadata:
        email_data["headers"] = {"X-Mailin-custom": json.dumps(metadata)}

    email = sib_api_v3_sdk.SendSmtpEmail(**email_data)

    try:
        response = email_api.send_transac_email(email)
        return response
    except ApiException as e:
        raise Exception(f"Brevo API Error (send_email): {e}")
    
def send_custom_email(
    to_email: str,
    subject: str,
    html_content: str,
    metadata: dict = None
):
    """
    Send an email via Brevo with custom HTML content.
    No Brevo template required.
    """
    email_data = {
        "to": [{"email": to_email}],
        "subject": subject,
        "html_content": html_content,
        "sender": {
            "name": "PVGIS Support",  
            "email": "<EMAIL>"
        }
    }

    if metadata:
        email_data["headers"] = {"X-Mailin-Custom": json.dumps(metadata)}

    email = sib_api_v3_sdk.SendSmtpEmail(**email_data)

    try:
        response = email_api.send_transac_email(email)
        return response
    except ApiException as e:
        raise Exception(f"Brevo API Error (send_custom_email): {e}")



def get_contact(email: str):
    """
    Retrieve full Brevo contact info by email.
    """
    try:
        contact = contacts_api.get_contact_info(email)
        return contact.to_dict()
    except ApiException as e:
        print(f"Brevo API Error (get_contact): {e}")
        
def get_contact_by_list(email: str, list_id: int = None):
    """
    Retrieve full Brevo contact info by email.
    If list_id is provided, also check if contact belongs to that list.
    """
    try:
        contact = contacts_api.get_contact_info(email).to_dict()
        
        if list_id is not None:
            in_list = list_id in contact.get("list_ids", [])
            contact["in_list"] = in_list  

        return contact
    except ApiException as e:
        print(f"Brevo API Error (get_contact): {e}")



def import_contacts(list_id: int, data: list):
    """
    Import new ones.
    """
    try:
        import_req = sib_api_v3_sdk.RequestContactImport(
            list_ids=[list_id],
            update_existing_contacts=True,
            json_body=data
        )
        import_resp = contacts_api.import_contacts(import_req)
        print(f"Import initiated for {len(data)} contacts. Response:", import_resp)

        return import_resp

    except ApiException as e:
        raise Exception(f"Brevo API Error (reset_and_import_contacts): {e}")

def reset_and_import_contacts(list_id: int, data: list):
    """
    Delete all contacts from a list, then import new ones.
    """
    try:
        remove_req = sib_api_v3_sdk.RemoveContactFromList(all=True)
        try:
            resp = contacts_api.remove_contact_from_list(list_id, remove_req)
            print(f"List {list_id} cleared. Response:", resp)
        except ApiException as e:
            if "Contacts already removed" in str(e.body):
                print(f"List {list_id} already empty, skipping clear step.")
            else:
                raise  
        time.sleep(5)
        import_req = sib_api_v3_sdk.RequestContactImport(
            list_ids=[list_id],
            update_existing_contacts=True,
            json_body=data
        )
        import_resp = contacts_api.import_contacts(import_req)
        print(f"Import initiated for {len(data)} contacts. Response:", import_resp)

        return import_resp

    except ApiException as e:
        raise Exception(f"Brevo API Error (reset_and_import_contacts): {e}")
    
    
def update_attribute(email: str, campaign_email_number: int):
    """
    Update the CAMPAIGN_EMAIL_NUMBER attribute of a Brevo contact.
    """
    try:
        update_req = sib_api_v3_sdk.UpdateContact(
            attributes={"CAMPAIGN_EMAIL_NUMBER": campaign_email_number}
        )
        response = contacts_api.update_contact(email, update_req)
        print(f"Updated CAMPAIGN_EMAIL_NUMBER for {email} → {campaign_email_number}")
        return response
    except ApiException as e:
        raise Exception(f"Brevo API Error (update_attribute): {e}")



def update_attribute_v2(
    email: str,
    campaign_email_number: int,
    list_id: Optional[int] = None,
    remove_from_list: bool = False
):
    """
    Update CAMPAIGN_EMAIL_NUMBER and optionally (un)link a list
    **in the same update_contact call**.
    This is idempotent: we only add/remove if needed.
    """
    try:
        contact = contacts_api.get_contact_info(email).to_dict()
        current_lists = set(contact.get("list_ids", []))

        kwargs = {
            "attributes": {"CAMPAIGN_EMAIL_NUMBER": campaign_email_number}
        }

        if list_id is not None:
            if remove_from_list:
                if list_id in current_lists:
                    kwargs["unlink_list_ids"] = [list_id]
            else:
                if list_id not in current_lists:
                    kwargs["list_ids"] = [list_id]

        update_req = sib_api_v3_sdk.UpdateContact(**kwargs)

        resp = contacts_api.update_contact(email, update_req)
        print(f"Updated CAMPAIGN_EMAIL_NUMBER for {email} → {campaign_email_number}")

        if list_id is not None:
            action = "removed from" if remove_from_list else "added to"
            if ("list_ids" in kwargs) or ("unlink_list_ids" in kwargs):
                print(f"{email} {action} list {list_id}")
            else:
                print(f"No list change needed for {email} (already correct).")

        return resp

    except ApiException as e:
        # Brevo often packs useful details in the body
        raise Exception(f"Brevo API Error (update_attribute): {e}")


def update_template(template_id: int, html_content: str = None, subject: str = None, is_active: bool = None):
    """
    Update an existing Brevo transactional email template.

    Args:
        template_id (int): The ID of the template to update.
        html_content (str, optional): New HTML content (string).
        subject (str, optional): New subject line.
        is_active (bool, optional): Whether to activate/deactivate the template.
    """
    try:
        template_update = {}
        if html_content is not None:
            template_update["html_content"] = html_content
        if subject is not None:
            template_update["subject"] = subject
        if is_active is not None:
            template_update["is_active"] = is_active

        update_obj = sib_api_v3_sdk.UpdateSmtpTemplate(**template_update)

        response = email_api.update_smtp_template(template_id, update_obj)
        print(f"Template {template_id} updated successfully.")
        return response
    except ApiException as e:
        raise Exception(f"Brevo API Error (update_template): {e}")

def get_senders():
    """
    Retrieve the list of all Brevo senders (email + name + status).
    """
    try:
        senders_api = sib_api_v3_sdk.SendersApi(sib_api_v3_sdk.ApiClient(configuration))
        response = senders_api.get_senders()
        return response.to_dict()  
    except ApiException as e:
        raise Exception(f"Brevo API Error (get_senders): {e}")

def create_template(name: str, content, active):
    """
    Create Brevo email templates.
    """
    try:
        subject = "{{contact.SUBJECT}}"
        # DEV
        # sender = {"name": "PVGIS.COM", "email": "<EMAIL>"}
        # sender = {"name": "PVGIS.COM", "email": ""}
        # PROD
        sender = {"name": "PVGIS.COM", "email": "<EMAIL>"}
        create_req = sib_api_v3_sdk.CreateSmtpTemplate(
            template_name=name,
            subject=subject,
            sender=sender,
            html_content=content,
            is_active=active
        )

        resp = email_api.create_smtp_template(create_req)
        print(f"Template {name} created successfully.")
        return resp.to_dict()

    except ApiException as e:
        raise Exception(f"Brevo API Error (create_template): {e}")
    
def import_senders_from_list(senders: list):
    senders_api = sib_api_v3_sdk.SendersApi(sib_api_v3_sdk.ApiClient(configuration))
    results = []

    for sender in senders:
        create_req = sib_api_v3_sdk.CreateSender(
            name=sender["name"],
            email=sender["email"]
        )
        try:
            resp = senders_api.create_sender(sender=create_req)
            print(f" Sender created: {sender['name']} <{sender['email']}>")
            results.append(resp.to_dict() if hasattr(resp, "to_dict") else resp)
        except ApiException as e:
            results.append({
                "name": sender["name"],
                "email": sender["email"],
                "error": str(e)
            })
    return results


def delete_template(template_id: int):
    """
    Args:
        template_id (int): The ID of the template to delete.
    """
    try:
        deactivate_template(template_id)
        time.sleep(5)
        response = email_api.delete_smtp_template(template_id)
        print(f"Template {template_id} deleted successfully.")
        return response
    except ApiException as e:
        raise Exception(f"Brevo API Error (delete_template): {e}")
    
def deactivate_template(template_id: int):
    """
    Deactivate a Brevo transactional template (set is_active=False).
    """
    try:
        update_obj = sib_api_v3_sdk.UpdateSmtpTemplate(is_active=False)
        resp = email_api.update_smtp_template(template_id, update_obj)
        print(f"Template {template_id} deactivated successfully.")
        return resp
    except ApiException as e:
        raise Exception(f"Brevo API Error (deactivate_template): {e}")
    

# TEMPLATE CONFIG FROM BREVO
# FOREACH TEMPLATE CREATED, WE NEED TO DEFINE THE RULES
    
# FOR OTHER CAMPAIGNS, WE CAN ADD THE TEMPLATE INFORMATION
# PRO MIX
# DEV
# rules_email_from_brevo = {
#     (8, 0) : 144,
#     (8, 1) : 145,
#     (8, 2) : 146,
#     (8, 3) : 147,
#     (8, 4) : 148,
#     (8, 5) : 149,
#     (8, 6) : 150,
#     (8, 7) : 151,
#     (8, 8) : 152,
#     (8, 9) : 153,
#     (8, 10) : 154,
#     (8, 11) : 155,        
# }  
# rules_email_from_brevo_track = {
#     144: (8, 0),
#     145: (8, 1),
#     146: (8, 2),
#     147: (8, 3),
#     148: (8, 4),
#     149: (8, 5),
#     150: (8, 6),
#     151: (8, 7),
#     152: (8, 8),
#     153: (8, 9),
#     154: (8, 10),
#     155: (8, 11),         
# }

#PROD
# 18 TO 29
rules_email_from_brevo = {
    (8, 0) : 18,
    (8, 1) : 19,
    (8, 2) : 20,
    (8, 3) : 21,
    (8, 4) : 22,
    (8, 5) : 23,
    (8, 6) : 24,
    (8, 7) : 25,
    (8, 8) : 26,
    (8, 9) : 27,
    (8, 10) : 28,
    (8, 11) : 29,
}  
rules_email_from_brevo_track = {
    18: (8, 0),
    19: (8, 1),
    20: (8, 2),
    21: (8, 3),
    22: (8, 4),
    23: (8, 5),
    24: (8, 6),
    25: (8, 7),
    26: (8, 8),
    27: (8, 9),
    28: (8, 10),
    29: (8, 11),         
}
    
dataSenders =  [
    {
    "id": 2,
    "name": "PVGIS.COM",
    "email": "<EMAIL>"
    },
    {
    "id": 3,
    "name": "Amira",
    "email": "<EMAIL>"
    },
    {
    "id": 4,
    "name": "Anna",
    "email": "<EMAIL>"
    },
    {
    "id": 5,
    "name": "Hinata",
    "email": "<EMAIL>"
    },
    {
    "id": 6,
    "name": "Ishika",
    "email": "<EMAIL>"
    },
    {
    "id": 7,
    "name": "Marie",
    "email": "<EMAIL>"
    },
    {
    "id": 8,
    "name": "Mary",
    "email": "<EMAIL>"
    },
    {
    "id": 9,
    "name": "Yinuo",
    "email": "<EMAIL>"
    },
    {
    "id": 10,
    "name": "Christopher",
    "email": "<EMAIL>"
    },
    {
    "id": 11,
    "name": "Daniel",
    "email": "<EMAIL>"
    },
    {
    "id": 12,
    "name": "David",
    "email": "<EMAIL>"
    },
    {
    "id": 13,
    "name": "Jacques",
    "email": "<EMAIL>"
    },
    {
    "id": 14,
    "name": "James",
    "email": "<EMAIL>"
    },
    {
    "id": 15,
    "name": "Jean",
    "email": "<EMAIL>"
    },
    {
    "id": 16,
    "name": "John",
    "email": "<EMAIL>"
    },
    {
    "id": 17,
    "name": "Joseph",
    "email": "<EMAIL>"
    },
    {
    "id": 18,
    "name": "Matthew",
    "email": "<EMAIL>"
    },
    {
    "id": 19,
    "name": "Michael",
    "email": "<EMAIL>"
    },
    {
    "id": 20,
    "name": "Pierre",
    "email": "<EMAIL>"
    },
    {
    "id": 21,
    "name": "Robert",
    "email": "<EMAIL>"
    },
    {
    "id": 22,
    "name": "Thierry",
    "email": "<EMAIL>"
    },
    {
    "id": 23,
    "name": "William",
    "email": "<EMAIL>"
    },
    {
    "id": 24,
    "name": "Ahmed",
    "email": "<EMAIL>"
    },
    {
    "id": 25,
    "name": "Alberto",
    "email": "<EMAIL>"
    },
    {
    "id": 26,
    "name": "Alex",
    "email": "<EMAIL>"
    },
    {
    "id": 27,
    "name": "Alexandre",
    "email": "<EMAIL>"
    },
    {
    "id": 28,
    "name": "Alexei",
    "email": "<EMAIL>"
    },
    {
    "id": 29,
    "name": "Ali",
    "email": "<EMAIL>"
    },
    {
    "id": 30,
    "name": "Aman",
    "email": "<EMAIL>"
    },
    {
    "id": 31,
    "name": "Amir",
    "email": "<EMAIL>"
    },
    {
    "id": 32,
    "name": "Andrea",
    "email": "<EMAIL>"
    },
    {
    "id": 33,
    "name": "Andrei",
    "email": "<EMAIL>"
    },
    {
    "id": 34,
    "name": "Anton",
    "email": "<EMAIL>"
    },
    {
    "id": 35,
    "name": "Antonio",
    "email": "<EMAIL>"
    },
    {
    "id": 36,
    "name": "Aryan",
    "email": "<EMAIL>"
    },
    {
    "id": 37,
    "name": "Bilal",
    "email": "<EMAIL>"
    },
    {
    "id": 38,
    "name": "Bo",
    "email": "<EMAIL>"
    },
    {
    "id": 39,
    "name": "Carlo",
    "email": "<EMAIL>"
    },
    {
    "id": 40,
    "name": "Carlos",
    "email": "<EMAIL>"
    },
    {
    "id": 41,
    "name": "Chen",
    "email": "<EMAIL>"
    },
    {
    "id": 42,
    "name": "Cody",
    "email": "<EMAIL>"
    },
    {
    "id": 43,
    "name": "Cristian",
    "email": "<EMAIL>"
    },
    {
    "id": 44,
    "name": "Cristiano",
    "email": "<EMAIL>"
    },
    {
    "id": 45,
    "name": "Daan",
    "email": "<EMAIL>"
    },
    {
    "id": 46,
    "name": "Dev",
    "email": "<EMAIL>"
    },
    {
    "id": 47,
    "name": "Diogo",
    "email": "<EMAIL>"
    },
    {
    "id": 48,
    "name": "Dmitri",
    "email": "<EMAIL>"
    },
    {
    "id": 49,
    "name": "Federico",
    "email": "<EMAIL>"
    },
    {
    "id": 50,
    "name": "Felix",
    "email": "<EMAIL>"
    },
    {
    "id": 51,
    "name": "Fernando",
    "email": "<EMAIL>"
    },
    {
    "id": 52,
    "name": "Finn",
    "email": "<EMAIL>"
    },
    {
    "id": 53,
    "name": "Francisco",
    "email": "<EMAIL>"
    },
    {
    "id": 54,
    "name": "Gabriel",
    "email": "<EMAIL>"
    },
    {
    "id": 55,
    "name": "Giuseppe",
    "email": "<EMAIL>"
    },
    {
    "id": 56,
    "name": "Hao",
    "email": "<EMAIL>"
    },
    {
    "id": 57,
    "name": "Hassan",
    "email": "<EMAIL>"
    },
    {
    "id": 58,
    "name": "Ion",
    "email": "<EMAIL>"
    },
    {
    "id": 59,
    "name": "Ivan",
    "email": "<EMAIL>"
    },
    {
    "id": 60,
    "name": "Jan",
    "email": "<EMAIL>"
    },
    {
    "id": 61,
    "name": "Javier",
    "email": "<EMAIL>"
    },
    {
    "id": 62,
    "name": "Jens",
    "email": "<EMAIL>"
    },
    {
    "id": 63,
    "name": "Jie",
    "email": "<EMAIL>"
    },
    {
    "id": 64,
    "name": "Joao",
    "email": "<EMAIL>"
    },
    {
    "id": 65,
    "name": "Jose",
    "email": "<EMAIL>"
    },
    {
    "id": 66,
    "name": "Juan",
    "email": "<EMAIL>"
    },
    {
    "id": 67,
    "name": "Jun",
    "email": "<EMAIL>"
    },
    {
    "id": 68,
    "name": "Levi",
    "email": "<EMAIL>"
    },
    {
    "id": 69,
    "name": "Kai",
    "email": "<EMAIL>"
    },
    {
    "id": 70,
    "name": "Kunal",
    "email": "<EMAIL>"
    },
    {
    "id": 71,
    "name": "Lei",
    "email": "<EMAIL>"
    },
    {
    "id": 72,
    "name": "Li",
    "email": "<EMAIL>"
    },
    {
    "id": 73,
    "name": "Louis",
    "email": "<EMAIL>"
    },
    {
    "id": 74,
    "name": "Luca",
    "email": "<EMAIL>"
    },
    {
    "id": 75,
    "name": "Lucas",
    "email": "<EMAIL>"
    },
    {
    "id": 76,
    "name": "Luis",
    "email": "<EMAIL>"
    },
    {
    "id": 77,
    "name": "Lukas",
    "email": "<EMAIL>"
    },
    {
    "id": 78,
    "name": "Manuel",
    "email": "<EMAIL>"
    },
    {
    "id": 79,
    "name": "Marco",
    "email": "<EMAIL>"
    },
    {
    "id": 80,
    "name": "Matteo",
    "email": "<EMAIL>"
    },
    {
    "id": 81,
    "name": "Max",
    "email": "<EMAIL>"
    },
    {
    "id": 82,
    "name": "Maxim",
    "email": "<EMAIL>"
    },
    {
    "id": 83,
    "name": "Miguel",
    "email": "<EMAIL>"
    },
    {
    "id": 84,
    "name": "Mihai",
    "email": "<EMAIL>"
    },
    {
    "id": 85,
    "name": "Ming",
    "email": "<EMAIL>"
    },
    {
    "id": 86,
    "name": "Niklas",
    "email": "<EMAIL>"
    },
    {
    "id": 87,
    "name": "Noah",
    "email": "<EMAIL>"
    },
    {
    "id": 88,
    "name": "Omar",
    "email": "<EMAIL>"
    },
    {
    "id": 89,
    "name": "Paolo",
    "email": "<EMAIL>"
    },
    {
    "id": 90,
    "name": "Paul",
    "email": "<EMAIL>"
    },
    {
    "id": 91,
    "name": "Paulo",
    "email": "<EMAIL>"
    },
    {
    "id": 92,
    "name": "Pavel",
    "email": "<EMAIL>"
    },
    {
    "id": 93,
    "name": "Pedro",
    "email": "<EMAIL>"
    },
    {
    "id": 94,
    "name": "Peter",
    "email": "<EMAIL>"
    },
    {
    "id": 95,
    "name": "Philippe",
    "email": "<EMAIL>"
    },
    {
    "id": 96,
    "name": "Rahul",
    "email": "<EMAIL>"
    },
    {
    "id": 97,
    "name": "Raj",
    "email": "<EMAIL>"
    },
    {
    "id": 98,
    "name": "Ravi",
    "email": "<EMAIL>"
    },
    {
    "id": 99,
    "name": "Rohit",
    "email": "<EMAIL>"
    },
    {
    "id": 100,
    "name": "Roman",
    "email": "<EMAIL>"
    },
    {
    "id": 101,
    "name": "Rui",
    "email": "<EMAIL>"
    },
    {
    "id": 102,
    "name": "Sami",
    "email": "<EMAIL>"
    },
    {
    "id": 103,
    "name": "Samir",
    "email": "<EMAIL>"
    },
    {
    "id": 104,
    "name": "Sergei",
    "email": "<EMAIL>"
    },
    {
    "id": 105,
    "name": "Stefan",
    "email": "<EMAIL>"
    },
    {
    "id": 106,
    "name": "Tao",
    "email": "<EMAIL>"
    },
    {
    "id": 107,
    "name": "Tariq",
    "email": "<EMAIL>"
    },
    {
    "id": 108,
    "name": "Thomas",
    "email": "<EMAIL>"
    },
    {
    "id": 109,
    "name": "Tiago",
    "email": "<EMAIL>"
    },
    {
    "id": 110,
    "name": "Van",
    "email": "<EMAIL>"
    },
    {
    "id": 111,
    "name": "Vijay",
    "email": "<EMAIL>"
    },
    {
    "id": 112,
    "name": "Viktor",
    "email": "<EMAIL>"
    },
    {
    "id": 113,
    "name": "Wei",
    "email": "<EMAIL>"
    },
    {
    "id": 114,
    "name": "Yuri",
    "email": "<EMAIL>"
    },
    {
    "id": 115,
    "name": "Zain",
    "email": "<EMAIL>"
    }
]
    