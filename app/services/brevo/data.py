from app.models.account_information import AccountInformation
from app.models.account_type import AccountType
from app.models.country import Country
from app import crud 
from app.models.customer import Customer
from app.models.customer_email_item import CustomerEmailItem
from app.models.customer_email_task import CustomerEmailTask
from app.models.customer_email_campaign import CustomerEmailCampaign
from app.models.ce_sender_email_campaign_country import CeSenderEmailCampaignCountry
from app.models.customer_email_sender import CustomerEmailSender
from app.models.subscription import Subscription
from sqlalchemy.sql import and_, literal, func, case, or_
from sqlalchemy import literal, cast, Integer, text, func
from sqlalchemy.orm import Session
from app.services.brevo.utils.brevo import _as_aware, should_send_next_email
from app.crud.pvgis_service import create_token
from datetime import datetime, timedelta, timezone
from typing import Optional, Dict
from app.services.customer_email.customer_email_service import (
    get_template_content
)
from app.services.brevo.data_email import (
    get_subject_campaign
)
from app.services.brevo.brevo import (
    rules_email_from_brevo
)
try:
    from zoneinfo import ZoneInfo
except ImportError:
    ZoneInfo = None


from app.config.settings import get_settings
settings = get_settings()

def get_data_user_mail(db: Session, campaign_id: int, local_hour: int, to_email: Optional[str] = None, account_type: Optional[int] = None):
    subscription_sub_query = (
        db.query(
            Subscription.customer_id.label("customer_id"),
            literal(True).label("is_subscribed_user"),
        )
        .filter(
            and_(
                Subscription.subscription_status == "ACTIVE",
                Subscription.product_id != ********,
                Subscription.product_id.isnot(None),
            )
        )
        .subquery()
    )

    progress_subq = (
        db.query(
            CustomerEmailItem.customer_id.label("customer_id"),
            func.max(func.coalesce(CustomerEmailItem.current_task, -1)).label("max_task"),
        )
        .filter(
            CustomerEmailItem.campaign_number_brevo == campaign_id,
            CustomerEmailItem.sent_at.isnot(None),
            CustomerEmailItem.sender_email.isnot(None),
        )
        .group_by(CustomerEmailItem.customer_id)
        .subquery()
    )

    max_task_time_subq = (
        db.query(
            CustomerEmailItem.customer_id.label("customer_id"),
            func.max(CustomerEmailItem.sent_at).label("email_item_sent_at"),
        )
        .join(progress_subq, progress_subq.c.customer_id == CustomerEmailItem.customer_id)
        .filter(
            CustomerEmailItem.campaign_number_brevo == campaign_id,
            func.coalesce(CustomerEmailItem.current_task, -1) == progress_subq.c.max_task,
            CustomerEmailItem.sent_at.isnot(None),
            CustomerEmailItem.sender_email.isnot(None),
        )
        .group_by(CustomerEmailItem.customer_id)
        .subquery()
    )

    local_time_expr = func.convert_tz(func.utc_timestamp(), literal("+00:00"), Customer.timezone_offset)
    local_hour_expr = func.hour(local_time_expr)

    base_query = (
        db.query(
            Customer.id,
            Customer.full_name,
            Customer.email,
            Customer.settings_json,
            Customer.auth_user_id,
            Customer.timezone_offset,
            Country.name.label("country_name"),
            Country.timezone_name.label("timezone_name"),
            AccountType.name.label("user_type"),
            func.coalesce(subscription_sub_query.c.is_subscribed_user, False).label("is_subscribed_user"),
            func.coalesce(progress_subq.c.max_task, -1).label("current_task"),
            max_task_time_subq.c.email_item_sent_at.label("email_item_sent_at"),
            local_hour_expr.label("local_hour_expr"),
        )
        .outerjoin(subscription_sub_query, subscription_sub_query.c.customer_id == Customer.id)
        .outerjoin(AccountInformation, AccountInformation.customer_id == Customer.id)
        .outerjoin(AccountType, AccountType.id == AccountInformation.account_type_id)
        .outerjoin(Country, Country.id == Customer.country_id)
        .outerjoin(progress_subq, progress_subq.c.customer_id == Customer.id)
        .outerjoin(max_task_time_subq, max_task_time_subq.c.customer_id == Customer.id)
        .filter(
            Customer.auth_user_id.isnot(None),
            Customer.deleted_at.is_(None),
            or_(
                Customer.timezone_offset.is_(None),
                local_hour_expr == local_hour,
            )
        )
        .group_by(
            Customer.id,
            Customer.full_name,
            Customer.email,
            Customer.settings_json,
            Customer.auth_user_id,
            Customer.timezone_offset,
            Country.name,
            Country.timezone_name,
            AccountType.name,
            subscription_sub_query.c.is_subscribed_user,
            progress_subq.c.max_task,
            max_task_time_subq.c.email_item_sent_at,
            local_hour_expr,
        )
    )

    if to_email:
        base_query = base_query.filter(Customer.email == to_email)
        
    if account_type:
        base_query = base_query.filter(AccountInformation.account_type_id == account_type)

    rows = base_query.all()
    return rows

def get_data_email(
    db: Session, 
    email_user, 
    country_name, 
    campaign_task_id, 
    task_campaign,
):
    rules_email = rules_email_from_brevo
       
    customer = db.query(Customer).filter(Customer.email == email_user).first()
    if not customer:
        return None
    
    campaign_task = db.query(CustomerEmailTask).filter(
        CustomerEmailTask.id == campaign_task_id,
    ).first()
    
    campaign = db.query(CustomerEmailCampaign).filter(
        CustomerEmailCampaign.id == campaign_task.customer_email_campaign_id,
    ).first()
    
    if not campaign_task:
        return None
    
    country = db.query(Country).filter(Country.name == country_name).first()
    if country:
        sender_data = (
            db.query(CustomerEmailSender)
            .join(CeSenderEmailCampaignCountry,
                CustomerEmailSender.id == CeSenderEmailCampaignCountry.customer_email_sender_id)
            .filter(
                CeSenderEmailCampaignCountry.customer_email_campaign_id == campaign.id,
                CeSenderEmailCampaignCountry.country_id == country.id
            )
            .first()
        )
        if not sender_data or not sender_data.email or not sender_data.name:
            sender_data_final = country.email_sender_json
        else:
            sender_data_final = {
                "name": sender_data.name,
                "email": sender_data.email,
            }  
    else:
        sender_data_final = {
            "name": "PVGIS.COM",
            "email": "<EMAIL>"
        }
        
    return {
        "sender": sender_data_final,
        "customer": customer.email,
        "template_id": rules_email.get((campaign_task.id, task_campaign)),
    }
    
    # NEXT TIME
    # if task_campaign == 0:
        
    # email_item = db.query(CustomerEmailItem).filter(
    #     CustomerEmailItem.customer_id == customer.id,
    #     CustomerEmailItem.campaign_number_brevo == campaign_task.id,
    #     CustomerEmailItem.current_task == task_campaign - 1,
    #     CustomerEmailItem.sent_at.isnot(None),
    #     CustomerEmailItem.sender_email.isnot(None),
    # ).order_by(CustomerEmailItem.id.desc()).first()
    
    # if not email_item:
    #     return None
    
    # last_country_sender = db.query(Country).filter(
    #     Country.id == email_item.country_id
    # ).first()
    
    # return {
    #     "customer": customer.email,
    #     "sender": last_country_sender.email_sender_json if last_country_sender else country.email_sender_json,
    #     # "sender": {
    #     #     "name": "PVGIS.COM",
    #     #     "email": "<EMAIL>"
    #     # },
    #     "campaign_task": campaign_task.id,
    #     "task": task_campaign,
    #     "template_id": rules_email.get((campaign_task.id, task_campaign)),
    # }
    
    
    
def generate_payload_to_brevo(
    db: Session,
    rows,
    campaign_id: int,
    rules: Optional[Dict[int, timedelta]] = None,
    now: Optional[datetime] = None,
    default_tz: str = "UTC",
):
    if rules is None:
        rules = {
            0: timedelta(0),          
            1: timedelta(days=2),     
            2: timedelta(days=2),     
            3: timedelta(days=2),     
            4: timedelta(days=2),     
            5: timedelta(days=2),     
            6: timedelta(days=2),     
            7: timedelta(days=2),     
            8: timedelta(days=3),     
            9: timedelta(days=3),     
            10: timedelta(days=3),     
            11: timedelta(days=3),     
        }
        
    data = []
    for r in rows:
        lang = None
        if r.settings_json and isinstance(r.settings_json, dict):
            lang = r.settings_json.get("language") or r.settings_json.get("lang")
        language = lang or "en"
        
        # CUSTOMER TIME ZONE
        if now is None:
            if ZoneInfo and getattr(r, "timezone_name", None):
                user_tz = ZoneInfo(r.timezone_offset)
                now_for_user = datetime.now(user_tz)
            else:
                now_for_user = datetime.now(timezone.utc)
        else:
            now_for_user = now
            
        last_sent_at = _as_aware(r.email_item_sent_at, default_tz=default_tz)
    

        eligible, next_email_number = should_send_next_email(
            current_task=r.current_task,
            last_sent_at=last_sent_at,
            now=now_for_user,
            rules=rules,
        )
        
        if not eligible:
            continue

        token_data = {
            "token_type": "invitation",
            "customer": {"id": r.id, "auth_user_id": r.auth_user_id},
            "no_limit": True,
        }

        next_email_number = 0 if r.current_task in (-1, None) else (r.current_task + 1)
        
        # END TASK IF > 12
        # FOR OTHER CAMPAIGNS, WE CAN DECREASE OR INCREASE
        if next_email_number >= 12:
            return []
        
        subject = get_subject_campaign(db, campaign_id, language, r.current_task)
        
        data.append({
            "email": r.email,
            "attributes": {
                "EMAIL": r.email,
                "FULL_NAME": r.full_name or "",
                "LANGUAGE": language,
                "COUNTRY": r.country_name or "France",
                "TIMEZONE": r.timezone_offset or "",
                "USER_TYPE": r.user_type or "",
                "IS_SUBSCRIBED": "Yes" if r.is_subscribed_user else "No",
                "TOKEN": create_token(token_data),
                "CAMPAIGN_ID": campaign_id,  
                "CAMPAIGN_EMAIL_NUMBER": next_email_number,
                "HOME_PAGE_URL": settings.PVGIS_UI_URL,
                "UNSUBSCRIBE_URL": settings.PVGIS_UI_URL + '/unsubscribe',
                "YEAR": datetime.now().year,
                "SUBJECT": subject,
                "PVGIS_UI_URL": settings.PVGIS_UI_URL + f'/{language}',
                "UNSUBSCRIBEURL": settings.PVGIS_UI_URL + '/customer/unsubscribe',
                "PVGIS24_EXTRANETURL": settings.PVGIS_UI_URL + f'/pvgis24/app/{r.id}/pvgis24',
                "PVGIS24_SUBSCRIPTIONURL": settings.PVGIS_UI_URL + f'/{language}/subscription',
                "PVGIS24_PREMIUM_SUBSCRIPTIONURL": settings.PVGIS_UI_URL + '/subscribe/premium',
                "SUBSCRIBE_URL": settings.PVGIS_UI_URL + f'/{language}/subscription',
                "PVGIS24_URL": settings.PVGIS_UI_URL + f'/pvgis24/app/{r.id}/pvgis24',
                "HOMEPAGE_URL": settings.PVGIS_UI_URL + f'/{language}',
                "TESTOMONY_LINK": settings.PVGIS_UI_URL + f'/{language}/subscription',
                "PVGIS_OFFER_LINK": settings.PVGIS_UI_URL + f'/{language}/subscription?requires_login=1',
            },
        })
    return data


def generate_payload_to_brevo_simple(
    db: Session,
    rows,
    campaign_id: int,
):
    data = []
    for r in rows:
        lang = None
        if r.settings_json and isinstance(r.settings_json, dict):
            lang = r.settings_json.get("language") or r.settings_json.get("lang")
        language = lang or "en"

        token_data = {
            "token_type": "invitation",
            "customer": {"id": r.id, "auth_user_id": r.auth_user_id},
            "no_limit": True,
        }

        next_email_number = 0 if r.current_task in (-1, None) else (r.current_task + 1)

        subject = get_subject_campaign(db, campaign_id, language, r.current_task)
        
        data.append({
            "email": r.email,
            "attributes": {
                "EMAIL": r.email,
                "FULL_NAME": r.full_name or "",
                "LANGUAGE": language,
                "COUNTRY": r.country_name or "",
                "TIMEZONE": r.timezone_name or "",
                "USER_TYPE": r.user_type or "",
                "IS_SUBSCRIBED": "Yes" if r.is_subscribed_user else "No",
                "TOKEN": create_token(token_data),
                "CAMPAIGN_ID": campaign_id,  
                "CAMPAIGN_EMAIL_NUMBER": next_email_number,
                "HOME_PAGE_URL": settings.PVGIS_UI_URL,
                "UNSUBSCRIBE_URL": settings.PVGIS_UI_URL + '/unsubscribe',
                "YEAR": datetime.now().year,
                "SUBJECT": subject,
                "PVGIS_UI_URL": settings.PVGIS_UI_URL + f'/{language}',
                "UNSUBSCRIBEURL": settings.PVGIS_UI_URL + '/customer/unsubscribe',
                "PVGIS24_EXTRANETURL": settings.PVGIS_UI_URL + f'/pvgis24/app/{r.id}/pvgis24',
                "PVGIS24_SUBSCRIPTIONURL": settings.PVGIS_UI_URL + f'/{language}/subscription',
                "PVGIS24_PREMIUM_SUBSCRIPTIONURL": settings.PVGIS_UI_URL + '/subscribe/premium',
                "SUBSCRIBE_URL": settings.PVGIS_UI_URL + f'/{language}/subscription',
                "PVGIS24_URL": settings.PVGIS_UI_URL + f'/pvgis24/app/{r.id}/pvgis24',
                "HOMEPAGE_URL": settings.PVGIS_UI_URL + f'/{language}',
                "TESTOMONY_LINK": settings.PVGIS_UI_URL + f'/{language}/subscription',
                "PVGIS_OFFER_LINK": settings.PVGIS_UI_URL + f'/{language}/subscription?requires_login=1',
            },
        })
    return data