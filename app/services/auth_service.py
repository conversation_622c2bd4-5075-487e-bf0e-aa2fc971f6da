from fastapi import HTT<PERSON>Exception
from fastapi.encoders import jsonable_encoder
import requests
from sqlalchemy import func
from sqlalchemy.orm import Session
from app import crud
from app.config.settings import get_settings
from app.enums.subscription_status_enum import SubscriptionStatusEnum
from app.models.customer import Customer
from app.models.invitation import Invitation
from app.schemas.invitation import InvitationAccept
from app.services.subscription_service import get_user_and_subscription_user

settings = get_settings()


# TODO: important: create an endpoint in auth allowing to get all the following data with a single request
def get_user_by_id(user_id):
    response = requests.get(
        f"{settings.AUTH_API_URL}/user/{user_id}", 
        headers=settings.x_app_key_header,
    ) 
    if response.status_code != 200:
        error_message = response.json().get("error", "Unknown error occurred when getting user by id")
        raise Exception(error_message)

    res_body = response.json()
    return res_body 

def get_user_profile_by_username(username):
    response = requests.get(
        f"{settings.AUTH_API_URL}/user-app/all-user/{username}", 
        headers=settings.x_app_key_header,
    )

    if response.status_code != 200:
        error_message = response.json().get("error", "Unknown error occurred when getting user profile by username")
        raise Exception(error_message)

    res_body = response.json()
    return res_body 

def get_user_role_by_id(user_id):
    response = requests.get(
        f"{settings.AUTH_API_URL}/user-app/role/{user_id}", 
        headers=settings.x_app_key_header,
    )

    if response.status_code != 200:
        error_message = response.json().get("error", "Unknown error occurred when getting user role by id")
        raise Exception(error_message)

    res_body = response.json()
    return res_body 

#subscribed or not
 
        
def auto_login(db: Session, core_customer: Customer):
    if not core_customer or not core_customer.auth_user_id:
        raise HTTPException(
            status_code=403, detail="pvgis.authentication_error.non_existing_user"
        )
    if (core_customer and (core_customer.deleted_at)):
        raise HTTPException(
            status_code=403, detail="pvgis.authentication_error.user_disabled"
        )
    
    customer = crud.customer.get(db=db, id=core_customer.id, relations=["account_information"])
    user = get_user_by_id(core_customer.auth_user_id)
    payload = {"username": user['username'], "appKey": settings.APP_KEY, "forceUpdate": True}

    response = requests.post(
        f"{settings.AUTH_API_URL}/user/social-networks/signin",
        json=payload,
        headers=settings.x_app_key_header,
    )
    if response.status_code != 202:
        error_message = response.json().get("error", "Unknown error occurred")
        raise Exception(error_message)
    
    auth_res = response.json()["data"]
    del auth_res["userAppId"]
    
    found_customer, found_subscription_user = get_user_and_subscription_user(db, core_customer.auth_user_id)
    user_type = "customer" if found_customer else "subscription_user"
    main_user_data = {
        **auth_res,
        "customer": customer,
        "user_type": user_type,
        'user_id': core_customer.auth_user_id,
        "subscription_users": jsonable_encoder(found_subscription_user)
    }
    return main_user_data

def create_auth_user_from_invitation(db: Session, 
                                     found_invitation: Invitation,
                                     invitation_accept_in: InvitationAccept, 
                                     customer_obj: Customer):
    crud.invitation.check_customer_and_email(db=db, email=invitation_accept_in.email, customer=customer_obj)

    auth_payload = {
        "firstname": invitation_accept_in.first_name,
        "lastname": invitation_accept_in.last_name,
        "username": invitation_accept_in.email, 
        "appId": settings.PVGIS_APP_ID,
        "roleId": settings.CUSTOMER_ROLE_ID,
        **({"password": invitation_accept_in.password} if invitation_accept_in.password else {})
    }
    
    auth_user_id = crud.login.create_user_to_auth_without_mail(auth_payload=auth_payload)
    
    update_customer_full_data(
        db,
        auth_user_id,
        invitation_accept_in,
        customer_obj,
    )
    customer_obj.auth_user_id = auth_user_id
    
    # update invitation_accepted_at 
    invitation_update_in = {
        "invitation_accepted_at": func.now()
    }
    crud.invitation.update(db=db, db_obj=found_invitation, obj_in=invitation_update_in, commit=False)
    db.commit()
    return auth_user_id


def update_auth_user_after_invitation(
    db: Session,  
    invitation_accept_in: InvitationAccept, 
    customer_obj: Customer
):
    
    # update auth data
    auth_payload = {
        "firstname": invitation_accept_in.first_name,
        "lastname": invitation_accept_in.last_name, 
    } 
    crud.login.update_user_profile(customer_obj.auth_user_id, auth_payload=auth_payload)
    
    
    update_customer_full_data(
        db,
        customer_obj.auth_user_id,
        invitation_accept_in,
        customer_obj,
    )
    
    crud.login.update_user_password(customer_obj.email, invitation_accept_in.password)

     
    db.commit()
    return customer_obj.auth_user_id

def update_customer_full_data(
    db: Session,  
    auth_user_id: int,
    invitation_accept_in: InvitationAccept, 
    customer_obj: Customer
):
# update customer to the new id in auth
    customer_update_in = {
        "first_name": invitation_accept_in.first_name,
        "last_name": invitation_accept_in.last_name,
        "auth_user_id": auth_user_id, 
        **({"email": invitation_accept_in.email} if invitation_accept_in.email else {}),
        'accept_cgu': invitation_accept_in.accept_cgu,
        'created_at': func.now(),
        **({"country": invitation_accept_in.country} if invitation_accept_in.country else {}) 
    }
    crud.customer.update(db=db, db_obj=customer_obj, obj_in=customer_update_in, commit=False)
    
    # update account information 
    found_account_info = crud.account_information.get_first_where_array(
        db=db,
        where=[
            {
                "key": "customer_id",
                "value": invitation_accept_in.customer_id,
                "operator": "==",
            }
        ],
    )
    if found_account_info and invitation_accept_in.company_name:
        account_info_in = {
            "company_name": invitation_accept_in.company_name,
            "professional_category_id": invitation_accept_in.professional_category_id,
        }
        updated_account_info = crud.account_information.update(
            db=db, db_obj=found_account_info, obj_in=account_info_in, commit=False
        )