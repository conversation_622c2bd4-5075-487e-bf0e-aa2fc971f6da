from app.services.customer_email.email_task.registered_invitation import generate_query_registered_invitation, registered_invitation_pre_send_process, send_to_when_registered_invitation
from app.services.customer_email.email_task.welcome import (
    send_to_when_welcome_and_discovery,
    generate_query_welcome_and_discovery,
)

from app.services.customer_email.email_task.user_testimonial import (
    send_to_when_user_testimonial,
    generate_query_user_testimonial,
)
from app.services.customer_email.email_task.highlighting_features import (
    send_to_when_highlighting_features,
    generate_query_highlighting_features,
)

from app.services.customer_email.email_task.special_offer import (
    send_to_when_special_offer,
    generate_query_special_offer,
)

from app.services.customer_email.email_task.last_chance import (
    send_to_when_last_chance,
    generate_query_last_chance,
)

from app.services.customer_email.email_task.installer_invitation import (
    installer_invitation_pre_send_process,
    send_to_installer,
    generate_query_installer,
)

from app.services.customer_email.email_task.installer_invitation_2 import (
    send_to_installer_2,
    generate_query_installer_2,
)

from app.services.customer_email.email_task.pro_mix.pvgis_email_pro_mixte_1_j_1 import (
    send_to_pro_mix_1,
    generate_query_pro_mix_1,
)
from app.services.customer_email.email_task.pro_mix.pvgis_email_pro_mixte_2_j_3 import (
    send_to_pro_mix_2,
    generate_query_pro_mix_2,
)
from app.services.customer_email.email_task.pro_mix.pvgis_email_pro_mixte_3_j_5 import (
    send_to_pro_mix_3,
    generate_query_pro_mix_3,
)
from app.services.customer_email.email_task.pro_mix.pvgis_email_pro_mixte_4_j_7 import (
    send_to_pro_mix_4,
    generate_query_pro_mix_4,
)
from app.services.customer_email.email_task.pro_mix.pvgis_email_pro_mixte_5_j_9 import (
    send_to_pro_mix_5,
    generate_query_pro_mix_5,
)
from app.services.customer_email.email_task.pro_mix.pvgis_email_pro_mixte_6_j_11 import (
    send_to_pro_mix_6,
    generate_query_pro_mix_6,
)
from app.services.customer_email.email_task.pro_mix.pvgis_email_pro_mixte_7_j_13 import (
    send_to_pro_mix_7,
    generate_query_pro_mix_7,
)
from app.services.customer_email.email_task.pro_mix.pvgis_email_pro_mixte_8_j_15 import (
    send_to_pro_mix_8,
    generate_query_pro_mix_8,
)
from app.services.customer_email.email_task.pro_mix.pvgis_email_pro_mixte_9_j_18 import (
    send_to_pro_mix_9,
    generate_query_pro_mix_9,
)
from app.services.customer_email.email_task.pro_mix.pvgis_email_pro_mixte_10_j_21 import (
    send_to_pro_mix_10,
    generate_query_pro_mix_10,
)
from app.services.customer_email.email_task.pro_mix.pvgis_email_pro_mixte_11_j_23 import (
    send_to_pro_mix_11,
    generate_query_pro_mix_11,
)
from app.services.customer_email.email_task.pro_mix.pvgis_email_pro_mixte_12_j_30 import (
    send_to_pro_mix_12,
    generate_query_pro_mix_12,
)

from app.services.customer_email.email_task.spin_tap.pvgis_email_campaign_day1_verify_quote import (
    send_to_spin_1,
    generate_query_spin_1,
)
from app.services.customer_email.email_task.spin_tap.pvgis_email_campaign_day3_exaggerated_estimates import (
    send_to_spin_2,
    generate_query_spin_2,
)
from app.services.customer_email.email_task.spin_tap.pvgis_email_campaign_day5_long_term_loss import (
    send_to_spin_3,
    generate_query_spin_3,
)
from app.services.customer_email.email_task.spin_tap.pvgis_email_campaign_day7_reliable_analysis_9eur import (
    send_to_spin_4,
    generate_query_spin_4,
)
from app.services.customer_email.email_task.spin_tap.pvgis_email_campaign_day14_compare_offers import (
    send_to_spin_5,
    generate_query_spin_5,
)
from app.services.customer_email.email_task.spin_tap.pvgis_email_campaign_day16_profitability_check import (
    send_to_spin_6,
    generate_query_spin_6,
)
from app.services.customer_email.email_task.spin_tap.pvgis_email_campaign_day18_poor_choice_cost import (
    send_to_spin_7,
    generate_query_spin_7,
)
from app.services.customer_email.email_task.spin_tap.pvgis_email_campaign_day20_validate_project import (
    send_to_spin_8,
    generate_query_spin_8,
)
# PROJECT SPIN
from app.services.customer_email.email_task.spin_project.pvgis_email_campaign_day1_spin_project import (
    send_to_spin_project_1,
    generate_query_spin_project_1,
)
from app.services.customer_email.email_task.spin_project.pvgis_email_campaign_day3_spin_project import (
    send_to_spin_project_2,
    generate_query_spin_project_2,
)
from app.services.customer_email.email_task.spin_project.pvgis_email_campaign_day5_spin_project import (
    send_to_spin_project_3,
    generate_query_spin_project_3,
)
from app.services.customer_email.email_task.spin_project.pvgis_email_campaign_day7_spin_project import (
    send_to_spin_project_4,
    generate_query_spin_project_4,
)
from app.services.customer_email.email_task.spin_project.pvgis_email_campaign_day14_spin_project import (
    send_to_spin_project_5,
    generate_query_spin_project_5,
)
from app.services.customer_email.email_task.spin_project.pvgis_email_campaign_day16_spin_project import (
    send_to_spin_project_6,
    generate_query_spin_project_6,
)
from app.services.customer_email.email_task.spin_project.pvgis_email_campaign_day18_spin_project import (
    send_to_spin_project_7,
    generate_query_spin_project_7,
)
from app.services.customer_email.email_task.spin_project.pvgis_email_campaign_day20_spin_project import (
    send_to_spin_project_8,
    generate_query_spin_project_8,
)
# PROJECT SPIN
# CONTROL SPIN
from app.services.customer_email.email_task.spin_control.pvgis_email_campaign_day1_spin_control import (
    send_to_spin_control_1,
    generate_query_spin_control_1,
)
from app.services.customer_email.email_task.spin_control.pvgis_email_campaign_day3_spin_control import (
    send_to_spin_control_2,
    generate_query_spin_control_2,
)
from app.services.customer_email.email_task.spin_control.pvgis_email_campaign_day5_spin_control import (
    send_to_spin_control_3,
    generate_query_spin_control_3,
)
from app.services.customer_email.email_task.spin_control.pvgis_email_campaign_day7_spin_control import (
    send_to_spin_control_4,
    generate_query_spin_control_4,
)
from app.services.customer_email.email_task.spin_control.pvgis_email_campaign_day14_spin_control import (
    send_to_spin_control_5,
    generate_query_spin_control_5,
)
from app.services.customer_email.email_task.spin_control.pvgis_email_campaign_day16_spin_control import (
    send_to_spin_control_6,
    generate_query_spin_control_6,
)
from app.services.customer_email.email_task.spin_control.pvgis_email_campaign_day18_spin_control import (
    send_to_spin_control_7,
    generate_query_spin_control_7,
)
from app.services.customer_email.email_task.spin_control.pvgis_email_campaign_day20_spin_control import (
    send_to_spin_control_8,
    generate_query_spin_control_8,
)
# CONTROL SPIN


from app.services.customer_email.utils import (
    generate_random_subscribed_user_query,
    generate_email_items,
    get_sender
)


factories = {
    "welcome_and_discovery": send_to_when_welcome_and_discovery,
    "user_testimonial": send_to_when_user_testimonial,
    "highlighting_features": send_to_when_highlighting_features,
    "special_offer": send_to_when_special_offer,
    "last_chance": send_to_when_last_chance,
    "installer_invitation": send_to_installer,
    'registered_invitation':send_to_when_registered_invitation,
    "pvgis_email_pro_mixte_1_j_1": send_to_pro_mix_1,
    "pvgis_email_pro_mixte_2_j_3": send_to_pro_mix_2,
    "pvgis_email_pro_mixte_3_j_5": send_to_pro_mix_3,
    "pvgis_email_pro_mixte_4_j_7": send_to_pro_mix_4,
    "pvgis_email_pro_mixte_5_j_9": send_to_pro_mix_5,
    "pvgis_email_pro_mixte_6_j_11": send_to_pro_mix_6,
    "pvgis_email_pro_mixte_7_j_13": send_to_pro_mix_7,
    "pvgis_email_pro_mixte_8_j_15": send_to_pro_mix_8,
    "pvgis_email_pro_mixte_9_j_18": send_to_pro_mix_9,
    "pvgis_email_pro_mixte_10_j_21": send_to_pro_mix_10,
    "pvgis_email_pro_mixte_11_j_23": send_to_pro_mix_11,
    "pvgis_email_pro_mixte_12_j_30": send_to_pro_mix_12,
    "pvgis_email_campaign_day1_verify_quote": send_to_spin_1,
    "pvgis_email_campaign_day3_exaggerated_estimates": send_to_spin_2,
    "pvgis_email_campaign_day5_long_term_loss": send_to_spin_3,
    "pvgis_email_campaign_day7_reliable_analysis_9eur": send_to_spin_4,
    "pvgis_email_campaign_day14_compare_offers": send_to_spin_5,
    "pvgis_email_campaign_day16_profitability_check": send_to_spin_6,
    "pvgis_email_campaign_day18_poor_choice_cost": send_to_spin_7,
    "pvgis_email_campaign_day20_validate_project": send_to_spin_8,
    "pvgis-email-projet-spin-1-j-1": send_to_spin_project_1,
    "pvgis-email-projet-spin-2-j-3": send_to_spin_project_2,
    "pvgis-email-projet-spin-3-j-5": send_to_spin_project_3,
    "pvgis-email-projet-spin-4-j-7": send_to_spin_project_4,
    "pvgis-email-projet-spin-5-j-14": send_to_spin_project_5,
    "pvgis-email-projet-spin-6-j-16": send_to_spin_project_6,
    "pvgis-email-projet-spin-7-j-18": send_to_spin_project_7,
    "pvgis-email-projet-spin-8-j-20": send_to_spin_project_8,
    "pvgis-email-control-spin-1-j-1": send_to_spin_control_1,
    "pvgis-email-control-spin-2-j-3": send_to_spin_control_2,
    "pvgis-email-control-spin-3-j-5": send_to_spin_control_3,
    "pvgis-email-control-spin-4-j-7": send_to_spin_control_4,
    "pvgis-email-control-spin-5-j-14": send_to_spin_control_5,
    "pvgis-email-control-spin-6-j-16": send_to_spin_control_6,
    "pvgis-email-control-spin-7-j-18": send_to_spin_control_7,
    "pvgis-email-control-spin-8-j-20": send_to_spin_control_8,
    "installer_invitation_2": send_to_installer_2,
    # Add more selectors here as needed
}

queries = {
    "welcome_and_discovery": generate_query_welcome_and_discovery,
    "user_testimonial": generate_query_user_testimonial,
    "highlighting_features": generate_query_highlighting_features,
    "special_offer": generate_query_special_offer,
    "last_chance": generate_query_last_chance,
    "installer_invitation": generate_query_installer,
    'registered_invitation': generate_query_registered_invitation,
    "pvgis_email_pro_mixte_1_j_1": generate_query_pro_mix_1,
    "pvgis_email_pro_mixte_2_j_3": generate_query_pro_mix_2,
    "pvgis_email_pro_mixte_3_j_5": generate_query_pro_mix_3,
    "pvgis_email_pro_mixte_4_j_7": generate_query_pro_mix_4,
    "pvgis_email_pro_mixte_5_j_9": generate_query_pro_mix_5,
    "pvgis_email_pro_mixte_6_j_11": generate_query_pro_mix_6,
    "pvgis_email_pro_mixte_7_j_13": generate_query_pro_mix_7,
    "pvgis_email_pro_mixte_8_j_15": generate_query_pro_mix_8,
    "pvgis_email_pro_mixte_9_j_18": generate_query_pro_mix_9,
    "pvgis_email_pro_mixte_10_j_21": generate_query_pro_mix_10,
    "pvgis_email_pro_mixte_11_j_23": generate_query_pro_mix_11,
    "pvgis_email_pro_mixte_12_j_30": generate_query_pro_mix_12,
    "pvgis_email_campaign_day1_verify_quote": generate_query_spin_1,
    "pvgis_email_campaign_day3_exaggerated_estimates": generate_query_spin_2,
    "pvgis_email_campaign_day5_long_term_loss": generate_query_spin_3,
    "pvgis_email_campaign_day7_reliable_analysis_9eur": generate_query_spin_4,
    "pvgis_email_campaign_day14_compare_offers": generate_query_spin_5,
    "pvgis_email_campaign_day16_profitability_check": generate_query_spin_6,
    "pvgis_email_campaign_day18_poor_choice_cost": generate_query_spin_7,
    "pvgis_email_campaign_day20_validate_project": generate_query_spin_8,
    "pvgis-email-projet-spin-1-j-1": generate_query_spin_project_1,
    "pvgis-email-projet-spin-2-j-3": generate_query_spin_project_2,
    "pvgis-email-projet-spin-3-j-5": generate_query_spin_project_3,
    "pvgis-email-projet-spin-4-j-7": generate_query_spin_project_4,
    "pvgis-email-projet-spin-5-j-14": generate_query_spin_project_5,
    "pvgis-email-projet-spin-6-j-16": generate_query_spin_project_6,
    "pvgis-email-projet-spin-7-j-18": generate_query_spin_project_7,
    "pvgis-email-projet-spin-8-j-20": generate_query_spin_project_8,
    "pvgis-email-control-spin-1-j-1": generate_query_spin_control_1,
    "pvgis-email-control-spin-2-j-3": generate_query_spin_control_2,
    "pvgis-email-control-spin-3-j-5": generate_query_spin_control_3,
    "pvgis-email-control-spin-4-j-7": generate_query_spin_control_4,
    "pvgis-email-control-spin-5-j-14": generate_query_spin_control_5,
    "pvgis-email-control-spin-6-j-16": generate_query_spin_control_6,
    "pvgis-email-control-spin-7-j-18": generate_query_spin_control_7,
    "pvgis-email-control-spin-8-j-20": generate_query_spin_control_8,
    "installer_invitation_2": generate_query_installer_2,
    # Add more selectors here as needed
}
pre_sending_processes = {
    "installer_invitation": installer_invitation_pre_send_process,
    "registered_invitation": registered_invitation_pre_send_process,
}


"""
INPUTS: account_type_id, current_datetime

ALL EMAIL:
    - where it is 8am in their country (related to current_datetime)
    - with no subscription
    - with no <current-mail> sent yet
    
    
    


Email 1 : Envoyé 1 jour après l'inscription.
    select
        customer:
            email
            settings_json => language  
        country:
            name
    from 
    customer c 
     join account_information ai on c.account_information_id = ai.id
    join country co on co.name = c.country  
    join (
        SELECT  ei2.customer_id, count(ei2.id)  
        FROM 
            customer_email_item ei2
            JOIN customer_email_task et2 on et2.id = ei2.customer_email_task_id
        where et2.email_number = 1
        group by ei2.customer_id
        having count(ei2.id) =0
 
    ) no_2 on no_2.customer_id = c.id
     where 
        c.subscription_id is null
        and ai.account_type_id=:account_type_id
        and c.created_at <= :yesterday_date
        and get_time(co.timezone_offset, :current_date) = 8am
     

Email 2 : Envoyé 3 jours après l'email 1
     select
        customer:
            email
            settings_json => language  
        country:
            name
    from 
    customer c 
    join country co on co.name = c.country  
    join customer_email_item ei1 on ei1.customer_id = c.id 
    join customer_email_task et1 on et1.id = ei1.customer_email_task_id and et1.email_number = 1
    join
    (
        SELECT  ei2.customer_id, count(ei2.id)  
        FROM 
            customer_email_item ei2
            JOIN customer_email_task et2 on et2.id = ei2.customer_email_task_id
        where et2.email_number = 2
        group by ei2.customer_id
        having count(ei2.id) =0
 
    ) no_2 on no_2.customer_id = c.id
     where 
        get_time(co.timezone, :current_date) = 8am
        and account.account_type_id=:account_type_id
        and c.subscription_id is null
         ei1.sent_at <=  :toda_date

     


Email 3 : Envoyé 5 jours après l'email 2.
    
 
Email 4 :
    - Envoyé 3 jours avant la fin de l'offre spéciale.
        - fin de offre speciale = date d'inscription + n days (a determiner) ou alors get from somewhere else
    -

Email 5:
    - Envoyé le jour de la fin de l'offre spéciale. 
"""