 
from datetime import datetime, timedelta
from importlib.util import spec_from_file_location
import random
from app import crud
from app.models.account_information import AccountInformation
from app.models.country import Country
from app.models.customer import Customer
from app.models.customer_email_item import CustomerEmailItem
from app.models.customer_email_sender import CustomerEmailSender
from app.models.customer_email_task import CustomerEmailTask
from app.models.subscription import Subscription
from sqlalchemy.orm import Session, aliased
from sqlalchemy import and_, func, text, cast, Date
from sqlalchemy.sql import or_, and_, not_
from tzlocal import get_localzone

from app.schemas.customer_email_item import CustomerEmailItemGenerateParams


def get_local_timezone_offset():
    # Get the server's local timezone dynamically
    local_tz = get_localzone()

    # Get current time in the local timezone
    local_time = datetime.now(local_tz)

    # Get the timezone offset (UTC offset) in hours and minutes
    offset = local_time.strftime('%z')
    formatted_offset = f"{offset[:3]}:{offset[3:]}" 
    return formatted_offset 

def valid_subscription_join(
    subscription,
    customer, 
    current_datetime,
    always_ongoing_sub = False):
    always_ongoing_conditions = (
        subscription.start_date <= current_datetime,  
        # subscription.expired_date >= current_datetime, 
        )
    return and_(
            subscription.customer_id == customer.id,
            subscription.subscription_status == "ACTIVE",
            subscription.product_id!= 11142024,
            subscription.product_id is not None,
            *(always_ongoing_conditions if always_ongoing_sub else ())
        )

def generate_random_subscribed_user_query(db: Session, current_datetime):
    c = aliased(Customer)
     
    return db.query(
        c.country_id,
        func.min(c.id).label("customer_id")
    ).join(
        Subscription,
        valid_subscription_join(Subscription, c, current_datetime, True)
    ).filter(
        c.deleted_at.is_(None),
        not_(c.first_name.is_(None)),
        func.length(func.trim(c.first_name)) > 0
    ).group_by(c.country_id).subquery()

def get_language (customer: Customer, country: Country, customer_email_task: CustomerEmailTask):
    global_default_language = 'en'
    language = global_default_language 
    if customer.settings_json and "language" in customer.settings_json and customer.settings_json["language"]:
        language = customer.settings_json["language"]
    elif len(customer_email_task.customer_email_campaign.ce_campaign_country_data): 
        current_country_data = next((data for data in customer_email_task.customer_email_campaign.ce_campaign_country_data if data.country_id == country.id), None)
        language = current_country_data.default_language if current_country_data else global_default_language
         
        
     
    return language if language and len(language) == 2 else global_default_language
    
def get_sender(country: Country, customer_email_task: CustomerEmailTask):  
     
    
        
    if  (country and customer_email_task 
        and customer_email_task.customer_email_campaign
        and len(customer_email_task.customer_email_campaign.ce_sender_email_campaign_countries)
    ):
        senders = [stc.customer_email_sender 
                       for stc in customer_email_task.customer_email_campaign.ce_sender_email_campaign_countries 
                       if stc.country_id == country.id]
        if not len(senders):
            senders = [sender for sender in customer_email_task.customer_email_campaign.ce_sender_email_campaign_countries 
                       if sender.country_id == None]
            
        sender = random.choice(senders) if len(senders) else None
        if sender: 
            return sender
        
    if country and country.email_sender_json :
        return CustomerEmailSender(name=country.email_sender_json["name"], email=country.email_sender_json["email"]) 
    return  CustomerEmailSender(name="Maria", email="<EMAIL>")
 
def generate_email_items(data_dicts, customer_email_task,params: CustomerEmailItemGenerateParams):
    email_items = []
    for res_item in data_dicts:
        customer = res_item['customer'] if 'customer' in  res_item  else None
        country = res_item['country'] if 'country' in  res_item  else None
        random_subscribed_customer = res_item['random_subscribed_customer'] if 'random_subscribed_customer' in  res_item  else None
        prev_email_item = res_item['prev_email_item'] if 'prev_email_item' in  res_item  else None
        prev_email_sender = res_item['prev_email_sender'] if 'prev_email_sender' in  res_item  else None
        
        sender = prev_email_sender if prev_email_sender else get_sender(country, customer_email_task)
        language = get_language(customer, country, customer_email_task)
        
        cur_customer_item =  CustomerEmailItem(
                customer_id=customer.id,
                customer=customer,
                country=country,  
                sender_email=sender.email,
                language=language,
                country_id=country.id if country else None,
                customer_email_task_id= customer_email_task.id, 
            )
        cur_customer_item.sender = sender
        cur_customer_item.customer_email_task = customer_email_task 
        cur_customer_item.generation_params = params
        cur_customer_item.random_subscribed_customer = random_subscribed_customer  
        cur_customer_item.prev_email_item =prev_email_item 
        email_items.append(
           cur_customer_item 
        )
    return email_items

def generate_no_duplicate_subquery(db, email_number):
    c = aliased(Customer)
    ei2 = aliased(CustomerEmailItem)
    et2 = aliased(CustomerEmailTask)
    no_email_subquery = (
        db.query(
            c.id.label('customer_id'),
            func.count(et2.id).label('email_count')
        )
        .join(ei2, c.id == ei2.customer_id, isouter=True)
        .join(et2, and_(
                et2.id == ei2.customer_email_task_id, 
                et2.email_number == email_number,
                not_(ei2.sent_at.is_(None))
            ), isouter=True)
        .group_by(c.id)
        .having(func.count(et2.id) == 0)
        .subquery()
    )
    return no_email_subquery