 
from datetime import timedelta
from app import crud
from app.config.settings import get_settings
from app.models.account_information import AccountInformation
from app.models.country import Country
from app.models.customer import Customer
from app.models.customer_email_item import CustomerEmailItem
from app.models.customer_email_task import CustomerEmailTask
from app.models.subscription import Subscription
from datetime import datetime
import jwt
from sqlalchemy.orm import Session, aliased, contains_eager
from sqlalchemy import and_, func, text, cast, Date
from sqlalchemy.sql import or_, and_, not_
from tzlocal import get_localzone

from app.schemas.customer_email_item import CustomerEmailItemGenerateParams

from app.schemas.invitation import InvitationCreate
from app.services.customer_email.utils import (
    get_local_timezone_offset,
    valid_subscription_join,
    generate_random_subscribed_user_query,
    generate_email_items,
    generate_no_duplicate_subquery
)
settings = get_settings()
def send_to_when_registered_invitation(self, db: Session, params: CustomerEmailItemGenerateParams):
    query = generate_query_registered_invitation(self, db,params ) 
    results = query.all() 
    data_dicts = [{
        "customer": r[0],
        "country": r[1], 
    } for r in results] 
    return generate_email_items(data_dicts, self, params)


def generate_query_registered_invitation(self, db: Session, params: CustomerEmailItemGenerateParams):
    no_email_7_subquery = generate_no_duplicate_subquery(db, 7)
    
    get_time_expr = func.extract(
        'hour', func.convert_tz(params.current_datetime, get_local_timezone_offset(), func.coalesce(Customer.timezone_offset, Country.timezone_offset, '+00:00'))
    ) == 8
    
    query = (
        db.query(
            Customer,
            Country
        )
        .join(Customer.account_information)
        .join(Country, Country.id == Customer.country_id, isouter=True)
        .join(no_email_7_subquery, no_email_7_subquery.c.customer_id == Customer.id)
        .join(Subscription, 
              valid_subscription_join(Subscription, Customer, params.current_datetime), 
              isouter=True)
        .filter(
            Customer.removed_from_mailing_list_at.is_(None),
            Customer.deleted_at.is_(None),
            Customer.to_delete_at.is_(None),
            Subscription.id.is_(None),
            # AccountInformation.account_type_id == params.account_type_id,
            get_time_expr
        ).order_by(Customer.created_at.asc()).options(contains_eager(Customer.account_information))
    )
    if params.chunk_size_per_task: 
        query = query.limit(params.chunk_size_per_task)
    return query
 
def registered_invitation_pre_send_process(self, db: Session,  item: CustomerEmailItem):
    data_token = {
        "token_type": "invitation",
        "customer": {
            "id": item.customer.id,
            "auth_user_id": item.customer.auth_user_id,
        }, 
        "expiration_date": (datetime.now() + timedelta(days=7)).isoformat(),
    }
    token = jwt.encode(
        data_token, settings.SECRET_KEY, algorithm="HS256"
    )
    settings_url = f'{settings.PVGIS_UI_URL}/{item.language}?token={token}'
    item.additional_template_vars = {
        'firstname': None,
        'lastname': None,
        'invitation_url':  settings_url
    }
    item.additional_template_vars['pvgis_com_url'] = settings_url
    item.additional_template_vars['pvgis_24_url'] = settings_url