from datetime import timedelta
from app.models.account_information import AccountInformation
from app.models.country import Country
from app.models.customer import Customer
from app.models.subscription import Subscription
from app.models.customer_email_item import CustomerEmailItem
from app.models.customer_email_task import CustomerEmailTask
from sqlalchemy.orm import Session, aliased, contains_eager
from sqlalchemy import and_, func, text, cast, Date
from sqlalchemy.sql import or_, and_, not_
from tzlocal import get_localzone

from app.schemas.customer_email_item import CustomerEmailItemGenerateParams

from app.services.customer_email.utils import (
    get_local_timezone_offset,
    valid_subscription_join,
    generate_random_subscribed_user_query,
    generate_email_items,
    generate_no_duplicate_subquery
)


def generate_query_highlighting_features(self, db: Session,     params: CustomerEmailItemGenerateParams):
    # Envoyé 5 jours après l'email 2
    no_email_3_subquery = generate_no_duplicate_subquery(db, 3)
    testimony_customer_subquery = generate_random_subscribed_user_query(db, params.current_datetime)
    testimony_customer = aliased(Customer)
    get_time_expr = func.extract(
        'hour', func.convert_tz(params.current_datetime, get_local_timezone_offset(),func.coalesce(Customer.timezone_offset,Country.timezone_offset,'+00:00'))
    ) == 16
    today_minus_5 = params.current_datetime - timedelta(days=5)
    # Main query
    query = (
        db.query(
            Customer,
            Country,
            testimony_customer,
            CustomerEmailItem
        )
        .join(testimony_customer_subquery, testimony_customer_subquery.c.country_id == Customer.country_id, isouter=True)
        .join(testimony_customer,testimony_customer.id == testimony_customer_subquery.c.customer_id ,  isouter=True)
        .join(Customer.account_information)
        .join(Country, Country.id == Customer.country_id, isouter=True)
        .join(CustomerEmailItem, CustomerEmailItem.customer_id == Customer.id )
        .join(CustomerEmailTask, and_(
                CustomerEmailTask.id == CustomerEmailItem.customer_email_task_id, 
                CustomerEmailTask.email_number == 2,
                not_(CustomerEmailItem.sent_at.is_(None))
            )
        )
        .join(no_email_3_subquery, no_email_3_subquery.c.customer_id == Customer.id)
        .join(Subscription,
              valid_subscription_join(Subscription, Customer, params.current_datetime)
              , isouter=True)
        .filter(
            Customer.removed_from_mailing_list_at.is_(None),
            Customer.deleted_at.is_(None),
            Customer.to_delete_at.is_(None),
            Subscription.id.is_(None),
            AccountInformation.account_type_id == params.account_type_id,
            cast(CustomerEmailItem.sent_at, Date) <= cast(today_minus_5, Date),
            get_time_expr
        ).options(contains_eager(Customer.account_information))
    )
    
    if params.chunk_size_per_task:
        query = query.order_by(Customer.created_at.asc()).limit(params.chunk_size_per_task)
    return query
def send_to_when_highlighting_features(self, db: Session,     params: CustomerEmailItemGenerateParams):
    query = generate_query_highlighting_features(self, db, params)
    results = query.all() 
    data_dicts = [{
        "customer": r[0],
        "country": r[1],
        "random_subscribed_customer": r[2],
        "prev_email_item": r[3]
    } for r in results] 
    return generate_email_items(data_dicts, self, params)
 