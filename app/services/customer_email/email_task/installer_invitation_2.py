 
from datetime import timedelta
from app.models.account_information import AccountInformation
from app.models.country import Country
from app.models.customer import Customer
from app.models.customer_email_item import CustomerEmailItem
from sqlalchemy.orm import Session, aliased, contains_eager
from sqlalchemy import and_, func, text, cast, Date
from sqlalchemy.sql import or_, and_, not_
from tzlocal import get_localzone

from app.models.customer_email_sender import CustomerEmailSender
from app.schemas.customer_email_item import CustomerEmailItemGenerateParams

from app.services.customer_email.utils import (
    get_local_timezone_offset,
    valid_subscription_join,
    generate_random_subscribed_user_query,
    generate_email_items,
    generate_no_duplicate_subquery
)

def send_to_installer_2(self, db: Session, params: CustomerEmailItemGenerateParams):
    query = generate_query_installer_2(self, db,params ) 
    results = query.all() 
    data_dicts = [{
        "customer": r[0],
        "country": r[1],
        "prev_email_sender":r[2]
    } for r in results] 
    return generate_email_items(data_dicts, self, params)


def generate_query_installer_2(self, db: Session, params: CustomerEmailItemGenerateParams):
    no_email_7_subquery = generate_no_duplicate_subquery(db, 7)
    
    get_time_expr = func.extract(
        'hour', func.convert_tz(params.current_datetime, get_local_timezone_offset(), func.coalesce(Customer.timezone_offset, Country.timezone_offset, '+00:00'))
    ) == 8
    today_minus_3 = params.current_datetime - timedelta(hours=72)
    
    query = (
        db.query(
            Customer,
            Country,
            CustomerEmailSender
        )
        .join(Customer.account_information)
        .join(Country, Country.id == Customer.country_id, isouter=True)
        .join(CustomerEmailItem, CustomerEmailItem.customer_id == Customer.id )
        .join(CustomerEmailSender, CustomerEmailSender.email == CustomerEmailItem.sender_email, isouter=True)
        # .join(CustomerEmailTask, and_(
        #         CustomerEmailTask.id == CustomerEmailItem.customer_email_task_id, 
        #         CustomerEmailTask.email_number == 7,
        #         not_(CustomerEmailItem.sent_at.is_(None))
        #     )
        # )
        .join(no_email_7_subquery, no_email_7_subquery.c.customer_id == Customer.id)
        .filter(
            Customer.removed_from_mailing_list_at.is_(None),
            Customer.deleted_at.is_(None),
            Customer.to_delete_at.is_(None),
            Customer.auth_user_id.is_(None),
            AccountInformation.account_type_id == 1,  
            AccountInformation.professional_category_id.between(1, 4),  
            cast(CustomerEmailItem.sent_at, Date) <= cast(today_minus_3, Date),
            get_time_expr  
        ).options(contains_eager(Customer.account_information))
    )
    if params.chunk_size_per_task:
        query = query.order_by(Customer.created_at.asc()).limit(params.chunk_size_per_task)
    return query
