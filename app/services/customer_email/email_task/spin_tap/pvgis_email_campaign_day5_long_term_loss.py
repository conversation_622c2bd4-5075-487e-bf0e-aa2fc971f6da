 
from datetime import timedelta
from app import crud
from app.config.settings import get_settings
from app.models.account_information import AccountInformation
from app.models.country import Country
from app.models.customer import Customer
from app.models.customer_email_item import CustomerEmailItem
from app.models.customer_email_task import CustomerEmailTask
from app.models.subscription import Subscription
from sqlalchemy.orm import Session, aliased, contains_eager
from sqlalchemy import and_, func, text, cast, Date
from sqlalchemy.sql import or_, and_, not_
from tzlocal import get_localzone

from app.schemas.customer_email_item import CustomerEmailItemGenerateParams

from app.services.customer_email.utils import (
    get_local_timezone_offset,
    valid_subscription_join,
    generate_random_subscribed_user_query,
    generate_email_items,
    generate_no_duplicate_subquery
)
settings = get_settings()
def send_to_spin_3(self, db: Session, params: CustomerEmailItemGenerateParams):
    query = generate_query_spin_3(self, db,params ) 
    results = query.all() 
    data_dicts = [{
        "customer": r[0],
        "country": r[1], 
    } for r in results] 
    items = generate_email_items(data_dicts, self, params)
    
    for item in items:
        item.additional_template_vars =  {
            "firstname": item.customer.first_name, 
            "lastname": item.customer.last_name, 
            "invitation_url":  f"{settings.PVGIS_UI_URL}/pvgis24/app/{item.customer.id}/subscription"
    }
    return items


def generate_query_spin_3(self, db: Session, params: CustomerEmailItemGenerateParams):
    no_email_22_subquery = generate_no_duplicate_subquery(db, 22)
    
    get_time_expr = func.extract(
        'hour', func.convert_tz(params.current_datetime, get_local_timezone_offset(), func.coalesce(Customer.timezone_offset, Country.timezone_offset, '+00:00'))
    ) == 8
    
    # today_minus_5 = params.current_datetime - timedelta(days=5)
    today_minus_5 = params.current_datetime - timedelta(days=2)
    
    query = (
        db.query(
            Customer,
            Country
        )
        .join(Customer.account_information)
        .join(Country, Country.id == Customer.country_id, isouter=True)
        .join(CustomerEmailItem, CustomerEmailItem.customer_id == Customer.id )
        .join(CustomerEmailTask, and_(
                CustomerEmailTask.id == CustomerEmailItem.customer_email_task_id, 
                CustomerEmailTask.email_number == 21,
                not_(CustomerEmailItem.sent_at.is_(None))
            )
        )
        .join(no_email_22_subquery, no_email_22_subquery.c.customer_id == Customer.id)
        .join(Subscription, 
              valid_subscription_join(Subscription, Customer, params.current_datetime), 
              isouter=True)
        .filter(
            Customer.removed_from_mailing_list_at.is_(None),
            Customer.deleted_at.is_(None),
            Customer.to_delete_at.is_(None),
            or_(
                Subscription.id.is_(None),
                and_(
                    Subscription.id.isnot(None),
                    Subscription.product_json.is_(None)
                )
            ),
            AccountInformation.account_type_id == 2,  
            AccountInformation.support == "CHECK_A_QUOTE_OR_AN_OFFER",  
            cast(CustomerEmailItem.sent_at, Date) <= cast(today_minus_5, Date), 
            get_time_expr  
        ).options(contains_eager(Customer.account_information))
    )
    if params.chunk_size_per_task: 
        query = query.order_by(Customer.created_at.asc()).limit(params.chunk_size_per_task)
    return query