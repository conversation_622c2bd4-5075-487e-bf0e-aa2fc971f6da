 
from datetime import timedelta
from app import crud
from app.config.settings import get_settings
from app.models import account_information
from app.models.account_information import AccountInformation
from app.models.country import Country
from app.models.customer import Customer
from app.models.customer_email_item import CustomerEmailItem
from app.models.customer_email_task import CustomerEmailTask
from app.models.subscription import Subscription
from sqlalchemy.orm import Session, aliased, contains_eager
from sqlalchemy import and_, func, text, cast, Date
from sqlalchemy.sql import or_, and_, not_
from tzlocal import get_localzone

from app.schemas.customer_email_item import CustomerEmailItemGenerateParams

from app.schemas.invitation import InvitationCreate
from app.services.customer_email.utils import (
    get_local_timezone_offset,
    valid_subscription_join,
    generate_random_subscribed_user_query,
    generate_email_items,
    generate_no_duplicate_subquery
)
settings = get_settings()
def send_to_installer(self, db: Session, params: CustomerEmailItemGenerateParams):
    query = generate_query_installer(self, db,params ) 
    results = query.all() 
    data_dicts = [{
        "customer": r[0],
        "country": r[1], 
    } for r in results] 
    items = generate_email_items(data_dicts, self, params)
    
    # TODO: to be moved to a better place?
    # for item in items:
    #     item.additional_template_vars =  {
    #         "firstname": item.customer.first_name, 
    #         "lastname": item.customer.last_name, 
    #         "invitation_url":  f"{settings.PVGIS_UI_URL}/pvgis24/app/{item.customer.id}/subscription"
    # }
    return items


def generate_query_installer(self, db: Session, params: CustomerEmailItemGenerateParams):
    no_email_6_subquery = generate_no_duplicate_subquery(db, 6)
    
    get_time_expr = func.extract(
        'hour', func.convert_tz(params.current_datetime, get_local_timezone_offset(), func.coalesce(Customer.timezone_offset, Country.timezone_offset, '+00:00'))
    ) == 8
    
    query = (
        db.query(
            Customer,
            Country
        )
        .join(Customer.account_information)
        .join(Country, Country.id == Customer.country_id, isouter=True)
        .join(no_email_6_subquery, no_email_6_subquery.c.customer_id == Customer.id)
        .filter(
            Customer.removed_from_mailing_list_at.is_(None),
            Customer.deleted_at.is_(None),
            Customer.to_delete_at.is_(None),
            Customer.auth_user_id.is_(None),
            AccountInformation.account_type_id == 1,  
            AccountInformation.professional_category_id.between(1, 4),  
            get_time_expr  
        ).options(contains_eager(Customer.account_information))
    )
    if params.chunk_size_per_task: 
        query = query.order_by(Customer.created_at.asc()).limit(params.chunk_size_per_task)
    return query

def installer_invitation_pre_send_process(self, db: Session,  item: CustomerEmailItem):
    
    invitation_in = InvitationCreate(**{
                        "customer_id": item.customer.id,
                        "email": item.customer.email
                    })
    results = crud.invitation.send_invitation(db,invitation_in,user_id=None,effective_send_mail=False, customer=item.customer)
    item.additional_template_vars = results["template_vars"]
    item.additional_template_vars['pvgis_com_url'] = results["template_vars"]["invitation_url"]
    item.additional_template_vars['pvgis_24_url'] = results["template_vars"]["invitation_url"]
    # item.additional_template_vars = {
    #     'firstname': None,
    #     'lastname': None,
    #     'invitation_url':  "https://dev.pvgis.com/invitation?data=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpbnZpdGF0aW9uX2lkIjozOH0.Lwu56EDNNckbXfU3nQiRf0ywjOTc0rXYWPFB-JYQvTY"
    # }