import requests
from app.config.settings import get_settings
from typing import Dict, Any, List
from app import crud
import json
import base64

settings = get_settings()

def get_basic_auth_header(username: str, password: str) -> str:
    credentials = f"{username}:{password}"
    base64_credentials = base64.b64encode(credentials.encode('utf-8')).decode('utf-8')
    return f"Basic {base64_credentials}"

def get_data_from_elastic(url: str, query: Any):
    try:
        token=get_basic_auth_header(settings.ELASTIC_USERNAME, settings.ELASTIC_PASSWORD)
        _url = settings.LOGSTASH_URL + url
        
        response = requests.post(
            url=_url,
            data=json.dumps(query),
            headers={
                "Authorization": token,
                "Content-Type": "application/json"
            },
        )
        
        response.raise_for_status() 
        
        if response.status_code == 200:
            print("Fetch elastic result successful!")
            return response.json()
        else:
            print(f"Request failed with status code: {response.status_code}")
            return None
    except Exception as e:
        print(f"Request failed: {e}")
        return  