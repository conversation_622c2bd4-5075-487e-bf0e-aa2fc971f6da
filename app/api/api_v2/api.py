from fastapi import APIRouter

from app.api.api_v2.endpoints import (
    account_customer,
    customer,
    ip_location,
    monitoring,
    product,
    pvgis,
    features,
    product_features,
    subscription,
    subscription_user,
    simulation,
    simulation_item,
    payment_method,
    subscription_payment_receipt,
    subscription_payment_transaction,
    cart,
    professional_category,
    school_category,
    account_type,
    account_information,
    account_notification_setting,
    notification_setting,
    customer_payment_information,
    login,
    contact_platform,
    customer_contact_platform_information,
    google_analytics,
    stripe_webhook,
    bulk_upload,
    invitation,
    city,
    country,
    simulation_listing,
    monotonous_electricity_consumption,
    monotonous_electricity_consumption_daily,
    download_page,
    customer_email,
    continent,
    testimony,
    region, 
    export_data,
    brevo,
    brevo_import,
)


api_router = APIRouter()

api_router.include_router(pvgis.router, prefix="/api", tags=["Pvgis"])
api_router.include_router(download_page.router, prefix="/download_page", tags=["Download Page"])
api_router.include_router(
    ip_location.router, prefix="/ip-location", tags=["Ip Location"]
)
api_router.include_router(monitoring.router, prefix="/monitoring", tags=["Monitoring"])
api_router.include_router(login.router, prefix="/auth", tags=["Auth"])
api_router.include_router(product.router, prefix="/products", tags=["Products"])
api_router.include_router(customer.router, prefix="/customers", tags=["Customer"])
api_router.include_router(customer_email.router, prefix="/customer-emails", tags=["Customer Emails"])
api_router.include_router(features.router, prefix="/features", tags=["Features"])
api_router.include_router(
    product_features.router, prefix="/product_features", tags=["Product Features"]
)
api_router.include_router(
    subscription.router, prefix="/subscriptions", tags=["Subscription"]
)
api_router.include_router(
    subscription_user.router, prefix="/subscription_users", tags=["Subscription User"]
)
api_router.include_router(simulation.router, prefix="/simulations", tags=["Simulation"])
api_router.include_router(
    simulation_item.router, prefix="/simulation_items", tags=["Simulation Item"]
)
api_router.include_router(
    payment_method.router, prefix="/payment_methods", tags=["Payment Method"]
)
api_router.include_router(
    subscription_payment_transaction.router,
    prefix="/subscription_payment_transactions",
    tags=["Subscription Payment Transaction"],
)

api_router.include_router(
    subscription_payment_receipt.router,
    prefix="/subscription_payment_receipts",
    tags=["Subscription Payment Receipt"],
)

api_router.include_router(
    account_information.router,
    prefix="/account_information",
    tags=["Account Information"],
)
api_router.include_router(
    account_type.router,
    prefix="/account_type",
    tags=["Account Type"],
)
api_router.include_router(
    professional_category.router,
    prefix="/professional_category",
    tags=["Professional Category"],
)
api_router.include_router(
    school_category.router,
    prefix="/school_category",
    tags=["School Category"],
)

api_router.include_router(
    cart.router,
    prefix="/cart",
    tags=["Cart"],
)

api_router.include_router(
    notification_setting.router,
    prefix="/notification_setting",
    tags=["Notification Setting"],
)

api_router.include_router(
    account_notification_setting.router,
    prefix="/account_notification_setting",
    tags=["Account Notification Setting"],
)

api_router.include_router(
    customer_payment_information.router,
    prefix="/customer_payment_information",
    tags=["Customer Payment Information"],
)

api_router.include_router(
    contact_platform.router,
    prefix="/contact_platform",
    tags=["Contact platform"],
)

api_router.include_router(
    customer_contact_platform_information.router,
    prefix="/customer_contact_platform_information",
    tags=["Customer contact platform information"],
)

api_router.include_router(
    account_customer.router,
    prefix="/account_customer",
    tags=["Account Customer"],
)

api_router.include_router(
    google_analytics.router,
    prefix="/google_analytics",
    tags=["Google Analytics"],
)

api_router.include_router(
    stripe_webhook.router,
    prefix="/stripe_webhook",
    tags=["Stripe Webhook"],
)

api_router.include_router(
    bulk_upload.router, 
    prefix="/bulk_upload", 
    tags=["bulk_upload"]
)

api_router.include_router(
    invitation.router, 
    prefix="/invitations", 
    tags=["Invitations"]
)

api_router.include_router(
    city.router, 
    prefix="/city", 
    tags=["city"]
)

api_router.include_router(
    country.router, 
    prefix="/country", 
    tags=["country"]
)

api_router.include_router(
    simulation_listing.router, 
    prefix="/simulation_listing", 
    tags=["simulation_listing"]
)

api_router.include_router(
    continent.router, 
    prefix="/continent", 
    tags=["continent"]
)

api_router.include_router(
    testimony.router,
    prefix="/testimony",
    tags=["testimony"]
)

api_router.include_router(
    monotonous_electricity_consumption.router, 
    prefix="/monotonous_electricity_consumption", 
    tags=["monotonous_electricity_consumption"]
)
api_router.include_router(
    monotonous_electricity_consumption_daily.router, 
    prefix="/monotonous_electricity_consumption_daily", 
    tags=["monotonous_electricity_consumption_daily"]
)

api_router.include_router(
    region.router,
    prefix="/regions",
    tags=["Regions"]
)

api_router.include_router(
    brevo.router, 
    prefix="/brevo", 
    tags=["Brevo"]
)


api_router.include_router(
    export_data.router,
    prefix="/export_data",
    tags=["Export Data"]
)

api_router.include_router(
    brevo_import.router,
    prefix="/brevo_import",
    tags=["Import Contact to brevo"]
)