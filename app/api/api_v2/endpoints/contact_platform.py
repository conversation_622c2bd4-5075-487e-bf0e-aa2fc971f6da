from typing import Any
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.api.api_v2.deps import get_user
from app.config.db.database import get_session
from app.schemas.contact_platform import ContactPlatformCreate, ContactPlatformUpdate

from app.crud.crud_contact_platform import contact_platform
from app import schemas
from app import crud

router = APIRouter()


import ast

@router.get("")
def read_contact_platform(
    db: Session = Depends(get_session),
    offset: int = 0,
    limit: int = 100,
    where: str = "",
    order_by: str = "id",
    order: str = "ASC",
    relations: str="[]",
    base_columns: str = "['created_at']"
) -> Any:
    wheres = []
    base_columns = ast.literal_eval(base_columns)
    relations = ast.literal_eval(relations)
    if where is not None and where != "" and where != []:
        wheres += ast.literal_eval(where)
    count = crud.contact_platform.get_count_where_array(
        db=db,
        where=wheres,
    )
    contact_platform_list = crud.contact_platform.get_multi_where_array_v2(
        db=db,
        where=wheres,
        order_by=order_by,
        order=order,
        limit=limit,
        skip=offset,
        relations=relations,
        base_columns=base_columns,
    )

    return {
        "count": count,
        "data": contact_platform_list,
    }


@router.post("", response_model=Any)
def create_contact_platform(
    *,
    db: Session = Depends(get_session),
    contact_platform_in: schemas.ContactPlatformCreate,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Create new contact_platform.
    """
    data = crud.contact_platform.create(db=db, obj_in=contact_platform_in)

    return data


@router.get("/{id}")
def read_contact_platform(
    *,
    db: Session = Depends(get_session),
    id: int,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Get contact_platform by ID.
    """
    data = crud.contact_platform.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="ContactPlatform not found")

    return data


@router.put("/{id}")
def update_contact_platform(
    *,
    db: Session = Depends(get_session),
    id: int,
    contact_platform_in: schemas.ContactPlatformUpdate,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Update an contact_platform.
    """
    data = crud.contact_platform.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="ContactPlatform not found")

    data = crud.contact_platform.update(db=db, db_obj=data, obj_in=contact_platform_in)

    return data


@router.delete("/{id}", response_model=Any)
def delete_contact_platform(
    *,
    db: Session = Depends(get_session),
    id: int,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Delete an contact_platform.
    """

    data = crud.contact_platform.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="ContactPlatform not found")

    return crud.contact_platform.soft_delete(db=db, id=id)
