from typing import Any
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.api.api_v2.deps import get_user
from app.config.db.database import get_session
from app.schemas.monotonous_electricity_consumption_daily import MonotonousElectricityConsumptionDailyCreate,MonotonousElectricityConsumptionDailyUpdate
from app.services.daily_consumtion_service import calculate_consumption_sum,calculate_consumption_percentage
from app.crud.crud_monotonous_electricity_consumption_daily import monotonous_electricity_consumption_daily
from app import schemas
from app import crud

router = APIRouter()

import ast

@router.get("")
def read_monotonous_electricity_consumption_daily(
    db: Session = Depends(get_session),
    offset: int = 0,
    limit: int = 100,
    where: str = "",
    order_by: str = "id",
    order: str = "ASC",
    relations: str="[]",
    base_columns: str = "['created_at']"
) -> Any:
    wheres = []
    base_columns = ast.literal_eval(base_columns)
    relations = ast.literal_eval(relations)
    if where is not None and where != "" and where != []:
        wheres += ast.literal_eval(where)
    count = crud.monotonous_electricity_consumption_daily.get_count_where_array(
        db=db,
        where=wheres,
    )
    monotonous_electricity_consumption_list = crud.monotonous_electricity_consumption_daily.get_multi_where_array_v2(
        db=db,
        where=wheres,
        order_by=order_by,
        order=order,
        limit=limit,
        skip=offset,
        relations=relations,
        base_columns=base_columns,
    )

    return {
        "count": count,
        "data": monotonous_electricity_consumption_list,
    }



@router.post("", response_model=Any)
def create_monotonous_electricity_consumption(
    *,
    db: Session = Depends(get_session),
    monotonous_electricity_consumption_in: schemas.MonotonousElectricityConsumptionDailyCreate,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Create new crud.monotonous_electricity_consumption.
    """
    if current_user and "id" in current_user:
        last_user_id = current_user["id"]
    else:
        last_user_id = None 
    print(f"Current user: {last_user_id}")  
    print(f"monotonous_electricity_consumption_in user: {monotonous_electricity_consumption_in}")  
    consumption_sum = calculate_consumption_sum(monotonous_electricity_consumption_in.residential_consumption_json)
    monotonous_electricity_consumption_in.residential_consumption_sum = consumption_sum
    consumption_percentage = calculate_consumption_percentage(
        monotonous_electricity_consumption_in.residential_consumption_json,
        consumption_sum
    )
    monotonous_electricity_consumption_in.residential_consumption_percentage_json = consumption_percentage
    data = crud.monotonous_electricity_consumption_daily.create(db=db, obj_in=monotonous_electricity_consumption_in,user_id=last_user_id )

    return data




@router.get("/{id}")
def read_monotonous_electricity_consumption(
    *,
    db: Session = Depends(get_session),
    id: int,
) -> Any:
    """
    Get monotonous_electricity_consumption by ID.
    """
    data = crud.monotonous_electricity_consumption_daily.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="monotonous_electricity_consumption_daily_not_found")

    return data


@router.put("/{id}")
def update_monotonous_electricity_consumption(
    *,
    db: Session = Depends(get_session),
    id: int,
    monotonous_electricity_consumption_in: schemas.MonotonousElectricityConsumptionDailyCreate,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Update an crud.monotonous_electricity_consumption.
    """
    data = crud.monotonous_electricity_consumption_daily.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="monotonous_electricity_consumption_daily_not_found")
    
    if current_user and "id" in current_user:
        last_user_id = current_user["id"]
    else:
        last_user_id = None 
        
    consumption_sum = 0

  
    if monotonous_electricity_consumption_in.residential_consumption_json:
        consumption_sum = calculate_consumption_sum(monotonous_electricity_consumption_in.residential_consumption_json)
        monotonous_electricity_consumption_in.residential_consumption_sum = consumption_sum

        consumption_percentage = calculate_consumption_percentage(
            monotonous_electricity_consumption_in.residential_consumption_json,
            consumption_sum
        )

        monotonous_electricity_consumption_in.residential_consumption_percentage_json = consumption_percentage
    data = crud.monotonous_electricity_consumption_daily.update(db=db, db_obj=data, obj_in=monotonous_electricity_consumption_in,user_id=last_user_id )

    return data


@router.delete("/{id}", response_model=Any)
def delete_monotonous_electricity_consumption(
    *,
    db: Session = Depends(get_session),
    id: int,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Delete an crud.monotonous_electricity_consumption.
    """

    data = crud.monotonous_electricity_consumption_daily.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="monotonous_electricity_consumption_daily_not_found")
    
    if current_user and "id" in current_user:
        last_user_id = current_user["id"]
    else:
        last_user_id = None 

    return crud.monotonous_electricity_consumption_daily.soft_delete(db=db, id=id,user_id=last_user_id )