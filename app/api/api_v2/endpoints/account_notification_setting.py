from ast import List
from typing import Any
from fastapi import APIRouter, Depends, HTTPException
from fastapi.encoders import jsonable_encoder
from requests import Session
from app.api.api_v2.deps import get_user,get_optional_customer
from app.config.db.database import get_session
from app.schemas.account_notification_setting import (
    AccountNotificationSettingBase,
    AccountNotificationSetting,
    AccountNotificationSettingCreate,
    AccountNotificationSettingToggle,
    ResponseAccountNotificationSetting,
    AccountNotificationSettingUpdate,
)

from app.crud.crud_account_notification_setting import account_notification_setting
from app.crud.crud_features import features
from app.utils.utils import to_dict
from app import schemas
from app import crud

router = APIRouter()


import ast

@router.get("")
def read_account_notification_settings(
    db: Session = Depends(get_session),
    offset: int = 0,
    limit: int = 100,
    where: str = "",
    order_by: str = "id",
    order: str = "ASC",
    relations: str="['notification_setting{key}']",
    base_columns: str = "['created_at']",
    current_user: Any = Depends(get_user),
    customer:Any=Depends(get_optional_customer),
) -> Any:
    wheres = [
        {
            "key": "customer_id",
            "value": customer.id,
            "operator": "=="
        }
    ]

    base_columns = ast.literal_eval(base_columns)
    relations = ast.literal_eval(relations)
    if where is not None and where != "" and where != []:
        wheres += ast.literal_eval(where)
    count = crud.account_notification_setting.get_count_where_array(
        db=db,
        where=wheres,
    )
    account_notification_setting_list = crud.account_notification_setting.get_multi_where_array_v2(
        db=db,
        where=wheres,
        order_by=order_by,
        order=order,
        limit=limit,
        skip=offset,
        relations=relations,
        base_columns=base_columns,
    )

    return {
        "count": count,
        "data": account_notification_setting_list,
    }


@router.get("/key_list")
def read_account_notification_settings_list(
    db: Session = Depends(get_session),
    current_user: Any = Depends(get_user),
    customer:Any=Depends(get_optional_customer)
) -> Any:
    data = crud.account_notification_setting.get_key_list(
        db=db,
        customer_id=customer.id
    )
    return data


@router.post("", response_model=Any)
def create_account_notification_setting(
    *,
    db: Session = Depends(get_session),
    account_notification_setting_in: schemas.AccountNotificationSettingCreate,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Create new account_notification_setting.
    """
    data = crud.account_notification_setting.create(
        db=db, obj_in=account_notification_setting_in
    )

    return data


@router.post("/toggle_by_key", response_model=Any)
def toggle_account_notification_setting_by_key(
    *,
    db: Session = Depends(get_session),
    obj_in: AccountNotificationSettingToggle,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    toggle account_notification_setting by key.
    """
    if obj_in.checked:
        data = crud.account_notification_setting.add_by_key(
            db=db, key=obj_in.key, auth_user_id=current_user["id"]
        )
    else:
        data = crud.account_notification_setting.remove_by_key(
            db=db, key=obj_in.key, auth_user_id=current_user["id"]
        )

    return data


@router.get("/{id}")
def read_account_notification_setting(
    *,
    db: Session = Depends(get_session),
    id: int,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Get account_notification_setting by ID.
    """
    data = crud.account_notification_setting.get(db=db, id=id)

    if not data:
        raise HTTPException(
            status_code=404, detail="AccountNotificationSetting not found"
        )

    return data


@router.put("/{id}", response_model=AccountNotificationSettingUpdate)
def update_account_notification_setting(
    *,
    db: Session = Depends(get_session),
    id: int,
    account_notification_setting_in: schemas.AccountNotificationSettingUpdate,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Update an account_notification_setting.
    """
    data = crud.account_notification_setting.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="Issue not found")

    data = crud.account_notification_setting.update(
        db=db, db_obj=data, obj_in=account_notification_setting_in
    )

    return to_dict(data)


@router.delete("/{id}", response_model=Any)
def delete_account_notification_setting(
    *,
    db: Session = Depends(get_session),
    id: int,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Delete an account_notification_setting.
    """

    data = crud.account_notification_setting.get(db=db, id=id)

    if not data:
        raise HTTPException(
            status_code=404, detail="AccountNotificationSetting not found"
        )

    return to_dict(crud.account_notification_setting.soft_delete(db=db, id=id))
