import os
from fastapi import APIRouter, Depends, Request
from sqlalchemy.orm import Session

from app.config.db.database import get_session
from app.crud.ip_location_service import get_location_from_ip
from app.crud.ip_location_service import get_google_place_ids_by_city_ip
from app import crud

router = APIRouter()


@router.get("/{ip}")
def get_request_ip_location(
    *,
    db: Session = Depends(get_session),
    ip: str
):
    return crud.ip_location.get_location_by_ip(db=db, ip=ip)


@router.get("/city/{ip}")
def get_city_by_ip(db: Session = Depends(get_session),ip: str = None):
    return get_google_place_ids_by_city_ip(db, ip)