from typing import Any
from app.utils.const import DOWNLOAD_PAGE_SECRET
from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import HTMLResponse, FileResponse
from app.config.settings import get_settings

router = APIRouter()
settings = get_settings()

@router.get("/{secret}", response_class=HTMLResponse)
async def download_page(
    *,
    secret: str,
) -> Any:
    if secret != DOWNLOAD_PAGE_SECRET:
        raise HTTPException(status_code=401)

    html_content = """
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Download Page</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    background-color: #f9f9f9;
                    margin: 0;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    height: 100vh;
                }
                h1 {
                    color: #333;
                }
                .button-container {
                    margin-top: 20px;
                    display: flex;
                    gap: 20px;
                }
                button {
                    background-color: #007BFF;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    font-size: 16px;
                    cursor: pointer;
                    border-radius: 5px;
                    transition: background-color 0.3s;
                }
                button:hover {
                    background-color: #0056b3;
                }
                #loading {
                    display: none;
                    font-size: 16px;
                    color: #555;
                    margin-top: 20px;
                }
            </style>
            <script>
                function startDownload(endpoint) {
                    const loading = document.getElementById("loading");
                    loading.style.display = "inline";
                    const link = document.createElement("a");
                    link.href = endpoint;
                    link.click();
                    setTimeout(() => {
                        loading.style.display = "none";
                    }, 2000); // Adjust timeout if needed
                }
            </script>
        </head>
        <body>
            <h1>Download Excel Files</h1>
            <div class="button-container">
                <button onclick="startDownload('/simulation_items/get_excel_file/by_simulation_type/?simulation_type=all')">Download Simulation</button>
                <button onclick="startDownload('/customers/all_subscription_file')">Download Subscription</button>
                <button onclick="startDownload('/customers/email_campaign_file')">Download PVGIS Mail Report</button>
            </div>
            <div id="loading">Loading...</div>
        </body>
        </html>
    """

    return HTMLResponse(content=html_content)
