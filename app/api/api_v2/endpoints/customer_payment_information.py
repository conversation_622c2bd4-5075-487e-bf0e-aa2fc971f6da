from ast import List
from typing import Any
from fastapi import APIRouter, Depends, HTTPException
from fastapi.encoders import jsonable_encoder
from requests import Session
from app.api.api_v2.deps import get_user
from app.config.db.database import get_session
from app.schemas.customer_payment_information import (
    CustomerPaymentInformationBase,
    CustomerPaymentInformation,
    CustomerPaymentInformationCreate,
    ResponseCustomerPaymentInformation,
    CustomerPaymentInformationUpdate,
)

from app.crud.crud_features import features
from app.utils.utils import to_dict
from app import schemas
from app import crud

router = APIRouter()


import ast

@router.get("")
def read_customer_payment_informations(
    db: Session = Depends(get_session),
    offset: int = 0,
    limit: int = 100,
    where: str = "",
    order_by: str = "id",
    order: str = "ASC",
    relations: str="[]",
    base_columns: str = "['created_at']"
) -> Any:

    return {
        "count": 0,
        "data": None,
    }


@router.post("", response_model=Any)
def create_customer_payment_information(
    *,
    db: Session = Depends(get_session),
    customer_payment_information_in: schemas.CustomerPaymentInformationCreate,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Create new customer_payment_information.
    """
    
    return None

@router.post("/create_or_update", response_model=Any)
def create_customer_payment_information(
    *,
    db: Session = Depends(get_session),
    customer_payment_information_in: schemas.CustomerPaymentInformationUpdate,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Create new customer_payment_information.
    """
    

    return None

@router.get("/by_customer_id/{customer_id}")
def read_customer_payment_information(
    *,
    db: Session = Depends(get_session),
    customer_id: int,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Get customer_payment_information by ID.
    """
    
    return None

@router.get("/{id}")
def read_customer_payment_information(
    *,
    db: Session = Depends(get_session),
    id: int,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Get customer_payment_information by ID.
    """
    
    return None


@router.put("/{id}", response_model=CustomerPaymentInformationUpdate)
def update_customer_payment_information(
    *,
    db: Session = Depends(get_session),
    id: int,
    customer_payment_information_in: schemas.CustomerPaymentInformationUpdate,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Update an customer_payment_information.
    """
    return None


@router.delete("/{id}", response_model=Any)
def delete_customer_payment_information(
    *,
    db: Session = Depends(get_session),
    id: int,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Delete an customer_payment_information.
    """

    return None
