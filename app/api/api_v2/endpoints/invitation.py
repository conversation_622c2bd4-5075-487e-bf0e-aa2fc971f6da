import ast
from sqlalchemy.orm import Session
from app.config.db.database import get_session
from app.crud.crud_invitation import CRUDInvitation
from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi import BackgroundTasks
from app.api.api_v2.deps import get_user
from app import crud
from app import schemas
from typing import Any, Optional

router = APIRouter()


# Endpoint to list all invitations without limit or sorting
@router.get("")
def read_invitations(
        db: Session = Depends(get_session),
        offset: int = 0,
        limit: int = 100,
        where: str = "",
        order_by: str = "id",
        order: str = "ASC",
        relations: str = "['customer{first_name,last_name}', 'account_information{company_name}']",
        # Update this to reflect the relationships
        base_columns: str = "['created_at', 'email', 'invitation_sent_at', 'invitation_accepted_at', 'invitation_link']",
) -> Any:
    # Convert string to list for conditions, relations, and base_columns
    where_conditions = ast.literal_eval(where) if where else []
    base_columns = ast.literal_eval(base_columns)
    relations = ast.literal_eval(relations)

    # Get the total count of invitations with the applied conditions
    count = crud.invitation.get_count_where_array(
        db=db,
        where=where_conditions,
    )

    # Get the invitations using `get_multi_where_array_v2` method
    invitation_list = crud.invitation.get_multi_where_array_v2(
        db=db,
        where=where_conditions,
        order_by=order_by,
        order=order,
        limit=limit,
        skip=offset,
        relations=relations,
        base_columns=base_columns,
    )

    # Return the count and the invitation list
    return {
        "count": count,
        "data": invitation_list,
    }


@router.get("/by_customer/{id}")
def read_customer(
        *,
        db: Session = Depends(get_session),
        id: int,
) -> Any:
    """
    Get user by ID.
    """
    where_conditions = [
        {
            "key": "customer_id",
            "value": id,
            "operator": "==",
        }
    ]
    count = crud.invitation.get_count_where_array(
        db=db,
        where=where_conditions,
    )

    # Get the invitations using `get_multi_where_array_v2` method
    invitation_list = crud.invitation.get_multi_where_array_v2(
        db=db,
        where=where_conditions,
    )

    return {
        "count": count,
        "data": invitation_list,
    }


@router.post("/send")
def send_invitation(
        *,
        db: Session = Depends(get_session),
        resend: bool= Query(default=False),
        invitation_in: schemas.InvitationCreate, 
) -> Any: 
    return crud.invitation.send_invitation(db=db, invitation_in=invitation_in,resend=resend)


@router.post("/save-required-info")
def save_required_info(
        *,
        db: Session = Depends(get_session),
        invitation_accept_in: schemas.InvitationAccept,
) -> Any:
    return crud.invitation.accept_invitation(db=db, invitation_accept_in=invitation_accept_in)


@router.post("/bulk_send_installer")
def bulk_send_invitation_installer(
        *,
        db: Session = Depends(get_session),
        current_user: Any = Depends(get_user),
        background_tasks: BackgroundTasks
) -> Any:
    background_tasks.add_task(crud.invitation.bulk_send_invitation_installer, db, user_id=current_user["id"])
    return {"message": "Notification sent in the background"}
    # return crud.invitation.bulk_send_invitation_installer(db=db, user_id=current_user["id"])
