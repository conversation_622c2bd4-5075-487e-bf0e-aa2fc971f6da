from typing import Any
import json
import tempfile
from app.models.ce_sender_email_campaign_country import CeSenderEmailCampaignCountry
from app.utils.const import DOWNLOAD_PAGE_SECRET
from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi.encoders import jsonable_encoder
import urllib
from app.services.customer_email.customer_email_item_factories import send_to_when_welcome_and_discovery
from app.utils.utils import send_email, send_email_cms
from sqlalchemy.orm import Session, joinedload
from app.config.db.database import get_session
from app.schemas.customer import (
    CustomerCreate,
    CustomerUpdate,
    RegistrationData,
    CustomerUpdateInfos,
    CustomerAccountInfoCreate,
)
from app.schemas.account_information import AccountInformationBase
import ast
from app.crud.crud_customer import customer
from app.crud.crud_account_information import account_information
from app.crud.crud_login import login
from app.utils.utils import to_dict
from app.api.api_v2.deps import get_user
import requests
from dotenv import load_dotenv
import os
from app import schemas
from app.api.api_v2.deps import get_current_language
from app import crud
from datetime import datetime, timedelta
from app.api.api_v2.deps import get_optional_customer, get_current_language
from typing import Optional
from app.enums.subscription_status_enum import SubscriptionStatusEnum
from app.services.notification.notification import send_notif
from app.config.settings import get_settings
from sqlalchemy import func, text, update
from sqlalchemy.sql import or_, and_, not_
from app.crud.pvgis_service import create_token, verify_token
from app.models.customer import Customer
from app.enums.title_enum import TitleEnum
from app.models.subscription import Subscription
from app.models.subscription_user import SubscriptionUser
import jwt

from fastapi.responses import FileResponse, RedirectResponse, StreamingResponse

router = APIRouter()

settings = get_settings()


@router.get("/dynamic/{id}")
def read_customer_dynamic(
        *,
        db: Session = Depends(get_session),
        id: int,
        relations: str = "[]",
        base_columns: str = "[]",
        json_columns: str = "[]", #example: ['path1:key1,key2','bar.arr:foo,soo,mi']
) -> Any:
    """
    Get customer by ID with dynamic relations and column selection.

    Args:
        id: Customer ID
        relations: JSON array of relations to include (e.g., ["account_information", "country_rel"])
                  Supports nested relations with column selection: ["relation{col1,col2}"]
        base_columns: JSON array of base columns to return (e.g., ["id", "first_name", "email"])
                     If empty, returns all columns

    Returns:
        Customer data with specified relations and columns
    """
    # Parse the relations and base_columns parameters
    relations_list = ast.literal_eval(relations)
    base_columns_list = ast.literal_eval(base_columns)

    # Use get_first_where_array_v2 for dynamic relations and column selection
    data = customer.get_first_where_array_v2(
        db=db,
        where=[
            {
                "key": "id",
                "value": id,
                "operator": "==",
            }
        ],
        relations=relations_list if relations_list else None,
        base_columns=base_columns_list if base_columns_list else None,
    )
    json_columns_list = ast.literal_eval(json_columns)
    for json_column_filter in json_columns_list:
        json_column_path = json_column_filter.split(":")[0]
        json_column_keys = json_column_filter.split(":")[1].split(",")
        filter_dict_keys(data, json_column_path, json_column_keys)
        
    if not data:
        raise HTTPException(status_code=404, detail="Customer not found")

    return data

def filter_dict_keys (dict_or_instance, path, keys_to_keep):
    to_filter = dict_or_instance
    path = path.split(".") if isinstance(path, str) else path
    for key_index, key in enumerate(path): 
        if isinstance(to_filter, dict):
            to_filter = to_filter.get(key, None)
        else: # to_filter is an instance
            to_filter = getattr(to_filter, key, None)
            
        if not to_filter:
            return None
        if isinstance(to_filter, list):
            remaining_path = path[key_index+1:]
            for item in to_filter:
                filter_dict_keys(item, remaining_path, keys_to_keep)
            return
        
    # This method only filters the keys of a dict, not the attributes of a class instance
    to_delete_keys = []
    for key in to_filter.keys():
        if key not in keys_to_keep:
            to_delete_keys.append(key)
    for key in to_delete_keys:
        del to_filter[key]

        


@router.get("/default-sender")
def get_default_sender(
    db: Session = Depends(get_session),
    customer_email: str = Query(title="Customer email", default =None),
    current_user: Any = Depends(get_user),
):
    default_emails = []
    customer= (
        db.query( 
            Customer
        ).options(joinedload(Customer.country_rel, innerjoin=True))
        .join(Subscription, Subscription.customer_id == Customer.id, isouter=True)
        .join(SubscriptionUser, SubscriptionUser.subscription_id == Subscription.id, isouter=True)
        .filter(
            or_(SubscriptionUser.email == customer_email,
                Customer.email == customer_email,
            )
        ).first()
    )
    if not(customer and customer.country_rel) :
        return  {"recommended_senders": []}
    
    default_emails.append(customer.country_rel.email_sender_json["email"])
    
    ce_sender_email_campaigns = (
        db.query(CeSenderEmailCampaignCountry)
        .options(joinedload(CeSenderEmailCampaignCountry.customer_email_sender, innerjoin=True))
        .filter(CeSenderEmailCampaignCountry.country_id == customer.country_rel.id).all()
        )
    for ce_sender in ce_sender_email_campaigns:
        default_emails.append(ce_sender.customer_email_sender.email)
     
    # Remove duplicates
    default_emails = list(dict.fromkeys(default_emails))
    return {"recommended_senders": default_emails}
    

@router.get("")
def read_customers(
        db: Session = Depends(get_session),
        offset: int = 0,
        limit: int = 100,
        where: str = "",
        order_by: str = "id",
        order: str = "ASC",
        relations: str = "[]",
        base_columns: str = "['created_at']",
) -> Any:
    wheres = []
    base_columns = ast.literal_eval(base_columns)
    relations = ast.literal_eval(relations)
    if where is not None and where != "" and where != []:
        wheres += ast.literal_eval(where)
    count = crud.customer.get_count_where_array(
        db=db,
        where=wheres,
    )
    customer_list = crud.customer.get_multi_where_array_v2(
        db=db,
        where=wheres,
        order_by=order_by,
        order=order,
        limit=limit,
        skip=offset,
        relations=relations,
        base_columns=base_columns,
    )

    return {
        "count": count,
        "data": customer_list,
    }


@router.get("/remove-from-mailing")
def remove_from_mailing(*, db: Session = Depends(get_session), email: str) -> Any:
    return RedirectResponse(
        url=f"{settings.PVGIS_UI_URL}/users/remove-from-mailing-list?email={email}"
    )


@router.post("/remove-from-mailing")
def remove_from_mailing(*, db: Session = Depends(get_session), email: str) -> Any:
    try:
        stmt = update(Customer).where(Customer.email == email).values(removed_from_mailing_list_at=datetime.now())
        db.execute(stmt)
        db.commit()
        return {"message": "Customer removed from mailing list"}
    except Exception as e:
        db.rollback()
        raise e

@router.get("/remove-from-mailing")
def remove_from_mailing(*, db: Session = Depends(get_session), email: str) -> Any:
    return RedirectResponse(
                url=f"{settings.PVGIS_UI_URL}/users/remove-from-mailing-list?email={email}"
            )

    
@router.get("/serve_file/{file_path:path}", response_model=Any)
def attachment_copp(file_path: str, ) -> Any:
    return FileResponse(
        path=file_path, filename=f"{file_path}", media_type="application/xlsx"
    )


@router.get("/registered_subscription/count")
def get_count_registered_subscription(
        *,
        x_app_key: str,
        db: Session = Depends(get_session),
) -> Any:
    if x_app_key != settings.X_APP_KEY:
        return HTTPException(status_code=401)

    return crud.customer.get_pvgis_status(db=db)


@router.get("/all_subscription_file")
def get_all_subscription(
        *,
        db: Session = Depends(get_session),
) -> Any:
    type_ = "all"
    file_name = f"subscriptions_{type_}_{datetime.now().strftime('%d%m%Y')}.xlsx"
    file_bytes = crud.customer.get_all_subscriptions_file(db=db, where=[])
    headers = {
        "Content-Disposition": f"attachment; filename={file_name}"
    }
    return  StreamingResponse(file_bytes, media_type="application/xlsx", headers=headers)


@router.get("/email_campaign_file")
def get_email_campaign_file(
    *,
    db: Session = Depends(get_session),
) -> Any:
    """
    Generate Excel file with customer email campaign data
    """
    file_name = f"PVGIS-registered-user-mailing-report-{datetime.now().strftime('%d%m%Y')}.xlsx"
    file_bytes = crud.customer_email_campaign.get_mail_campaign_report_file(db=db)
    headers = {
        "Content-Disposition": f"attachment; filename={file_name}"
    }
    return StreamingResponse(file_bytes, media_type="application/xlsx", headers=headers)

    
@router.get("/subscription_file/{type_}")
def get_all_subscription(
        *,
        db: Session = Depends(get_session),
        type_: str,
        current_datetime:datetime =  Query(default=datetime.now(), title="Current date")
) -> Any:
    type_conditions = {
        "last_24_hours":  [
            {"key": "created_at", "operator": "<=", "value": current_datetime},
            {"key": "created_at", "operator": ">=", "value": (current_datetime-timedelta(hours=24))},
            {"key": "subscription.created_at", "operator": "<=", "value": current_datetime},
            {"key": "subscription.created_at", "operator": ">=", "value": (current_datetime-timedelta(hours=24))},
        ],
        "all": [
            {"key": "subscription.created_at", "operator": "<=", "value": current_datetime},
            {"key": "created_at", "operator": "<=", "value": current_datetime},
        ]
    }
    # Format the date as "DDMMYYYY"
    file_name = get_sub_file_name(current_datetime, type_)
    file_bytes = crud.customer.get_all_subscriptions_file(
            db=db, 
            where=type_conditions[type_], 
          
        )
    headers = {
        "Content-Disposition": f"attachment; filename={file_name}"
    }
    return  StreamingResponse(file_bytes, media_type="application/xlsx", headers=headers)
     
def get_sub_file_name(current_datetime, type_):
    formatted_date = current_datetime.strftime("%d%m%Y")
    file_name = f"subscriptions_{type_}_{formatted_date}.xlsx"
    return file_name

@router.get("/cron_job/send_status_pvgis")
def cron_job_send_status_pvgis(
        *,
        x_app_key: str,
        db: Session = Depends(get_session),
        email_to: Optional[str] = ""
) -> Any:
    if x_app_key != settings.X_APP_KEY:
        return HTTPException(status_code=401)

    current_datetime = datetime.now()
    
    pvgis_status_data = crud.customer.get_pvgis_status(db=db) 

     
    file_last_24_hours_name = get_sub_file_name(current_datetime, 'last_24_hours')
    data = {
        "date": current_datetime.strftime("%d/%m/%Y"),
        "data": pvgis_status_data,
        "link": f"{settings.BACKEND_URL}/download_page/{DOWNLOAD_PAGE_SECRET}",
        "attachments":
            [
                # {
                #     "filename": f"{file_all_data}",
                #     "path": f"{settings.BACKEND_URL}/customers/serve_file/files%2F{file_all_data}",
                # },
                {
                    "filename": f"{file_last_24_hours_name}",
                    "path": f"{settings.BACKEND_URL}/customers/subscription_file/last_24_hours?current_datetime={urllib.parse.quote(str(current_datetime), safe=':/?&=')}",
                },
            ],
    }
    # send_mail
    email_param = {
        "templateVars": {**data},
        "mailVars": {
            "from": settings.SMPT_USER_MAIL,
            "to": "<EMAIL>, <EMAIL>",

        },
        "baseUrls": {"cmsUrl": settings.CMS_URL, "translationUrl": settings.TRANSLATION_URL},
        "sentByApp": "app",
        "sentByProcess": "Customer",
        "sentToUser": "",
        "type": "MAIL",
    }
    lang = "fr"

    if email_to != "":
        email_param["mailVars"]["to"] = email_to

    if settings.IS_PROD and email_to == "":
        # add list of email to send status if email to not set
        email_param["mailVars"][
            "to"] = ("<EMAIL>, <EMAIL>, "
                     "<EMAIL>")

    send_email_cms(email_param, lang, "pvgis-email-registered-subscription-status")

    return "status_sent"


@router.get("/test/send_status_pvgis")
def cron_job_send_status_pvgis(
        *,
        db: Session = Depends(get_session),
        email_to: Optional[str] = "",
        current_user: Any = Depends(get_user),
) -> Any:
    
    current_datetime = datetime.now()
    file_last_24_hours_name = get_sub_file_name(current_datetime, 'last_24_hours')
    pvgis_status_data = crud.customer.get_pvgis_status(db=db) 

    
    data = {
        "date": datetime.now().strftime("%d/%m/%Y"),
        "data": pvgis_status_data,
        "link": f"{settings.BACKEND_URL}/download_page/{DOWNLOAD_PAGE_SECRET}",
        "attachments":
            [
                # {
                #     "filename": f"{file_all_data}",
                #     "path": f"{settings.BACKEND_URL}/customers/serve_file/files%2F{file_all_data}",
                # },
                {
                    "filename": f"{file_last_24_hours_name}",
                    "path": f"{settings.BACKEND_URL}/customers/subscription_file/last_24_hours?current_datetime={urllib.parse.quote(str(current_datetime), safe=':/?&=')}",
                },
            ],

    }
    # send_mail
    email_param = {
        "templateVars": {**data},
        "mailVars": {
            "from": settings.SMPT_USER_MAIL,
            "to": "<EMAIL>",
        },
        "baseUrls": {"cmsUrl": settings.CMS_URL, "translationUrl": settings.TRANSLATION_URL},
        "sentByApp": "app",
        "sentByProcess": "Customer",
        "sentToUser": "",
        "type": "MAIL",

    }
    lang = "en"

    if email_to != "":
        email_param["mailVars"]["to"] = email_to

    send_email_cms(email_param, lang, "pvgis-email-registered-subscription-status")

    return "status_sent"


@router.post("")
def create_customer(
        *,
        db: Session = Depends(get_session),
        received_data: RegistrationData
) -> Any:
    """
    Create new user.
    - send payload to auth.api.com
    - wait for response
    """
    return login.main_function(db=db, received_data=received_data)


@router.get("/{id}")
def read_customer(
        *,
        db: Session = Depends(get_session),
        current_user: Any = Depends(get_user),
        id: int,
) -> Any:
    """
    Get user by ID.
    """
    data = crud.customer.get(db=db, id=id, relations=["account_information"])

    if not data:
        raise HTTPException(status_code=404, detail="Product not found")

    return data


@router.get("/customer_only/{id}")
def read_customer(
        *,
        db: Session = Depends(get_session),
        id: int,
) -> Any:
    """
    Get user by ID.
    """
    data = crud.customer.get(db=db, id=id, relations=["account_information"])

    if not data:
        raise HTTPException(status_code=404, detail="Product not found")

    return data


@router.put("/{id}", response_model=CustomerUpdate)
def update_customer(
        *,
        db: Session = Depends(get_session),
        id: int,
        obj_in: schemas.CustomerUpdate,
) -> Any:
    """
    Update an user.
    """
    data = crud.customer.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="Customer Not Found")

    data = crud.customer.update(db=db, db_obj=data, obj_in=obj_in)

    return to_dict(data)


@router.put("/customer_only/{id}", response_model=CustomerAccountInfoCreate)
def update_customer(
        *,
        db: Session = Depends(get_session),
        id: int,
        obj_in: schemas.CustomerAccountInfoCreate,
) -> Any:
    """
    Update an user.
    """
    data = crud.customer.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="Customer Not Found")

    updated_customer = crud.customer.update(db=db, db_obj=data, obj_in=obj_in)

    account_info_in = {
        "account_type_id": obj_in.account_type_id,
        "company_name": obj_in.company_name,
        "siret_number": obj_in.siret_number,
    }
    # update account_info
    found_account_info = account_information.get_first_where_array(
        db=db,
        where=[
            {
                "key": "customer_id",
                "value": id,
                "operator": "==",
            }
        ],
    )
    if not found_account_info:
        account_information.create(
            db=db,
            obj_in=AccountInformationBase(
                account_type_id=obj_in.account_type_id,
                company_name=obj_in.company_name,
                siret_number=obj_in.siret_number,
                customer_id=id,
            ),
        )
    else:
        updated_account_info = account_information.update(
            db=db, db_obj=found_account_info, obj_in=account_info_in
        )

    return to_dict(updated_customer)


@router.put("/reset-stripe-customer/{id}", response_model=CustomerUpdate)
def reset_stripe_customer_id(
        *,
        db: Session = Depends(get_session),
        id: int,
        current_user: Any = Depends(get_user),
        current_customer: Optional[Any] = Depends(get_optional_customer)
) -> Any:
    """
    Update an user.
    """

    if not current_customer:
        raise HTTPException(status_code=404, detail="customer_not_found")

    if id != current_customer.id:
        raise HTTPException(status_code=403, detail="not_authorized")

    data = crud.customer.update(db=db, db_obj=current_customer, obj_in={"stripe_customer_id": None})

    return to_dict(data)


@router.delete("/{id}", response_model=Any)
def delete_customer(
        *,
        db: Session = Depends(get_session),
        id: int,
) -> Any:
    """
    Delete an user.
    """

    data = crud.customer.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="Product not found")

    return to_dict(crud.customer.soft_delete(db=db, id=id))


@router.put("/updateDeleteAt/{id}", response_model=Any)
def delete_customer(
        *,
        db: Session = Depends(get_session),
        current_user: Any = Depends(get_user),
        lang: str = Depends(get_current_language),
) -> Any:
    """
    Delete an user.
    """
    found_customer = crud.customer.get_first_where_array(db=db, where=[
        {"key": "auth_user_id", "operator": "==", "value": current_user["id"]}])
    if not found_customer:
        raise HTTPException(status_code=404, detail="Product not found")
    wheres = [{
        "key": "customer_id",
        "value": found_customer.id,
        "operator": "==",
    },
        {
            "key": "subscription_status",
            "value": SubscriptionStatusEnum.ACTIVE,
            "operator": "==",
        },
        {
            "key": "product_json",
            "operator": "isNotNull"
        }

    ]
    subscriptions = crud.subscription.get_multi_where_array(db=db, where=wheres, limit=1, order="DESC",
                                                            order_by="start_date")
    expired_date = datetime.now()

    if len(subscriptions) > 0:
        expired_date = subscriptions[0].expired_date
    to_delete_at = expired_date + timedelta(days=1)
    setting_res = requests.get(f"{settings.SETTINGS_API_URL}/setting/key/pvgis__ui.stored_duration")
    stored_duration = setting_res.json()["value"]
    token = create_token(data={'customer_id': found_customer.id})
    notification_data = [
        {
            "id": current_user["id"],
            "roleId": 2,
            "appKey": settings.APP_KEY,
            "vars": {
                "customerUsername": found_customer.email,
                "customerFullName": found_customer.full_name,
                "disabled_date": to_delete_at.strftime("%d/%m/%Y"),
                "stored_duration": stored_duration,
                "limite_date": to_delete_at.strftime("%d/%m/%Y"),
                "reactivation_url": f"{settings.PVGIS_UI_URL}/activate_account/{token}"
            }
        }
    ]

    # Send notification
    send_notif(event_code="delete-customer-account", users=notification_data, mailLanguage=lang)
    return to_dict(crud.customer.update(
        db=db, db_obj=found_customer, obj_in={"to_delete_at": to_delete_at}
    ))

    # return to_dict(crud.customer.soft_delete(db=db, id=id))


@router.put("/activate_account/{activate_account_hash}", response_model=Any)
def reactivate_customer_account(
        *,
        db: Session = Depends(get_session),
        activate_account_hash: Any,
        lang: str = Depends(get_current_language)
) -> Any:
    """
    Delete an user.
    """
    customer_id = 0
    try:
        customer_id = verify_token(activate_account_hash)["customer_id"]
    except jwt.ExpiredSignatureError as e:
        raise HTTPException(status_code=500, detail="pvgis.error.reactivation_time_expired")
    except jwt.InvalidTokenError as e:
        raise HTTPException(status_code=500, detail="pvgis.error.invalid_token")
    except Exception as e:
        raise HTTPException(status_code=500, detail="pvgis.error.unknown")

    found_customer = crud.customer.get(db=db, id=customer_id)
    if not found_customer:
        raise HTTPException(status_code=404, detail="pvgis.error.customer_already_deleted")

    notification_data = [
        {
            "id": found_customer.auth_user_id,
            "roleId": 2,
            "appKey": settings.APP_KEY,
            "vars": {
                "customerUsername": found_customer.email,
                "loginUrl": f"{settings.PVGIS_UI_URL}/login"
            }
        }
    ]

    # Send notification
    send_notif(event_code="activate-account", users=notification_data, mailLanguage=lang)
    return to_dict(crud.customer.update(
        db=db, db_obj=found_customer, obj_in={"to_delete_at": None, "deleted_at": None}
    ))


@router.post("/create_customer_only", response_model=Any)
def create_customer_only(
        *,
        db: Session = Depends(get_session),
        customer_in: schemas.CustomerAccountInfoCreate,
) -> Any:
    """
    Create new account_type.
    """
    data = crud.customer.create(db=db, obj_in=customer_in)

     

    account_information.create(
        db=db,
        obj_in=AccountInformationBase(
            account_type_id=customer_in.account_type_id,
            company_name=customer_in.company_name,
            siret_number=customer_in.siret_number,
            customer_id=data.id,
        ),
    )
    return data

    # return to_dict(crud.customer.soft_delete(db=db, id=id))


@router.post("/create_or_update")
def create_or_update_customer(
        *,
        db: Session = Depends(get_session),
        received_data: schemas.CustomerUpdateInfos,
        current_user: Any = Depends(get_user),
) -> Any:
    
    """
    CREATE OR UPDATE CUSTOMER INFOS
    """
    # Construct the payload
    updated_customer = None
    # Create user_in object for database insertion
    if current_user:
        found_customer = crud.customer.get_first_where_array(
            db=db,
            where=[
                {"key": "auth_user_id", "value": current_user["id"], "operator": "=="}
            ],
        )
        if not found_customer:
            raise HTTPException(status_code=404, detail="USER NOT FOUND")
        # update customer
        customer_ob_in = {
            "country": received_data.country,
            "city": received_data.city,
            "street_address": received_data.street_address,
            # "mobile_number": received_data.mobile_number, Change to the full number
            "mobile_number": received_data.full_phone,
            "email": received_data.email,
        }
        if received_data.title is not None:
            customer_ob_in["title"] = received_data.title

        if received_data.first_name is not None:
            customer_ob_in["first_name"] = received_data.first_name,

        if received_data.last_name is not None:
            customer_ob_in["last_name"] = received_data.last_name,

        if received_data.settings_json is not None:
            customer_ob_in["settings_json"] = received_data.settings_json

        if "email" in customer_ob_in and not customer_ob_in["email"]:
            del customer_ob_in["email"]
        account_info_in = {
            "company_name": received_data.company_name,
            "siret_number": received_data.siret_number,
        }
        updated_customer = crud.customer.update(
            db=db, db_obj=found_customer, obj_in=customer_ob_in
        )
        # update account_info
        found_account_info = account_information.get_first_where_array(
            db=db,
            where=[
                {
                    "key": "customer_id",
                    "value": found_customer.id,
                    "operator": "==",
                }
            ],
        )
        if not found_account_info:
            account_information.create(
                db=db,
                obj_in=AccountInformationBase(
                    company_name=received_data.company_name,
                    siret_number=received_data.siret_number,
                    customer_id=found_customer.id,
                ),
            )
        else:
            updated_account_info = account_information.update(
                db=db, db_obj=found_account_info, obj_in=account_info_in
            )
    else:
        raise HTTPException(status_code=404, detail="CREATION NOT IMPLEMENTED")
    return updated_customer


@router.get("/infos/{id}")
def read_customer(
        *,
        db: Session = Depends(get_session),
        id: int,
) -> Any:
    """
    Get user by ID.
    """
    data = customer.get(
        db=db,
        id=id,
        relations=[
            "account_information",
            "country_rel"
        ],
    )

    if not data:
        raise HTTPException(status_code=404, detail="Customer not found")

    return data



@router.get("/validate_customer_info/{id}")
def validate_customer_info(
        *,
        db: Session = Depends(get_session),
        id: int
) -> Any:
    """
    Get user by ID.
    """
    customer_ob = crud.customer.get(
        db=db,
        id=id,
        relations=[
            "account_information",
        ],
    )
    if not customer_ob:
        raise HTTPException(status_code=404, detail="Customer not found")

    account_type_id = customer_ob.account_information.account_type_id
    valid_cond = [
        customer_ob.email,
        # customer_ob.street_address, 
        # customer_ob.city, 
        customer_ob.country,
        # customer_ob.mobile_number
    ]
    # if account_type_id==1:
    #     valid_cond.extend([
    #         customer_ob.account_information.siret_number, 
    #         customer_ob.account_information.company_name
    #     ])
    #     print("1")
    if account_type_id == 2:
        valid_cond.extend([
            customer_ob.first_name,
            customer_ob.last_name,
        ])
    #     print("2")
    # elif account_type_id==3:
    #     print("3")
    #     valid_cond.extend([
    #         customer_ob.account_information.company_name
    #     ])

    return all(valid_cond)


@router.delete("/cron_job/remove_today_customer", response_model=Any)
def delete_customer(
        *,
        x_app_key: str,
        db: Session = Depends(get_session),
) -> Any:
    """
    Cron Delete Customers.
    """
    if x_app_key != settings.X_APP_KEY:
        return HTTPException(status_code=401)
    setting_res = requests.get(f"{settings.SETTINGS_API_URL}/setting/key/pvgis__ui.stored_duration")
    stored_duration = setting_res.json()["value"]
    # Adjust the where condition to include all dates up to today
    target_date = datetime.now().strftime("%Y-%m-%d")
    data = crud.customer.get_multi_where_array(
        db=db,
        where=[
            {"key": "to_delete_at", "operator": "lte_date", "value": target_date}
        ]
    )
    for cust in data:
        limite_date_to_delete = cust.to_delete_at + timedelta(days=int(stored_duration))
        crud.customer.soft_delete(db=db, id=cust.id)
        send_cron_soft_delete_notif(customer=cust, limite_date_to_delete=limite_date_to_delete, stored_duration=stored_duration)
    # disable in auth

    return {"status": 200, "message": "cron delete customers successful"}


@router.delete("/cron_job/hard_delete_today_customer", response_model=Any)
def hard_delete_customer(
        *,
        x_app_key: str,
        db: Session = Depends(get_session),
) -> Any:
    """
    Cron Delete Customers.
    """
    if x_app_key != settings.X_APP_KEY:
        return HTTPException(status_code=401)
    stored_duration = 15
    try:
        setting_res = requests.get(f"{settings.SETTINGS_API_URL}/setting/key/pvgis__ui.stored_duration")
        stored_duration = setting_res.json()["value"] if setting_res.json()["value"] else 15
    except:
        pass

    today = datetime.today().date()

    # Adjust the where condition to include all dates up to today
    target_date = today - timedelta(days=int(stored_duration))
    
    data = crud.customer.get_multi_where_array(
        db=db,
        where=[
            {"key": "deleted_at", "operator": "lte_date", "value": target_date.strftime("%Y-%m-%d")}
        ],
        include_deleted=True
    )

     
    for cust in data:
        crud.customer.hard_delete_customer_data(db=db, customer=cust)

    return {"status": 200, "message": "cron delete customers successful"}


def send_cron_soft_delete_notif(customer, limite_date_to_delete, stored_duration, lang: str = 'en'):
    token = create_token(data={'customer_id': customer.id})
    print("token", token)
    stored_duration = stored_duration if stored_duration else 365
    notification_data = [
        {
            "id": customer.auth_user_id,
            "roleId": 2,
            "appKey": settings.APP_KEY,
            "vars": {
                "customerUsername": customer.email,
                "customerFullName": customer.full_name,
                "stored_duration": stored_duration,
                "limite_date_to_delete": limite_date_to_delete.strftime("%d/%m/%Y"),
                "reactivation_url": f"{settings.PVGIS_UI_URL}/activate_account/{token}"
            }
        }
    ]

    # Send notification
    send_notif(event_code="delete-account-after-cron", users=notification_data, mailLanguage=lang)


#####UNSUBSCRIBE USER#####
@router.post("/unsubscribe_customer")
def unsubscribe_customer(
    *,
    db: Session = Depends(get_session),
    current_user: Any = Depends(get_user),
) -> Any:
    try:
        now = datetime.now()

        #Get customer
        customer = crud.customer.get_first_where_array(db=db, where=[
            {"key": "auth_user_id", "operator": "==", "value": current_user["id"]}
        ])
        if not customer:
            raise HTTPException(status_code=404, detail="Customer not found")

        #Update removed_from_mailing_list_at et unsubscribed_at
        crud.customer.update(
            db=db,
            db_obj=customer,
            obj_in={
                "removed_from_mailing_list_at": now,
                "unsubscribed_at": now
            }
        )

       #Delete all subscriptions
        subscriptions = crud.subscription.get_multi_where_array(db=db, where=[
            {"key": "customer_id", "operator": "==", "value": customer.id}
        ])
        subscription_ids = [sub.id for sub in subscriptions]  # Récupérer les IDs des subscriptions

        for sub in subscriptions:
            sub_id = sub.id
            crud.subscription.update(db=db, db_obj=sub, obj_in={"deleted_at": now})

        #Delete all simulations
        if subscription_ids:
            simulations = crud.simulation.get_multi_where_array(db=db, where=[
                {"key": "subscription_id", "operator": "in", "value": subscription_ids}
            ])
            simulation_ids = [sim.id for sim in simulations]  # Récupérer les IDs des simulations
            
            for sim in simulations:
                sim_id = sim.id
                crud.simulation.update(db=db, db_obj=sim, obj_in={"deleted_at": now})
        else:
            simulations = []
            simulation_ids = []
        #Delete all simulation_items
        if simulation_ids:
            simulation_items = crud.simulation_item.get_multi_where_array(db=db, where=[
                {"key": "simulation_id", "operator": "in", "value": simulation_ids}
            ])            
            for item in simulation_items:
                item_id = item.id
                crud.simulation_item.update(db=db, db_obj=item, obj_in={"deleted_at": now})
        #Desabled account into AUTH_API
        auth_api_res = requests.get(f"{settings.AUTH_API_URL}/user-app/userId/{customer.auth_user_id}")
        deactive_payload = {"deactivate": True, "verified": False,}
        if auth_api_res.status_code == 200:
            user_app_data = auth_api_res.json()[0]
            user_app_id = user_app_data['id']
            deactive_user_res = requests.patch(f"{settings.AUTH_API_URL}/user-app/{user_app_id}", json=deactive_payload, headers={'Notifinfo': '{}'})
            if deactive_user_res.status_code == 200:
                return print("User deactivated successfully")
        else:
            print('user not found',auth_api_res)
            raise HTTPException(status_code=404, detail="User not found")

        # db.commit()

        return {
            "message": "Customer unsubscribed successfully",
            "customer": customer,
        }

    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

#####RESUBSCRIBE USER#####
@router.post("/resubscribe_customer")
def resubscribe_customer(
        *,
    db: Session = Depends(get_session),
    received_data: RegistrationData,
) -> Any:
    try:
        now = datetime.now()
        lang = received_data.language or "en"

        #Get customer
        customer = crud.customer.get_first_where_array(db=db, where=[
            {"key": "email", "operator": "==", "value": received_data.email}
        ])
        if not customer:
            raise HTTPException(status_code=404, detail="Customer not found")
        
        #Update customer data
        customer_ob_update_data = {
            "country": received_data.country,
            "country_id": received_data.country_id,
            "removed_from_mailing_list_at": None,
            "first_name": received_data.first_name,
            "last_name": received_data.last_name,
            "updated_at": now,
        }
        crud.customer.update(
            db=db,
            db_obj=customer,
            obj_in=customer_ob_update_data
        )
        print("customer updated successfully")

        #Update account information data
        customer_account_information = crud.account_information.get_first_where_array(
            db=db,
            where=[
                {"key": "customer_id", "operator": "==", "value": customer.id}
            ]
        )
        if not customer_account_information:
            raise HTTPException(status_code=404, detail="Account information not found")
        customer_account_info_update_data = {
            "account_type_id": received_data.account_type_id,
            "profession": None,
            "age_range": None,
            "professionnal_category_id": None,
            "school_category_id": None,     
        }
        if received_data.account_type_id == 1:
            customer_account_info_update_data["company_name"] = received_data.company_name
        elif received_data.account_type_id == 2:
            customer_account_info_update_data["company_name"] = None
        elif received_data.account_type_id == 3:
            customer_account_info_update_data["company_name"] = received_data.company_name
        account_information.update(
            db=db,
            db_obj=customer_account_information,
            obj_in=customer_account_info_update_data
        )
        print("customer account information updated successfully")

        #Update customer data into AUTH_API
        user_profile_data = {
            "firstname": received_data.first_name,
            "lastname": received_data.last_name,
        }
        headers = {
            "Accept": "*/*",
            "Content-Type": "application/json"
        }
        user_profile_res = requests.patch(f"{settings.AUTH_API_URL}/user-profile/by-user-id/{customer.auth_user_id}", json=user_profile_data, headers=headers)
        if user_profile_res.status_code == 200:
            print("User profile updated successfully")
        else:
            print('user profile not found',user_profile_res.json())
        generate_pwd_res = requests.get(f"{settings.AUTH_API_URL}/user/generate-password/{received_data.password}")
        password_hashed = generate_pwd_res.text
        user_update_data = {
            "password": password_hashed,
        }
        user_res = requests.patch(f"{settings.AUTH_API_URL}/user/{customer.auth_user_id}", json=user_update_data)
        if user_res.status_code == 200:
            print("User updated successfully")
        else:
            print(f"Error from auth API: {user_res.json()}")
        
        #Enabled account into AUTH_API
        auth_api_res = requests.get(f"{settings.AUTH_API_URL}/user-app/userId/{customer.auth_user_id}")
        active_payload = {"deactivate": False,}
        if auth_api_res.status_code == 200:
            user_app_data = auth_api_res.json()[0]
            user_app_id = user_app_data['id']
            active_user_res = requests.patch(f"{settings.AUTH_API_URL}/user-app/{user_app_id}", json=active_payload, headers={'Notifinfo': '{}'})
            if active_user_res.status_code == 200:
                print("User activated successfully")
            else:
                print('user not found',auth_api_res)
                
        #Send verification email
        token = create_token(data={'customer_id': customer.id})
        cms_key = "mail-resubscription-customer"
        verification_url = settings.PVGIS_UI_URL + "/login/verify_email/" + token
        email_param = {
            "templateVars": {
                "first_name": received_data.first_name,
                "last_name": received_data.last_name,
                "reactivation_url": verification_url,
            },
            "mailVars": {
                "from": settings.SMPT_USER_MAIL,
                "to": received_data.email,
            },
            "baseUrls": {"cmsUrl": settings.CMS_URL, "translationUrl": settings.TRANSLATION_URL},
            "sentByApp": "app",
            "sentByProcess": "Customer",
            "sentToUser": "",
            "type": "MAIL",
        }
        send_email_cms(email_param, lang, cms_key)
        print("Email sent successfully")

        db.commit()

        return {
            "message": "Customer subscribed successfully",
            "customer": customer,
        }

    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))
    
@router.get("/reverify_email/{token}")
def reverify_email(
        *,
        db: Session = Depends(get_session),
        token: str,
) -> Any:
    try:
        customer_id = verify_token(token)["customer_id"]
    except jwt.ExpiredSignatureError as e:
        raise HTTPException(status_code=500, detail="pvgis.error.reactivation_time_expired")
    except jwt.InvalidTokenError as e:
        raise HTTPException(status_code=500, detail="pvgis.error.invalid_token")
    except Exception as e:
        raise HTTPException(status_code=500, detail="pvgis.error.unknown")

    customer = crud.customer.get(db=db, id=customer_id)
    if not customer:
        raise HTTPException(status_code=404, detail="pvgis.error.customer_already_deleted")

    #Update verified into AUTH API
    auth_api_res = requests.get(f"{settings.AUTH_API_URL}/user-app/userId/{customer.auth_user_id}")
    verified_payload = {"verified": True}
    if auth_api_res.status_code == 200:
        user_app_data = auth_api_res.json()[0]
        user_app_id = user_app_data['id']
        verified_user_res = requests.patch(f"{settings.AUTH_API_URL}/user-app/{user_app_id}", json=verified_payload, headers={'Notifinfo': '{}'})
        if verified_user_res.status_code == 200:
            print("User verified successfully")
        else:
            print('user not found',auth_api_res)
    else:
        print('user not found',auth_api_res)

    return {"status": 200, "message": "Email verified successfully"}