import ast
from sqlalchemy.orm import Session
from app.config.db.database import get_session
from app.config.settings import get_settings
from app.crud.crud_invitation import CRUDInvitation
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Query
from app.api.api_v2.deps import get_user
from app import crud
from app import schemas
from typing import Any, Optional
import uuid
import pandas as pd
import os

from app.services.customer_migration_service import integrate_installers_by_chunk
settings = get_settings()
router = APIRouter()



@router.post("/customer/installer/batch", response_model=Any)
def bulk_upload_customer(
        *,
        x_app_key: str,
        db: Session = Depends(get_session),  
        chunk_size: int = Query(default=200)
) -> Any: 
    if x_app_key != settings.X_APP_KEY:
        return HTTPException(status_code=401)
    integrate_installers_by_chunk(db, chunk_size)
    return {"message": "Migration done successfully"}


@router.post("/customer/installer", response_model=Any)
def bulk_upload_customer(
        *,
        x_app_key: str,
        db: Session = Depends(get_session),
        excel_file: UploadFile = File(...),
        current_user: Any = Depends(get_user),
) -> Any:
    """
    Create new account_type.
    """
    if x_app_key != settings.X_APP_KEY:
        return HTTPException(status_code=401)
    import_id = str(uuid.uuid4())
    try:
        name = excel_file.filename

        file_directory = "files"
        file_location = f"{file_directory}/{str(name).replace(' ', '_')}"

        if not os.path.exists(file_directory):
            os.makedirs(file_directory)

        bulk_result = {
            "added": [],
            "skipped": []
        }
        with open(file_location, "wb+") as file_object:
            file_object.write(excel_file.file.read())
            df = pd.read_excel(file_location, engine="openpyxl", sheet_name=0, header=0)
            df = df.applymap(lambda x: None if pd.isna(x) else x)
            row_nb = 0
            for index, row in df.iterrows():
                if (
                        not row.isnull().all()
                        and not row.apply(lambda x: pd.isna(x) or x is pd.NaT).all()
                ):
                    
                    row_nb += 1

                    row_result = crud.bulk_upload.bulk_upload_installer(
                        db=db,
                        import_id=import_id,
                        row=row,
                        user_id=current_user["id"],
                    )
                    db.commit()
                    if row_result.get("added"):
                        bulk_result["added"].append(row_result["added"])
                    elif row_result.get("skipped"):
                        bulk_result["skipped"].append(row_result["skipped"])

        crud.bulk_upload.create_import(
            db=db,
            file_name=name,
            resource_name="bulk_upload_installer",
            import_id=import_id,
        )
        return {
            "import_id": import_id,
            "status": "success",
            "count": {
                "added": len(bulk_result["added"]),
                "skipped": len(bulk_result["skipped"]),
            },
            "response": bulk_result
        }
    except Exception as e:
        # TODO revert bulk
        crud.bulk_upload.revert_bulk_installer(db=db, import_id=import_id)
        raise e


@router.delete("revert", response_model=Any)
def delete_features(
        *,
        db: Session = Depends(get_session),
        import_id: str,
        current_user: Any = Depends(get_user),
) -> Any:
    """
    Delete an crud.features.
    """
    crud.bulk_upload.revert_bulk_installer(db=db, import_id=import_id)
    return "fait"

@router.post("/customer/fill_missing_country", response_model=Any)
def bulk_update_missing_country_customer(
    *,
    db: Session = Depends(get_session),
    start_date: str = Query(
        default="2025-01-01T00:00:00",
        description="Start date for the query, defaults to the start of the current month if not set"
    ),
    end_date: str = Query(
        default="2025-01-30T00:00:00",
        description="End date for the query, defaults to the end of the current month if not set"
    ),
    customer_limit: int = 1000
):
    return crud.bulk_upload.bulk_update_missing_country_customer(db=db, start_date=start_date, end_date=end_date, customer_limit=customer_limit)