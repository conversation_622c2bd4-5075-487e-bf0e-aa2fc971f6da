from typing import Any
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
import ast

from app.api.api_v2.deps import get_user
from app.config.db.database import get_session
from app.schemas.subscription_payment_transaction import (
    SubscriptionPaymentTransactionUpdate,
)
from app.crud.pvgis_service import (
    generatePaymentReference
)
from app.utils.utils import to_dict
from app import schemas
from app.config.settings import get_settings
from app import crud

router = APIRouter()

settings = get_settings()


@router.get("")
def read_subscription_payment_receipts(
    db: Session = Depends(get_session),
    offset: int = 0,
    limit: int = 100,
    where: str = "",
    order_by: str = "id",
    order: str = "ASC",
    relations: str="[]",
    base_columns: str = "['created_at']"
) -> Any:
    wheres = []
    base_columns = ast.literal_eval(base_columns)
    relations = ast.literal_eval(relations)
    if where is not None and where != "" and where != []:
        wheres += ast.literal_eval(where)
    count = crud.subscription_payment_receipt.get_count_where_array(
        db=db,
        where=wheres,
    )
    subscription_payment_receipt_list = crud.subscription_payment_receipt.get_multi_where_array_v2(
        db=db,
        where=wheres,
        order_by=order_by,
        order=order,
        limit=limit,
        skip=offset,
        relations=relations,
        base_columns=base_columns,
    )

    return {
        "count": count,
        "data": subscription_payment_receipt_list,
    }

@router.get("/cron/populate_missing_transaction_id") 
def fix_transaction_subs_relations(
    *,
    x_app_key: str,
    db: Session = Depends(get_session),
):
    if x_app_key != settings.X_APP_KEY:
        return HTTPException(status_code=401)
    
    crud.subscription_payment_transaction.fix_transaction_subs_relations(db)
    return {"message": "Process finished successfully"}

@router.get("/{id}")
def read_subscription_payment_transaction(
    *,
    db: Session = Depends(get_session),
    id: int,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Get subscription_payment_transaction by ID.
    """
    data = crud.subscription_payment_receipt.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="Receipt not found")

    return data


@router.delete("/{id}", response_model=Any)
def delete_subscription_payment_receipt(
    *,
    db: Session = Depends(get_session),
    id: int,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Delete an crud.subscription_payment_receipt.
    """

    data = crud.subscription_payment_receipt.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="Receipt not found")

    return to_dict(crud.subscription_payment_receipt.soft_delete(db=db, id=id))


