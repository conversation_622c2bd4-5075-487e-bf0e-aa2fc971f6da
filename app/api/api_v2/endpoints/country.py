from typing import Any
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File
from sqlalchemy.orm import Session
import json

from app.api.api_v2.deps import get_user
from app.config.db.database import get_session
from app.schemas.country import CountryCreate, CountryUpdate

from app.crud.crud_country import country
from app import schemas
from app import crud
from fastapi.encoders import jsonable_encoder

router = APIRouter()

import ast

@router.get("")
def read_country(
    db: Session = Depends(get_session),
    offset: int = 0,
    limit: int = 100,
    where: str = "",
    order_by: str = "id",
    order: str = "ASC",
    relations: str="[]",
    base_columns: str = "['created_at']",
    no_limit: bool = False,
) -> Any:
    wheres = []
    base_columns = ast.literal_eval(base_columns)
    relations = ast.literal_eval(relations)
    if where is not None and where != "" and where != []:
        wheres += ast.literal_eval(where)
    count = crud.country.get_count_where_array(
        db=db,
        where=wheres,
    )
    country_list = crud.country.get_multi_where_array_v2(
        db=db,
        where=wheres,
        order_by=order_by,
        order=order,
        limit=limit,
        skip=offset,
        relations=relations,
        base_columns=base_columns,
        no_limit=no_limit,
    )
    
    

    return {
        "count": count,
        "data": country_list,
    }


@router.post("", response_model=Any)
def create_country(
    *,
    db: Session = Depends(get_session),
    country_in: schemas.CountryCreate,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Create new crud.country.
    """
    data = crud.country.create(db=db, obj_in=country_in)

    return data

@router.post("/import-json", response_model=Any)
def upload_json_countries(
    *,
    db: Session = Depends(get_session),
    file: UploadFile = File(...),
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Upload a JSON file and create countries in the database.
    """
    try:
        content = file.file.read()
        countries_data = json.loads(content)
        
        if not isinstance(countries_data, list):
            raise HTTPException(status_code=400, detail="invalid_json_file")
        
        created_countries = []
        for country in countries_data:
            if(country["cca2"]):
                country_in = {
                    "name": country["name"]["common"],
                    "code_alpha_2": country["cca2"],
                    "code_alpha_3": country["cca3"],
                    "status": True,
                }
                country_exists = crud.country.get_first_where_array_v2(db=db, where=[{"key":"code_alpha_2", "operator":"==", "value": country["cca2"]}])
                if country_exists is None:
                    country_schema = schemas.CountryCreate(**country_in)
                    created_countries.append(country_schema)
        created_country = crud.country.create_multi(db=db, objs_in=created_countries)
        return created_country

    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="invalid_json_file")
    except Exception as e:
        raise
        # raise HTTPException(status_code=500, detail=f"An error occurred: {str(e)}")


@router.get("/{id}")
def read_country(
    *,
    db: Session = Depends(get_session),
    id: int,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Get country by ID.
    """
    data = crud.country.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="country_not_found")

    return data


@router.put("/{id}")
def update_country(
    *,
    db: Session = Depends(get_session),
    id: int,
    country_in: schemas.CountryUpdate,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Update an crud.country.
    """
    data = crud.country.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="country_not_found")

    data = crud.country.update(db=db, db_obj=data, obj_in=country_in)

    return data


@router.delete("/{id}", response_model=Any)
def delete_country(
    *,
    db: Session = Depends(get_session),
    id: int,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Delete an crud.country.
    """

    data = crud.country.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="country_not_found")

    return crud.country.soft_delete(db=db, id=id)
