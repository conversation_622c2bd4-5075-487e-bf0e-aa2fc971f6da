from typing import Any
import ast
from app.enums.subscription_status_enum import SubscriptionStatusEnum
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import func
from fastapi.encoders import jsonable_encoder

from app.api.api_v2.deps import get_user,get_optional_customer, get_current_language
from app.config.db.database import get_session
from app.schemas.simulation import SimulationUpdate
from app.services.notification.notification import send_notif
from datetime import datetime
import json
from app.crud.crud_simulation import simulation

from app.services.country_name_by_loc import get_country_name_from_coordinates

from app import schemas
from app import crud
from app.config.settings import get_settings
settings = get_settings()


from app.utils.utils import to_dict

router = APIRouter()


@router.get("/code_alpha")
def get_country_by_loc(
    db: Session = Depends(get_session),
    latitude: float = 0.0,
    longitude: float = 0.0
):
    if latitude is not None and longitude is not None:
        code_alpha = get_country_name_from_coordinates(latitude=latitude, longitude=longitude)
        if code_alpha:
            return code_alpha
        else:
            return {
                "message": "Coordinates not found",
                "latitude": latitude,
                "longitude": longitude,
                "code_alpha": None
            }
    else:
        return None
    
@router.get("/update-simulation-country-ids-by-coordinates")
def update_simulation_country_ids_by_coordinates(
    db: Session = Depends(get_session)
):
    simulations = simulation.get_multi_where_array(db=db, where=[{"key": "country_id", "operator": "isNull"}])
    missing_countries = []
    for sim in simulations:
        code_alpha = get_country_name_from_coordinates(sim.latitude, sim.longitude)
        if code_alpha:
            country_obj = crud.country.get_by_code_alpha_2(db=db, code_alpha_2=code_alpha)
            if country_obj:
                simulation.update_country_id(db=db, simulation_id=sim.id, country_id=country_obj.id)
            else:
                missing_countries.append(code_alpha)
        else:
            if sim.country:
                country_obj = crud.country.get_by_name(db=db, name=sim.country)
                if country_obj:
                    simulation.update_country_id(db=db, simulation_id=sim.id, country_id=country_obj.id)
                else:
                    missing_countries.append(sim.country)
            else:
                missing_countries.append(f"Coordinates ({sim.latitude}, {sim.longitude})")

    if missing_countries:
        return {"message": "Some country IDs have been successfully updated", "missing_countries": missing_countries}

    return {"message": "All country IDs updated successfully"}


@router.get("/update-country-ids")
def update_country_ids(
    db: Session = Depends(get_session)
) -> Any:
    simulations = simulation.get_multi_where_array(db=db, where=[{"key": "country_id", "operator": "isNull"}])
    missing_countries = []
    for sim in simulations:
        country_obj = country.get_by_name(db=db, name=sim.country)
        if country_obj:
            simulation.update_country_id(db=db, simulation_id=sim.id, country_id=country_obj.id)
        else:
            missing_countries.append(sim.country)
    if missing_countries:
        return {"message": "Some country IDs updated successfully", "missing_countries": missing_countries}
    return {"message": "All country IDs updated successfully"}

@router.get("")
def read_simulations(
    db: Session = Depends(get_session),
    offset: int = 0,
    limit: int = 100,
    where: str = "",
    order_by: str = "id",
    order: str = "ASC",
    relations: str = "[]",
    base_columns: str = "['created_at']",
    current_user: Any = Depends(get_user),
    customer:Any=Depends(get_optional_customer),
) -> Any:
    # GET LIST OF SIMULATION OF CUSTOMER (PROJECT LIST)
    wheres = [
        {"key": "subscription.customer_id", "value": customer.id, "operator": "=="}
    ]
    base_columns = ast.literal_eval(base_columns)
    relations = ast.literal_eval(relations)
    if where is not None and where != "" and where != []:
        wheres += ast.literal_eval(where)
    count = crud.simulation.get_count_where_array(
        db=db,
        where=wheres,
    )
    simulation_list = crud.simulation.get_multi_where_array_v2(
        db=db,
        where=wheres,
        order_by=order_by,
        order=order,
        limit=limit,
        skip=offset,
        relations=relations,
        base_columns=base_columns,
    )

    return {
        "count": count,
        "data": simulation_list,
    }


@router.post("")
def create_simulation(
    *,
    db: Session = Depends(get_session),
    simulation_in: schemas.SimulationCreate,
    current_user: Any = Depends(get_user),
    customer:Any=Depends(get_optional_customer),
    lang: str = Depends(get_current_language)
) -> Any:
    """
    Create new crud.simulation.
    """
    if simulation_in.country_id is None:
        code_alpha = get_country_name_from_coordinates(simulation_in.latitude, simulation_in.longitude)
        articles = {"la", "le", "les", "l'", "l’"} 
        country_name = simulation_in.country or ""
        country_parts = country_name.split()
        if country_parts and country_parts[0].lower() in articles:
            country_parts = country_parts[1:]
        cleaned_country_name = " ".join(country_parts)
        if simulation_in.region_id is None:
            if code_alpha:
                region_obj = crud.region.get_by_short_name(db=db, short_name=code_alpha,normalized_name_param=cleaned_country_name)
                if region_obj:
                    simulation_in.region_id = region_obj.id
                    country_obj = crud.country.get_by_id(db=db, country_id=region_obj.country_id)
                    if country_obj:
                        simulation_in.country_id = country_obj.id
                        simulation_in.country = country_obj.name
                        simulation_in.simulation_carbon["country"] = {
                            "name": country_obj.name,
                            "code_alpha_2": country_obj.code_alpha_2,
                            "normalized_name": country_obj.normalized_name,
                            "code_alpha_3": country_obj.code_alpha_3,
                            "id": country_obj.id
                        }
                else:
                    country_obj = crud.country.get_by_code_alpha_2(db=db, code_alpha_2=code_alpha)
                    if country_obj:
                        simulation_in.country_id = country_obj.id
                    else:
                       raise HTTPException(status_code=404, detail="Coordinates not found")
        else:
            region_obj = crud.region.get_country_by_region_id(db=db, region_id=simulation_in.region_id)
            if region_obj:
                simulation_in.country_id = region_obj.country_id
                country_obj = crud.country.get_by_id(db=db, country_id=region_obj.country_id)
                if country_obj:
                    simulation_in.country_id = country_obj.id
                    simulation_in.country = country_obj.name
                    simulation_in.simulation_carbon["country"] = {
                        "name": country_obj.name,
                        "code_alpha_2": country_obj.code_alpha_2,
                        "normalized_name": country_obj.normalized_name,
                        "code_alpha_3": country_obj.code_alpha_3,
                        "id": country_obj.id
                    }
            else:
                raise HTTPException(status_code=404, detail="Coordinates not found")          
    if not customer:
        raise HTTPException(status_code=404, detail="customer not found")

    subscription = crud.simulation.get_current_subscription_by_customer_auth_id(
        db=db, auth_user_id=customer.auth_user_id
    )
    if not subscription:
        subscription = crud.subscription.get_or_create_subscription_pvgis24(
            db=db, customer_id=customer.id
        )
        if not subscription:
            raise HTTPException(
                status_code=500, detail="Failed to create a subscription for the customer"
            )
            
    # Check if free customer already created a project.
    created_simulation_count = crud.simulation.get_simulation_count_by_customer_id(db, customer.id)
    
    if subscription.id == 11142024 and created_simulation_count > 1:
        raise HTTPException(
                status_code=403, detail="Non subscribed user can only create one sub"
            )
        
    simulation = crud.simulation.create_simulation_and_deduct_subscription(
        db=db,
        subscription=subscription,
        simulation_in=simulation_in,
        current_user=current_user,
    )
    customer=crud.customer.get_first_where_array(db=db,where=[{"key":"auth_user_id","operator":"==","value":current_user["id"]}])

    # Folder creation notification
    users = [
        {
            "id": current_user["id"],
            "roleId": 2,
            "appKey": "1284939136",
            "vars": {"name": simulation.name},
        }
    ]
    send_notif(event_code="folder-creation", users=users, mailLanguage=lang)

    notification_credit_remaining = False
    if subscription.product_json:
        monthly_credit = subscription.product_json.get('monthly_credit', 0)
        if monthly_credit <= 10 and subscription.credit_balance <= 3:
            notification_credit_remaining = True
        elif monthly_credit > 10 and subscription.credit_balance < 10:
            notification_credit_remaining = True

    if (notification_credit_remaining):
        product_json = subscription.product_json
        subscription_name = product_json.get("name") if isinstance(product_json, dict) else None
        users = [
            {
                "id": current_user["id"],
                "roleId": 2,
                "appKey": "1284939136",
                "vars": {
                    "firstname": customer.first_name,
                    "lastname": customer.last_name,
                    "limit": subscription.credit_balance,
                    "subscriptionName": subscription_name,
                    "url":f"{settings.PVGIS_UI_URL}/pvgis24/app/{customer.id}/subscription",
                    "customerId":customer.id
                },
            }
        ]
        send_notif(event_code="subscription", users=users, mailLanguage=lang)

    return simulation

@router.post("/pvgis24")
def create_folder_pvgis24(
    *,
    db: Session = Depends(get_session),
    simulation_in: schemas.SimulationCreate,
    current_user: Any = Depends(get_user),
    customer:Any=Depends(get_optional_customer),
    lang: str = Depends(get_current_language)
) -> Any:
    """
    Create folder in PVGIS24 Calculator
    """

    if not customer:
        raise HTTPException(status_code=404, detail="pvgis.error.customer_not_fount")

    subscription = crud.subscription.get_or_create_subscription_pvgis24(
        db=db, customer_id=customer.id
    )

    if not subscription:
        raise HTTPException(
            status_code=403, detail="pvgis.error.subscription_not_found"
        )
        
    # Check if free customer already created a project.
    created_simulation_count = crud.simulation.get_simulation_count_by_customer_id(db, customer.id)
    if subscription.id == 11142024 and created_simulation_count > 1:
        raise HTTPException(
                status_code=403, detail="Non subscribed user can only create one sub"
            )
        
    simulation_in.subscription_id = subscription.id

    simulation = crud.simulation.create(
        db=db, obj_in=simulation_in, user_id=current_user["id"]
    )

    # Folder creation notification
    users = [
        {
            "id": current_user["id"],
            "roleId": 2,
            "appKey": "1284939136",
            "vars": {"name": simulation.name},
        }
    ]
    send_notif(event_code="folder-creation", users=users, mailLanguage=lang)

    return simulation

@router.put("/pvgis24/convert-to-financial/{id}")
def convert_folder_to_financial(
    *,
    id: int,
    db: Session = Depends(get_session),
    current_user: Any = Depends(get_user),
    customer:Any=Depends(get_optional_customer),
    lang: str = Depends(get_current_language)
):
    if not customer:
        raise HTTPException(status_code=404, detail="pvgis.error.customer_not_found")
    
    where_simulation=[{"key":"id", "operator":"==", "value": id}]
    relation_simulation=[r"subscription{subscription_status}"]
    simulation_obj = crud.simulation.get_first_where_array_v2(
        db=db,
        where=where_simulation,
        base_columns=["name"],
        relations=relation_simulation)
    
    if not simulation_obj: 
        raise HTTPException(status_code=404, detail="pvgis.error.folder_not_found")
    
    if(simulation_obj.subscription.subscription_status != SubscriptionStatusEnum.PVGIS24):
        return "pvgis.success.folder_converted_successfully"
    
    simulation_name = simulation_obj.name
    
    current_subscription = crud.simulation.get_current_subscription_by_customer_auth_id(
        db=db, auth_user_id=customer.auth_user_id
    )

    if not current_subscription:
        raise HTTPException(
            status_code=403, detail="pvgis.error.subscription_not_found"
        )
     
    # convert simulation pvgis24 to financial
    crud.simulation.convert_pvgis24_folder_to_financial(
        db=db,
        subscription=current_subscription,
        simulation_in=simulation_obj,
        user_id=current_user["id"],
    )
    
    notification_credit_remaining = False
    if current_subscription.product_json:
        monthly_credit = current_subscription.product_json.get('monthly_credit', 0)
        if monthly_credit <= 10 and current_subscription.credit_balance <= 3:
            notification_credit_remaining = True
        elif monthly_credit > 10 and current_subscription.credit_balance < 10:
            notification_credit_remaining = True

    if (notification_credit_remaining):
        product_json = current_subscription.product_json
        subscription_name = product_json.get("name") if isinstance(product_json, dict) else None
        users = [
            {
                "id": current_user["id"],
                "roleId": 2,
                "appKey": "1284939136",
                "vars": {
                    "firstname": customer.first_name,
                    "lastname": customer.last_name,
                    "limit": current_subscription.credit_balance,
                    "subscriptionName": subscription_name,
                    "url":f"{settings.PVGIS_UI_URL}/pvgis24/app/{customer.id}/subscription",
                    "customerId":customer.id
                },
            }
        ]
        send_notif(event_code="subscription", users=users, mailLanguage=lang)

    return "pvgis.success.folder_converted_successfully"

@router.get("/check/simulation-name")
def check_existing_simulation(
    name: str,
    db: Session = Depends(get_session)
) -> Any:
    """
    Check if a simulation exists by name or name without spaces.
    """
    existing_simulation = crud.simulation.find_existing_simulation_by_name(db=db, name=name)

    if existing_simulation:
        return {
            "status": "success",
            "message": "Simulation name exists",
            "data": existing_simulation
        }

    # If no match is found, raise a 404 error
    raise HTTPException(status_code=404, detail="Simulation name does not exist")

@router.get("/{id}")
def read_subscription_user(
    *,
    db: Session = Depends(get_session),
    id: int,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Get simulation by ID.
    """
    data = crud.simulation.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="Simulation not found")

    return data


@router.put("/{id}", response_model=SimulationUpdate)
def update_subscription_user(
    *,
    db: Session = Depends(get_session),
    id: int,
    simulation_in: schemas.SimulationUpdate,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Update an crud.simulation.
    """
    data = crud.simulation.get(db=db, id=id)
    simulation_in.updated_at= func.now()
    
    if not data:
        raise HTTPException(status_code=404, detail="Issue not found")

    data = crud.simulation.update(db=db, db_obj=data, obj_in=simulation_in)

    return to_dict(data)


@router.delete("/{id}", response_model=Any)
def delete_subscription_user(
    *,
    db: Session = Depends(get_session),
    id: int,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Delete an crud.simulation.
    """

    data = crud.simulation.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="Product not found")

    return to_dict(crud.simulation.soft_delete(db=db, id=id))


@router.get("/count/{customer_id}")
def get_created_simulation(
    *,
    db: Session = Depends(get_session),
    customer_id: int,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Get count of simulations created for a specific customer.
    Returns both the total count and the count for the current month.
    """
    total_count = crud.simulation.get_count_where_array(
        db=db,
        where=[
            {"key": "subscription.customer_id", "value": customer_id, "operator": "=="}
        ],
    )
    current_year = func.extract("year", func.now())
    
    current_year_count = crud.simulation.get_count_where_array(
        db=db,
        where=[
            {"key": "subscription.customer_id", "value": customer_id, "operator": "=="},
            {"key": "created_at", "operator": "year", "value": current_year},
        ],
    )
    
    current_month = func.extract("month", func.now())
    current_month_count = crud.simulation.get_count_where_array(
        db=db,
        where=[
            {"key": "subscription.customer_id", "value": customer_id, "operator": "=="},
            {"key": "created_at", "operator": "month", "value": current_month},
            {"key": "created_at", "operator": "year", "value": current_year},
        ],
    )

    return {
        "total_count": total_count,
        "current_month_count": current_month_count,
        "current_year_count": current_year_count
    }