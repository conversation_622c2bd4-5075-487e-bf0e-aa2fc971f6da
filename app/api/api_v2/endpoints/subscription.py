from ast import List
import ast
from typing import Any
from app.crud.pvgis_service import generatePaymentReference
from fastapi import APIRouter, Depends, HTTPException
from fastapi.encoders import jsonable_encoder
from sqlalchemy.orm import Session
from sqlalchemy import func
from app.models.product import Product
from app.services.notification.notification import send_notif

from app.api.api_v2.deps import get_user,get_optional_customer, get_current_language
from app.config.db.database import get_session
from app.schemas.subscription import SubscriptionCreate, SubscriptionUpdate,SubscriptionStatusEnum
from app.schemas.subscription_payment_transaction import SubscriptionPaymentTransactionCreate
from app import crud
from app.utils.utils import to_dict
from app import schemas
from datetime import datetime
from app.enums.payment_status_enum import PaymentStatusEnum
from functools import reduce
import stripe
from app.config.settings import get_settings
router = APIRouter()
from datetime import datetime,timedelta
from app.services.stripe import stripe_service

settings=get_settings()
@router.get("")
def read_subscriptions(
    db: Session = Depends(get_session),
    offset: int = 0,
    limit: int = 100,
    where: str = "",
    order_by: str = "id",
    order: str = "ASC",
    relations: str="[]",
    base_columns: str = "['created_at']"
) -> Any:
    wheres = []
    base_columns = ast.literal_eval(base_columns)
    relations = ast.literal_eval(relations)
    if where is not None and where != "" and where != []:
        wheres += ast.literal_eval(where)
    count = crud.subscription.get_count_where_array(
        db=db,
        where=wheres,
    )
    subscription_list = crud.subscription.get_multi_where_array_v2(
        db=db,
        where=wheres,
        order_by=order_by,
        order=order,
        limit=limit,
        skip=offset,
        relations=relations,
        base_columns=base_columns,
    )
    return {
        "count": count,
        "data": subscription_list,
    }
    

    
    
    
@router.get("/count/{customerId}")
def count_subscription_of_customer(
    db: Session = Depends(get_session),
    customerId: int = 1,
) -> Any:
    wheres = [
        {
            "key" : "customer_id",
            "operator": "==",
            "value" : customerId
        },
        {
            "key": "product_json", 
            "operator": "isNotNull"
        }  
    ]
    base_columns = ["id"]
    relations = []
    
    subscription_list = crud.subscription.get_multi_where_array_v2(
        db=db,
        where=wheres,
        relations=relations,
        base_columns=base_columns,
    )
    result = reduce(lambda acc, item: {**acc, item.product_json["id"]: acc.get(item.product_json["id"], 0) + 1}, subscription_list, {})


    return result




@router.get("/last_active")
def read_last_active_subscription(
    db: Session = Depends(get_session),
    where: str = "",
    relations: str="[]",
    base_columns: str = "['created_at']",
    customer:Any=Depends(get_optional_customer),
    is_not_cancelled:bool=False,
    include_stripe_object:bool=False,
    must_have_started: bool= True
) -> Any:
    if not customer: 
         raise HTTPException(status_code=404, detail="Customer not found")
    wheres = [
        {
            "key": "customer_id",
            "value": customer.id,
            "operator": "==",
        },
        {
            "key": "subscription_status",
            "value": "active",
            "operator": "==",
        },
        *([
            {
            "key":"start_date",
            "operator":"<=",
            "value":func.now()
            }
            ] if must_have_started else []
          ),
        
        
        # This endpoint returns the ongoing subscription, no matter the expired
        # {
        #     "key":"expired_date",
        #     "operator":">=",
        #     "value":func.now()
        # },
        {
            "key":"product_json",
            "operator":"isNotNull"
        }
        
        # {
        #     "key": "disabled_at",
        #     "operator": "isNull",
        # },
        # start_date<now<expired_date
        # {
        #     "key": "credit_balance",
        #     "value": 0,
        #     "operator": ">",
        # },
    ]
    if is_not_cancelled:
        wheres.append({"key":"disabled_at","operator":"isNull",})
    base_columns = ast.literal_eval(base_columns)
    relations = ast.literal_eval(relations)
    if where is not None and where != "" and where != []:
        wheres += ast.literal_eval(where)
    subscription = crud.subscription.get_first_where_array_v2(
        db=db,
        where=wheres,
        relations=relations,
        base_columns=base_columns,
    )
    
    if subscription:
        if include_stripe_object:
            if subscription.subscription_stripe_id:
                stripe_subscription = stripe.Subscription.retrieve(subscription.subscription_stripe_id)
            else :
                stripe_subscription = stripe_service.get_latest_active_subscription(customer.stripe_customer_id)
            subscription.stripe_subscription = stripe_subscription
        else:
            subscription.stripe_subscription = None
    
    return subscription


@router.post("", response_model=Any)
def create_subscription(
    *,
    db: Session = Depends(get_session),
    subscription_in: schemas.SubscriptionCreate,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Create new subscription.
    """
    
    
    # data = crud.subscription.create_subsription(db=db, subscription_in=subscription_in, user_id=current_user["id"])
    return {"message": "Needs update"}

@router.get("/{id}")
def read_subscription(
    *,
    db: Session = Depends(get_session),
    id: int,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Get subscription by ID.
    """
    data = crud.subscription.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="Product not found")

    return data


@router.put("/{id}", response_model=SubscriptionUpdate)
def update_subscription(
    *,
    db: Session = Depends(get_session),
    id: int,
    product_in: schemas.SubscriptionUpdate,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Update an subscription.
    """
    data = crud.subscription.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="Issue not found")

    data = crud.subscription.update(db=db, db_obj=data, obj_in=product_in)

    return to_dict(data)

@router.put("/cancel/{id}", response_model=SubscriptionUpdate)
def update_subscription(
    *,
    db: Session = Depends(get_session),
    id: int,
    sub_in: schemas.SubscriptionUpdate,
    current_user: Any = Depends(get_user),
    current_customer: Any= Depends(get_optional_customer),
    lang: str = Depends(get_current_language)
    
) -> Any:
    """
    Update an subscription.
    """
    data = crud.subscription.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="subscription_not_found")
    
    if data.customer_id != current_customer.id:
        raise HTTPException(status_code=403, detail="customer_ownership_error")
    
    if not current_customer.stripe_customer_id:
        raise HTTPException(status_code=404, detail="stripe_customer_required")
    
    new_subscription = crud.subscription.update(db=db, db_obj=data, obj_in=sub_in)
    
    customer_stripe_id = current_customer.stripe_customer_id

    # Get active subscription for the customer
    subscription_stripe_ob = (
            stripe.Subscription.retrieve(data.subscription_stripe_id) 
            if data.subscription_stripe_id 
            else stripe_service.get_latest_active_subscription(customer_stripe_id)
        )
    
    # Void previous invoice
    stripe_service.void_latest_invoice(subscription_stripe_ob)

    # Unsubscribe from Stripe
    canceled_subscription = stripe.Subscription.modify(
            subscription_stripe_ob['id'],
            cancel_at_period_end=True  # Cancels the subscription at the end of the current period
        )
        
        
        
    # send notification
    users = [
        {
            "id": current_user["id"],
            "roleId": 2,
            "appKey": "1284939136",
             "vars": {
                "customerUsername": new_subscription.customer.pseudo or new_subscription.customer.first_name+" "+new_subscription.customer.last_name ,
                "subscriptionName": new_subscription.product_json['name'],
                "startedDate": new_subscription.start_date.strftime("%d/%m/%Y"),
                "expiredDate": new_subscription.expired_date.strftime("%d/%m/%Y"),
                "url":f"{settings.PVGIS_UI_URL}/pvgis24/app/"+str(new_subscription.customer_id)+"/subscription",
            }
        }
    ]
    send_notif(event_code="subscription-canceled", users=users, mailLanguage=lang)
    return to_dict(new_subscription)


@router.delete("/{id}", response_model=Any)
def delete_subscription(
    *,
    db: Session = Depends(get_session),
    id: int,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Delete an subscription.
    """

    data = crud.subscription.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="Product not found")

    return to_dict(crud.subscription.soft_delete(db=db, id=id))



@router.post("/cron_job/notify_discount_customer", response_model=Any)
def notify_discount_customer_will_end(
    *,
    x_app_key: str,
    db: Session = Depends(get_session),
) -> Any:
    """
    Cron Discount Customer
    """
    if x_app_key != settings.X_APP_KEY:
        return HTTPException(status_code=401, detail="Unauthorized")
    three_days_past=datetime.now()+ timedelta(days=3)
    three_days_past_formatted=three_days_past.strftime("%Y-%m-%d")
    relations=["subscription","subscription{product_json}","subscription{coupon_end}","subscription{is_auto_renew}"]
    base_columns=["first_name","last_name"]
    data = crud.customer.get_multi_where_array_v2(db=db,
        where=[{"key":"subscription.coupon_end","operator":"date","value":three_days_past_formatted}],
        relations=relations,
        base_columns=base_columns
    )
    
    for cust in data:
        if not cust.subscription.is_auto_renew:
            continue
        card=stripe_service.get_default_card(cust.stripe_customer_id)
        card_number=f" {card['brand']} **** **** **** {card['last4']}"
        
        subscription_name=cust.subscription.product_json["name"]
        subscription_price=Product.get_price_from_json(cust.subscription.product_json)  
        customer_name=f"{cust.first_name} {cust.last_name}"
        expiredDate=cust.subscription.coupon_end
        send_cron_coupon_before_end_notif(cust.auth_user_id,customer_name,card_number,subscription_name,subscription_price,expiredDate)
    
    # disable in auth


    return {"status":200,"message":"cron discount customers coupon end successfull"}

def send_cron_coupon_before_end_notif(auth_user_id,customer_name,card_number,subscription_name,subscription_price,expiredDate, lang: str = "en"):
    notification_data = [
        {
            "id": auth_user_id,
            "roleId": 2,
            "appKey": settings.APP_KEY,
            "vars": {
                "customerUsername":customer_name,
                "subscriptionName":subscription_name,
                "subscriptionPrice":subscription_price,
                "cardNumber":card_number,
                "expiredDate":expiredDate.strftime("%d/%m/%Y")
            }
        }
    ]
        
    # Send notification
    send_notif(event_code="subscription-coupon-will-end", users=notification_data, mailLanguage=lang)
    

    