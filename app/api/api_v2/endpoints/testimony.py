import random
from typing import Any

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app import crud
from app import schemas
from app.api.api_v2.deps import get_user
from app.config.db.database import get_session
from app.data import testimony as testimonial
from app.models.testimony import Testimony
from app.config.settings import get_settings
settings = get_settings()

router = APIRouter()

import ast


@router.get("")
def read_testimony(
        db: Session = Depends(get_session),
        offset: int = 0,
        limit: int = 100,
        where: str = "",
        order_by: str = "id",
        order: str = "ASC",
        relations: str = "['country{name}']",
        base_columns: str = "['created_at','languages','flag','testimony','is_default','country_id','name',"
                            "'base_language']"
) -> Any:
    language = ['EN', 'FR']
    wheres = []
    random_value = random.choice(language)
    wheres_en = [
        {
            'key': 'base_language',
            'operator': '==',
            'value': random_value
        }
    ]
    wheres_default = [
        {
            'key': 'is_default',
            'operator': '==',
            'value': True
        }
    ]
    base_columns = ast.literal_eval(base_columns)
    relations = ast.literal_eval(relations)
    if where is not None and where != "" and where != []:
        wheres += ast.literal_eval(where)

    count_default = crud.testimony.get_count_where_array(
        db=db,
        where=wheres_default,
    )
    testimony_list_default = crud.testimony.get_multi_where_array_v2(
        db=db,
        where=wheres_default,
        order_by=order_by,
        order=order,
        limit=limit,
        skip=offset,
        relations=relations,
        base_columns=base_columns,
    )
    count = crud.testimony.get_count_where_array(
        db=db,
        where=wheres,
    )
    if count != 0:
        testimony_list = crud.testimony.get_multi_where_array_v2(
            db=db,
            where=wheres,
            order_by=order_by,
            order=order,
            limit=limit,
            skip=offset,
            relations=relations,
            base_columns=base_columns,
        )
    else:
        count = crud.testimony.get_count_where_array(
            db=db,
            where=wheres_en,
        )
        testimony_list = crud.testimony.get_multi_where_array_v2(
            db=db,
            where=wheres_en,
            order_by=order_by,
            order=order,
            limit=limit,
            skip=offset,
            relations=relations,
            base_columns=base_columns,
        )

    return {
        "count": count + count_default,
        "data": testimony_list + testimony_list_default,
    }


@router.get("/admin")
def read_testimony(
        db: Session = Depends(get_session),
        offset: int = 0,
        limit: int = 100,
        where: str = "",
        order_by: str = "id",
        order: str = "ASC",
        relations: str = "['country{name}']",
        base_columns: str = "['created_at','languages','flag','testimony','is_default','country_id','name',"
                            "'base_language']"
) -> Any:
    base_columns = ast.literal_eval(base_columns)
    relations = ast.literal_eval(relations)
    wheres = []
    if where is not None and where != "" and where != []:
        wheres += ast.literal_eval(where)
    count = crud.testimony.get_count_where_array(
        db=db,
        where=wheres,
    )
    testimony_list = crud.testimony.get_multi_where_array_v2(
        db=db,
        where=wheres,
        order_by=order_by,
        order=order,
        limit=limit,
        skip=offset,
        relations=relations,
        base_columns=base_columns,
    )

    return {
        "count": count,
        "data": testimony_list
    }


@router.post("/", response_model=Any)
def create_testimony(
        *,
        db: Session = Depends(get_session),
        testimony_in: schemas.TestimonyCreate,
        X_APP_KEY: str
) -> Any:
    """
    Create new crud.testimony.
    """
    if X_APP_KEY != settings.X_APP_KEY:
        raise HTTPException(status_code=404, detail="Invalid X_APP_KEY ")

    data = crud.testimony.create(db=db, obj_in=testimony_in)

    return data


@router.post("bulk_insert", response_model=Any)
def insert_testimony(
        *,
        db: Session = Depends(get_session),
        current_user: Any = Depends(get_user),
) -> Any:
    """
    Create new crud.testimony.
    """
    existing = crud.testimony.get_count(db=db)
    if existing > 0:
        return {"message": f"Successfully inserted 0 testimonies"}
    try:
        testimony_all = testimonial.testimonyAll
        data_to_insert = []

        for testimony_ in testimony_all:
            for testimony_value in testimony_['testimony']:
                country_id = None
                if testimony_['base_language']:
                    country = crud.country.get_by_code_alpha_2(
                        db=db,
                        code_alpha_2=testimony_['base_language'].upper()
                    )
                    country_id = country.id if country else None  # Use None instead of 0 for foreign keys
                # Create Pydantic model to validate input

                if not country_id:
                    country_ = crud.country.get_by_code_alpha_2(
                        db=db,
                        code_alpha_2=testimony_value['flag'].upper()
                    )
                    country_id = country_.id if country_ else None

                testimony_in = schemas.TestimonyCreate(
                    **testimony_value,
                    base_language=testimony_['base_language'],
                    country_id=country_id
                )

                # Convert to dictionary for SQLAlchemy, ensuring column names match exactly
                insert_dict = testimony_in.dict()
                data_to_insert.append(insert_dict)

        # Bulk insert - ensure Testimony is your SQLAlchemy model class
        db.bulk_insert_mappings(Testimony, data_to_insert)
        db.commit()

        return {"message": f"Successfully inserted {len(data_to_insert)} testimonies"}
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"Failed to insert testimonies: {str(e)}"
        )
    finally:
        db.close()


@router.get("/")
def read_testimony(
        *,
        db: Session = Depends(get_session),
        testimony_id: int,
        X_APP_KEY: str,
) -> Any:
    """
    Get testimony by ID.
    """
    if X_APP_KEY != settings.X_APP_KEY:
        raise HTTPException(status_code=404, detail="Invalid X_APP_KEY ")
    data = crud.testimony.get(db=db, id=testimony_id)

    if not data:
        raise HTTPException(status_code=404, detail="testimony_not_found")

    return data


@router.put("/")
def update_testimony(
        *,
        db: Session = Depends(get_session),
        testimony_id: int,
        testimony_in: schemas.TestimonyUpdate,
        X_APP_KEY: str,
) -> Any:
    """
    Update an crud.testimony.
    """
    if X_APP_KEY != settings.X_APP_KEY:
        raise HTTPException(status_code=404, detail="Invalid X_APP_KEY ")
    data = crud.testimony.get(db=db, id=testimony_id)

    if not data:
        raise HTTPException(status_code=404, detail="testimony_not_found")

    data = crud.testimony.update(db=db, db_obj=data, obj_in=testimony_in)

    return data


@router.delete("/", response_model=Any)
def delete_testimony(
        *,
        db: Session = Depends(get_session),
        testimony_id: int,
        X_APP_KEY: str,
) -> Any:
    """
    Delete an crud.testimony.
    """
    if X_APP_KEY != settings.X_APP_KEY:
        raise HTTPException(status_code=404, detail="Invalid X_APP_KEY ")

    data = crud.testimony.get(db=db, id=testimony_id)

    if not data:
        raise HTTPException(status_code=404, detail="testimony_not_found")

    return crud.testimony.soft_delete(db=db, id=testimony_id)
