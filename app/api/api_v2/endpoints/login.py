from typing import Any, Optional
from app.const.client_config import CLIENT_CONFIG
from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import RedirectResponse
from fastapi.security import OAuth2PasswordRequestForm
from fastapi.encoders import jsonable_encoder
from app.enums.subscription_status_enum import SubscriptionStatusEnum
from app.models import account_information
from sqlalchemy.orm import Session
from app.api.api_v2.deps import get_token, get_user
from app.config.db.database import get_session, SessionLocal
from app.config.settings import get_settings
from app.schemas.account_information import AccountInformationCreate
from app.schemas.customer import (
    CustomerCreate,
    RegistrationData,
    Token,
)
from app.schemas.invitation import InvitationAccept
from app.services.auth_service import auto_login, create_auth_user_from_invitation, get_user_by_id, \
    get_user_profile_by_username, get_user_role_by_id
from app.services.subscription_service import get_user_and_subscription_user
from google.auth.exceptions import GoogleAuthError
from google_auth_oauthlib.flow import Flow
from google.oauth2 import id_token
from google.auth.transport import requests as reqs
from sqlalchemy.exc import SQLAlchemyError
from app.api.api_v2.deps import get_current_language
from app import crud

import requests
import jwt
from app import schemas
import urllib.parse
from app.services.notification.notification import send_notif
from datetime import datetime, timedelta
from app.const.default_data import DEFAULT_REFERENTIAL_DATA
from app.services.city_migration_service import get_timezone_offset

settings = get_settings()

router = APIRouter()


@router.get('/token-user-data')
def get_token_user_data(
        *,
        db: Session = Depends(get_session),
        current_user: Any = Depends(get_user),
) -> Any:
    core_customer = crud.customer.get_by_user_id(db, current_user['id'])
    customer = crud.customer.get(db=db, id=core_customer.id, relations=["account_information"])
    user = get_user_by_id(current_user["id"])
    user_profile = get_user_profile_by_username(user['username'])
    if not user_profile or len(user_profile) == 0:
        raise HTTPException(
            status_code=400, detail="No user profile found"
        )
    user_role = get_user_role_by_id(current_user["id"])
    if not user_role:
        raise HTTPException(
            status_code=400, detail="No user role found"
        )
    found_customer, found_subscription_user = get_user_and_subscription_user(db, current_user["id"])
    user_type = "customer" if found_customer else "subscription_user"
    main_user_data = {
        "customer": customer,
        "role": user_role['role']['role'],
        "user": {
            "username": user['username'],
            "firstname": user_profile[0]['userProfile']['firstname'],
            "lastname": user_profile[0]['userProfile']['lastname'],
            "pseudo": user['pseudo'],
        },
        "user_type": user_type,
        'user_id': current_user["id"],
        "subscription_users": jsonable_encoder(found_subscription_user)
    }
    # user-app/all-user/exiration_test%40yopmail.com
    return main_user_data


@router.get('/create-and-auto-login')
def create_and_auto_login(
        *,
        db: Session = Depends(get_session),
        invitation_id: int,
        x_app_key: str,
) -> Any:
    if x_app_key != settings.X_APP_KEY:
        return HTTPException(status_code=401)
    invitation = crud.invitation.get(db, id=invitation_id)
    core_customer = crud.customer.get(db, id=invitation.customer_id)
    
    if core_customer.auth_user_id:
        return HTTPException(status_code=401, detail="User already created")
    first_name = core_customer.first_name or invitation.email.split("@")[0]
    
    create_auth_user_from_invitation(db, invitation, InvitationAccept(
        customer_id=core_customer.id,
        email=invitation.email,
        first_name=first_name, 
        last_name=core_customer.last_name or "",
        company_name=None,
    ), core_customer)

    main_user_data = auto_login(db, core_customer)

    return main_user_data


@router.get('/auto-login')
def auto_login_by_auth_user_id(
        *,
        db: Session = Depends(get_session),
        auth_user_id: int,
        x_app_key: str,
) -> Any:
    if x_app_key != settings.X_APP_KEY:
        return HTTPException(status_code=401)

    core_customer = crud.customer.get_by_user_id(db, auth_user_id)
    main_user_data = auto_login(db, core_customer)
    return main_user_data


@router.post("/register")
def register(
        *,
        db: Session = Depends(get_session),
        received_data: RegistrationData,
) -> Any:
    """
    Create new user.
    - send payload to auth.api.com
    - wait for response
    """
    customer = crud.login.main_function(db=db, received_data=received_data)

    created_customer = crud.customer.get_first_where_array_v2(
        db=db,
        where=[
            {
                "key": "email",
                "operator": "==",
                "value": received_data.email,
            },
        ],
    )

    if created_customer:
        crud.account_notification_setting.check_all_by_customer_id(
            db=db, customer_id=created_customer.id
        )

    return customer


@router.post("/register/without_verification")
def register_without_verification(
        *,
        db: Session = Depends(get_session),
        received_data: RegistrationData,
) -> Any:
    """
    Create new user.
    - send payload to auth.api.com
    - wait for response
    """
    customer = crud.login.main_function(db=db, received_data=received_data, verification=False)

    created_customer = crud.customer.get_first_where_array_v2(
        db=db,
        where=[
            {
                "key": "email",
                "operator": "==",
                "value": received_data.email,
            },
        ],
    )

    if created_customer:
        crud.account_notification_setting.check_all_by_customer_id(
            db=db, customer_id=created_customer.id
        )

    return customer


@router.post("/login/access-token")
def login_access_token(
        form_data: OAuth2PasswordRequestForm = Depends(),
        db: Session = Depends(get_session),
) -> Token:
    """
    OAuth2 compatible token login, get an access token for future requests
    """
    return crud.login.authenticate(
        db, username=form_data.username, password=form_data.password
    )


@router.post("/logout")
def logout(
        user_token: Any = Depends(get_token),
):
    response = requests.post(
        f"{settings.AUTH_API_URL}/user/signout",
        headers={"Authorization": f"Bearer {user_token}"},
    )

    raise HTTPException(status_code=response.status_code, detail=response.json())


@router.get("/google")
async def google_auth(
        lang: str = "en",
        country_code: Optional[str] = None,
):
    redirect_uri = settings.get_google_auth_url()
    try:
        flow = Flow.from_client_config(
            CLIENT_CONFIG,
            scopes=[
                "openid",
                "https://www.googleapis.com/auth/userinfo.email",
                "https://www.googleapis.com/auth/userinfo.profile",
            ],
            redirect_uri=redirect_uri,
        )
        state = urllib.parse.urlencode({"lang": lang, "country_code": country_code})
        authorization_url, _ = flow.authorization_url(prompt="consent", state=state)
        return {"authorization_url": authorization_url}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/google/callback")
def google_auth_callback(
        *,
        code: str,
        db: Session = Depends(get_session),
        state: Any,
):
    return crud.login.google_auth_callback(db=db, code=code, state=state)


@router.get("/facebook")
async def facebook_auth():
    return {
        "authorization_url": f"https://www.facebook.com/v12.0/dialog/oauth?client_id={settings.FACEBOOK_CLIENT_ID}&redirect_uri={settings.get_facebook_auth_url()}&scope=email"
    }


@router.get("/facebook/callback")
async def facebook_auth_callback(
        *,
        code: str,
        db: Session = Depends(get_session),
):
    # Exchange code for access token
    token_url = "https://graph.facebook.com/v12.0/oauth/access_token"
    token_params = {
        "client_id": settings.FACEBOOK_CLIENT_ID,
        "redirect_uri": settings.get_facebook_auth_url(),
        "client_secret": settings.FACEBOOK_CLIENT_SECRET,
        "code": code,
    }
    response = requests.get(token_url, params=token_params)
    token_data = response.json()

    if "access_token" not in token_data:
        raise HTTPException(status_code=400, detail="Could not retrieve access token")

    # Get user info
    user_info_url = "https://graph.facebook.com/me"
    user_info_params = {
        "fields": "id,name,email",
        "access_token": token_data["access_token"],
    }
    response = requests.get(user_info_url, params=user_info_params)
    user_info = response.json()

    user_name = user_info["name"].split()

    try:
        # Start transaction
        db.begin(subtransactions=True)

        # Create customer in local database first
        user_in = CustomerCreate(
            first_name=user_name[0],
            last_name=user_name[1] if len(user_name) > 1 else "",
            email=user_info["email"],
        )

        created_customer = crud.customer.create(
            db=db,
            obj_in=user_in,
            commit=False,
            refresh=False,
        )

        user = {
            "firstname": created_customer.first_name,
            "lastname": created_customer.last_name,
            "username": created_customer.email,
            "appId": settings.APPID,
            "roleId": settings.ROLEID,
            "sentByApp": settings.APP_KEY,
            "sentByProcess": "registration",
        }

        response = requests.post(
            f"{settings.AUTH_API_URL}/user/social-networks/signup",
            json=user,
            headers=settings.x_app_key_header,
        )

        if response.status_code != 201:
            db.rollback()
            error_detail = response.json()
            return RedirectResponse(
                url=f"{settings.PVGIS_UI_URL}/register?errorMessage={str(error_detail['message'])}"
            )

        auth_user = response.json()["userCreated"]

        # Update customer with auth_user_id
        crud.customer.update(
            db=db,
            db_obj=created_customer,
            obj_in={"auth_user_id": auth_user["id"]},
            commit=False,
        )

        account_information_in = AccountInformationCreate(
            customer_id=created_customer.id,
            account_type_id=2,  # default as particular
        )

        crud.account_information.create(
            db=db,
            obj_in=account_information_in,
            user_id=auth_user["id"],
            commit=False,
            refresh=False,
        )

        # Commit transaction
        db.commit()

    except SQLAlchemyError as e:
        db.rollback()
        return RedirectResponse(
            url=f"{settings.PVGIS_UI_URL}/register?errorMessage=Database error occurred"
        )
    except Exception as e:
        db.rollback()
        return RedirectResponse(
            url=f"{settings.PVGIS_UI_URL}/register?errorMessage={str(e)}"
        )

    try:
        authenticate_user = crud.login.social_authenticate(
            db=db, username=auth_user["username"]
        )

        created_customer = crud.customer.get_first_where_array_v2(
            db=db,
            where=[
                {
                    "key": "email",
                    "operator": "==",
                    "value": user_info["email"],
                },
            ],
        )

        if created_customer:
            crud.account_notification_setting.check_all_by_customer_id(
                db=db, customer_id=created_customer.id
            )
    except Exception as e:
        return RedirectResponse(
            url=f"{settings.PVGIS_UI_URL}/login?errorMessage={str(e)}"
        )

    token = jwt.encode(
        {"data": authenticate_user}, settings.SECRET_KEY, algorithm="HS256"
    )

    return RedirectResponse(url=f"{settings.PVGIS_UI_URL}?token={token}")


@router.post("/login")
async def login(
        *,
        db: Session = Depends(get_session),
        data: schemas.LoginData,
):
     
    country = crud.country.get_first_where_array_v2(
        db=db,
        where=[{"key": "code_alpha_2", "value": data.country_code, "operator": "=="}]
    )
    

    if data.user_id:
        data.user_id = crud.customer.get_auth_id_by_customer_id(db, data.user_id)
        if not data.user_id:
            raise HTTPException(status_code=404, detail="Customer not found")

    auth_url = f"{settings.AUTH_API_URL}/user/signin"
    APP_KEY = settings.PVGIS_UI_APP_KEY
    ADMIN_APP_KEY = settings.PVGIS_ADMIN_APP_KEY
    payload = {
        "username": data.username,
        "password": data.password,
        "appKey": APP_KEY if not data.user_id else ADMIN_APP_KEY,
        "targetUserId": data.user_id,
    }
    response = requests.post(auth_url, data=payload)
    if response.status_code != 202:
        raise HTTPException(status_code=400, detail=response.json())
    auth_res = response.json()
    decoded_token = jwt.decode(
        auth_res["data"]["token"], options={"verify_signature": False}
    )
    decoded_user_id = decoded_token.get("id")
    is_admin = data.user_id and auth_res["data"]["role"] == "admin"
    user_id = data.user_id if is_admin else decoded_user_id
    # return {
    #     "auth_res": auth_res,
    #     "user_id": user_id,
    #     "decoded_token": decoded_token
    # }
    # auth_res = {"data": {}}
    # user_id = 4
    subscription_conditions = [
        # why we will need check disabled is null of subscription ?
        # {"key": "subscription.disabled_at", "operator":"isNull"},
        {"key": "subscription.deleted_at", "operator": "isNull"},
        {"key": "subscription.subscription_status", "value": SubscriptionStatusEnum.ACTIVE, "operator": "=="}
    ]
    found_customer = crud.customer.get_first_where_array(
        db=db,
        where=[
            {"key": "auth_user_id", "value": user_id, "operator": "=="},
            # {"key":"deleted_at","operator":"isNull"}, 
            # *subscription_conditions 
        ],
        relations=["account_information", "subscription"],
    )

    found_subscription_user = crud.subscription_user.get_first_where_array(
        db=db, where=[{"key": "auth_user_id", "value": user_id, "operator": "=="},
                      {"key": "is_active", "value": 1, "operator": "=="},
                      *subscription_conditions
                      ],
        relations=[r"subscription.customer.account_information"]
    )

    if not found_customer and not found_subscription_user:
        raise HTTPException(
            status_code=400, detail="Could not authenticate, Create Account first"
        )

    auth_res["data"]["user_type"] = "customer" if found_customer else "subscription_user"

    if not found_customer and found_subscription_user:
        found_customer = found_subscription_user.subscription.customer
        del found_customer.subscription.customer
        found_customer.subscription = found_subscription_user.subscription
        
    if not found_customer.country and country:
        crud.customer.update(
            db=db,
            db_obj=found_customer,
            obj_in={
                "country": country.name if country else None
            }
        )

    twenty_four_hours_from_now = datetime.now() + timedelta(hours=24)
    date_cond = found_customer.to_delete_at and found_customer.to_delete_at <= twenty_four_hours_from_now
    disabled_user_cond = found_customer and date_cond and not found_customer.subscription

    if (found_customer and found_customer.deleted_at) or disabled_user_cond:
        raise HTTPException(
            status_code=403, detail="pvgis.authentication_error.user_disabled"
        )

    auth_res["data"]["customer"] = found_customer
    auth_res["data"]["user_id"] = user_id
    auth_res["data"]["subscription_users"] = found_subscription_user

    # if not found_customer and found_subscription_user:
    #     subscription=crud.subscription.get(db=db,id=found_subscription_user.subscription_id)
    #     customer=crud.customer.get(db=db,id=subscription.customer_id,relations=['account_information'])
    #     auth_res["data"]["customer"] = customer

    return auth_res


@router.get("/login/google")
async def google_auth_login(
        *,
        country_code: Optional[str] = None,
        lang: Optional[str] = "en",
        redirect_url: Optional[str] = None,
):
    try:
        flow = Flow.from_client_config(
            CLIENT_CONFIG,
            scopes=[
                "openid",
                "https://www.googleapis.com/auth/userinfo.email",
                "https://www.googleapis.com/auth/userinfo.profile",
            ],
            redirect_uri=settings.get_google_login_auth_url(),
        )
        state_params = {}
        if country_code:
            state_params["country_code"] = country_code
        if lang:
            state_params["lang"] = lang
        if redirect_url:
            state_params["redirect_url"] = redirect_url

        authorization_url, _ = flow.authorization_url(
            prompt="consent",
            state=urllib.parse.urlencode(state_params)
        )
        return {"authorization_url": authorization_url}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/login/google/callback")
async def google_login_auth_callback(
        *,
        code: str,
        db: Session = Depends(get_session),
        state: Any,
):
    try:
        flow = Flow.from_client_config(
            CLIENT_CONFIG,
            scopes=[
                "openid",
                "https://www.googleapis.com/auth/userinfo.email",
                "https://www.googleapis.com/auth/userinfo.profile",
            ],
            redirect_uri=settings.get_google_login_auth_url(),
        )
    except ValueError as ve:
        raise HTTPException(
            status_code=500, detail=f"Invalid client configuration: {str(ve)}"
        )

    try:
        flow.fetch_token(code=code)
    except GoogleAuthError as ge:
        raise HTTPException(status_code=500, detail=f"Error fetching token: {str(ge)}")

    credentials = flow.credentials

    try:
        id_info = id_token.verify_oauth2_token(
            credentials.id_token,
            reqs.Request(),
            credentials.client_id,
            clock_skew_in_seconds=10,
        )
    except ValueError as ve:
        raise HTTPException(status_code=500, detail=f"Invalid token: {str(ve)}")

    if "email" not in id_info:
        raise HTTPException("Email not found in user info")

    country_code = urllib.parse.parse_qs(state).get("country_code", [None])[0]
    country = None
    if country_code and country_code.lower() != "undefined":
        country = crud.country.get_by_code_alpha_2(db=db, code_alpha_2=country_code)

    found_customer = crud.customer.get_first_where_array_v2(
        db=db,
        where=[{"key": "email", "value": id_info["email"], "operator": "=="}]
    )
    lang = urllib.parse.parse_qs(state).get("lang", ["en"])[0]
    if found_customer and not found_customer.country and country:
        crud.customer.update(
            db=db,
            db_obj=found_customer,
            obj_in={
                "timezone_offset": get_timezone_offset(country.code_alpha_2) if country else None,
                "country_id": country.id if country else None,
                "country": country.name if country else None,
            }
        )

    try:
        
        authenticate_user = crud.login.social_authenticate(
            db=db, username=id_info["email"]
        )
        if authenticate_user == "auth.error.user_not_found":
            try:
                # Start transaction
                db.begin(subtransactions=True)

                invited_user = crud.customer.get_first_where_array_v2(
                    db=db,
                    where=[
                        {"key": "email", "operator": "==", "value": id_info["email"]},
                        {"key": "auth_user_id", "operator": "isNull"}
                    ]
                )
                created_customer = None

                if invited_user:
                    # Update invited user with new information
                    created_customer = crud.customer.update(
                        db=db,
                        db_obj=invited_user,
                        obj_in={
                            "first_name": id_info.get("given_name", ""),
                            "last_name": id_info.get("family_name", ""),
                            "pseudo": id_info.get("given_name", "") + id_info.get("family_name", ""),
                            "accept_cgu": True,
                            "cart_reference": None,
                            "settings_json": {
                                "language": lang if lang else "en",
                                "simulation_language": lang if lang else "en",
                                "base_currency": {
                                    "name": "Euro",
                                    "code": "EUR",
                                    "symbol": "€",
                                    "default": "true"
                                }
                            }
                        },
                        commit=False
                    )
                else:
                    # Create customer in local database first
                    user_in = CustomerCreate(
                        first_name=id_info.get("given_name", ""),  # Add fallback
                        last_name=id_info.get("family_name", ""),  # Add fallback
                        email=id_info["email"],
                        timezone_offset=get_timezone_offset(country.code_alpha_2) if country else None,
                        country_id=country.id if country else None,
                        country=country.name if country else None,
                        settings_json={
                            "language": lang if lang else "en",
                            "simulation_language": lang if lang else "en",
                            "base_currency": {
                                "name": "Euro",
                                "code": "EUR",
                                "symbol": "€",
                                "default": "true"
                            }
                        },
                        default_referential_info_json=DEFAULT_REFERENTIAL_DATA,
                    )
                    created_customer = crud.customer.create(
                        db=db,
                        obj_in=user_in,
                        commit=False,
                        refresh=False,
                    )
                user = {
                    "firstname": created_customer.first_name,
                    "lastname": created_customer.last_name,
                    "username": created_customer.email,
                    "appId": settings.PVGIS_APP_ID,
                    "roleId": settings.CUSTOMER_ROLE_ID,
                    "language": lang,
                    "sentByApp": settings.APP_KEY,
                    "sentByProcess": "registration",
                }

                response = requests.post(
                    f"{settings.AUTH_API_URL}/user/social-networks/signup",
                    json=user,
                    headers=settings.x_app_key_header,
                )
                if response.status_code != 201:
                    db.rollback()
                    # Enable when using google login
                    #db = SessionLocal()
                    auth_user = user
                else:
                    res_auth = response.json()
                    auth_user = res_auth["userCreated"]
                    auth_user_id = str(auth_user["id"])

                    try:
                        crud.customer.update(
                            db=db,
                            db_obj=created_customer,
                            obj_in={"auth_user_id": auth_user["id"]},
                            commit=False,
                        )
                    except Exception as e:
                        print(f"Failed to update customer: {str(e)}")
                        raise HTTPException(status_code=500, detail="Failed to update customer")

                    try:
                        crud.account_notification_setting.check_all_by_customer_id(
                            db=db, customer_id=created_customer.id
                        )
                        
                    except Exception as e:
                        print(f"Failed to check account notification settings: {str(e)}")
                        raise HTTPException(status_code=500, detail="Failed to check account notification settings")

                    try:
                        account_information_in = AccountInformationCreate(
                            customer_id=created_customer.id,
                            account_type_id=2,  # default as particular
                        )

                        crud.account_information.create(
                            db=db,
                            obj_in=account_information_in,
                            user_id=auth_user["id"],
                            commit=False,
                            refresh=False,
                        )
                    except Exception as e:
                        print(f"Failed to create account information: {str(e)}")
                        raise HTTPException(status_code=500, detail="Failed to create account information")

                    users = [
                        {
                            "id": auth_user["id"],  # Convert to string to ensure serialization
                            "roleId": 2,
                            "appKey": "**********",
                            "vars": {
                                "name": created_customer.first_name or "",
                                "surname": created_customer.last_name or "",
                                "username": created_customer.email or "",
                                "lang": lang  # Convert to string to ensure correct formt
                            }
                        }
                    ]

                    # Commit transaction
                    db.commit()

                    try:
                        send_notif(event_code="first-registration-welcome", users=users, mailLanguage=lang)
                    except Exception as e:
                        print(f"Notification error: {str(e)}")  # Log but don't fail registration

            except SQLAlchemyError as e:
                db.rollback()
                print(e)
                return RedirectResponse(
                    url=f"{settings.PVGIS_UI_URL}/login?errorMessage=Database error occurred"
                )
            except Exception as e:
                print("========================")
                print(e)
                print("========================")
                db.rollback()
                return RedirectResponse(
                    url=f"{settings.PVGIS_UI_URL}/login?errorMessage=1{str(e)}"
                )

            try:
                authenticate_user = crud.login.social_authenticate(
                    db=db, username=auth_user["username"]
                )

                created_customer = crud.customer.get_first_where_array_v2(
                    db=db,
                    where=[
                        {
                            "key": "email",
                            "operator": "==",
                            "value": id_info["email"],
                        },
                    ],
                )

                if created_customer:
                    crud.account_notification_setting.check_all_by_customer_id(
                        db=db, customer_id=created_customer.id
                    )
            except Exception as e:
                return RedirectResponse(
                    url=f"{settings.PVGIS_UI_URL}/login?errorMessage=2{str(e)}"
                )

    except Exception as e:
        print(e )
        return RedirectResponse(
            url=f"{settings.PVGIS_UI_URL}/login?errorMessage={str(e)}"
        )
    
    token = jwt.encode(
        {"data": authenticate_user['data']['token']}, settings.SECRET_KEY, algorithm="HS256"
    )

    redirect_url = urllib.parse.parse_qs(state).get("redirect_url", [None])[0]
    base_url = f"{settings.PVGIS_UI_URL}?token={token}"

    if redirect_url:
        final_url = f"{base_url}&redirect={urllib.parse.quote(redirect_url)}"
    else:
        final_url = base_url

    return RedirectResponse(url=final_url)


@router.get("/login/facebook")
async def facebook_login_auth():
    return {
        "authorization_url": f"https://www.facebook.com/v12.0/dialog/oauth?client_id={settings.FACEBOOK_CLIENT_ID}&redirect_uri={settings.get_facebook_login_auth_url()}&scope=email"
    }


@router.get("/login/facebook/callback")
async def facebook_login_auth_callback(
        *,
        code: str,
        db: Session = Depends(get_session),
):
    # Exchange code for access token
    token_url = "https://graph.facebook.com/v12.0/oauth/access_token"
    token_params = {
        "client_id": settings.FACEBOOK_CLIENT_ID,
        "redirect_uri": settings.get_facebook_login_auth_url(),
        "client_secret": settings.FACEBOOK_CLIENT_SECRET,
        "code": code,
    }
    response = requests.get(token_url, params=token_params)
    token_data = response.json()

    if "access_token" not in token_data:
        raise HTTPException(status_code=400, detail="Could not retrieve access token")

    # Get user info
    user_info_url = "https://graph.facebook.com/me"
    user_info_params = {
        "fields": "id,name,email",
        "access_token": token_data["access_token"],
    }
    response = requests.get(user_info_url, params=user_info_params)
    user_info = response.json()

    try:
        authenticate_user = crud.login.social_authenticate(
            db=db, username=user_info["email"]
        )
    except Exception as e:
        return RedirectResponse(
            url=f"{settings.PVGIS_UI_URL}/login?errorMessage={str(e)}"
        )

    token = jwt.encode(
        {"data": authenticate_user}, settings.SECRET_KEY, algorithm="HS256"
    )

    return RedirectResponse(url=f"{settings.PVGIS_UI_URL}?token={token}")
