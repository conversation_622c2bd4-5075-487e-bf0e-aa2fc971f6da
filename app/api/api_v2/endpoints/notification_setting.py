from ast import List
from typing import Any
from fastapi import APIRouter, Depends, HTTPException
from fastapi.encoders import jsonable_encoder
from requests import Session
from app.api.api_v2.deps import get_user
from app.config.db.database import get_session
from app.schemas.notification_setting import (
    NotificationSettingBase,
    NotificationSetting,
    NotificationSettingCreate,
    ResponseNotificationSetting,
    NotificationSettingUpdate,
)

from app.crud.crud_notification_setting import notification_setting
from app.crud.crud_features import features
from app.utils.utils import to_dict
from app import schemas
from app import crud
import ast

router = APIRouter()


@router.get("")
def read_notification_settings(
    db: Session = Depends(get_session),
    offset: int = 0,
    limit: int = 100,
    where: str = "",
    order_by: str = "id",
    order: str = "ASC",
    relations: str="[]",
    base_columns: str = "['created_at']"
) -> Any:
    wheres = []
    base_columns = ast.literal_eval(base_columns)
    relations = ast.literal_eval(relations)
    if where is not None and where != "" and where != []:
        wheres += ast.literal_eval(where)
    count = crud.notification_setting.get_count_where_array(
        db=db,
        where=wheres,
    )
    notification_setting_list = crud.notification_setting.get_multi_where_array_v2(
        db=db,
        where=wheres,
        order_by=order_by,
        order=order,
        limit=limit,
        skip=offset,
        relations=relations,
        base_columns=base_columns,
    )

    return {
        "count": count,
        "data": notification_setting_list,
    }


@router.post("", response_model=Any)
def create_notification_setting(
    *,
    db: Session = Depends(get_session),
    notification_setting_in: schemas.NotificationSettingCreate,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Create new notification_setting.
    """
    data = crud.notification_setting.create(db=db, obj_in=notification_setting_in)

    return data


@router.get("/{id}")
def read_notification_setting(
    *,
    db: Session = Depends(get_session),
    id: int,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Get notification_setting by ID.
    """
    data = crud.notification_setting.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="NotificationSetting not found")

    return data


@router.put("/{id}")
def update_notification_setting(
    *,
    db: Session = Depends(get_session),
    id: int,
    notification_setting_in: schemas.NotificationSettingUpdate,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Update an notification_setting.
    """
    data = crud.notification_setting.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="NotificationSetting not found")

    data = crud.notification_setting.update(
        db=db, db_obj=data, obj_in=notification_setting_in
    )

    return data


@router.delete("/{id}")
def delete_notification_setting(
    *,
    db: Session = Depends(get_session),
    id: int,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Delete an notification_setting.
    """

    data = crud.notification_setting.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="NotificationSetting not found")

    return crud.notification_setting.soft_delete(db=db, id=id)
