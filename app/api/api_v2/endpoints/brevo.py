import json
from fastapi import APIRouter, Request, Depends, HTTPException
from sqlalchemy.orm import Session
from app.services.brevo.brevo import (
    send_custom_email,
    send_email, get_contact, 
    update_attribute, 
    update_attribute_v2,
    reset_and_import_contacts, 
    import_contacts,
    get_contact_by_list,
    update_template,
    delete_template,
    get_senders,
    import_senders_from_list,
    create_template,
    send_email_with_sender,
    rules_email_from_brevo_track,
    dataSenders
)
from app.services.brevo.data import (
    get_data_user_mail, 
    generate_payload_to_brevo, 
    generate_payload_to_brevo_simple,
    get_data_email
)
from app.services.brevo.utils.brevo import (
    decode_x_mailin_custom,
    _first_row_with_id
)
from app.config.db.database import get_session
from app.services.customer_email.customer_email_service import (
    create_or_update_email_item_from_brevo,
    get_customer_from_webhook_brevo,
    update_customer_email_item_work_flow,
    handle_email_item_tracking,
    get_task_list_campaign,
    get_campaign_data,
)
from app.models.customer_email_item import CustomerEmailItem
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, Query
from app.config.settings import get_settings
from app.services.brevo.data_email import (
    get_all_langue_template,
    get_data_cms_campaign,
    get_ts_data_campaign,
    generate_the_template,
    regroupe_generate_template
)
from fastapi.responses import StreamingResponse, PlainTextResponse
from io import BytesIO
from typing import Optional, Dict

router = APIRouter()
settings = get_settings()


# IN OUR DB ABOUT CAMPAIGN
# Pro Mix : 8
# Spin 1 : 20
# Spin 2 : 28
# Spin 3 : 36
# Task handle in brevo : 0 to 12*

# DEV
# list_id_contact_in_brevo = 8

# PROD
# Pro Mix ---- 9
# Spin : Check a quote or an offer ---- 10
# Spin : Project ---- 11
# Spin : Control ---- 12
# Change the value for each campaign used
# list id contact in brevo : 9
# list_id_contact_in_brevo = 9

list_id_contact_in_brevo = 9
# SET THE HOUR TO SEND MAIL
heure_sent = 8

@router.get("/action_multiple")
def action_to_brevo():
    
    # senders LIST
    # result = get_senders()
    # result = import_senders_from_list(dataSenders)
        
    # ACTIVE AUTOMATION MULTIPLE
    # for index in range(14, 26):  
    #     try:
    #         activate_automation(index)
    #     except Exception as e:
    #         print(f"Failed to activate automation {index}: {e}")
        
    # ACTIVE AUTOMATION MULTIPLE
    
    # DELETE MULTIPLE
    # for template_id in range(6, 18):  
    #     try:
    #         delete_template(template_id)
    #     except Exception as e:
    #         print(f"Failed to delete template {template_id}: {e}")
    # delete_template(75)
    # DELETE MULTIPLE
    
    return {
        "status": "completed",
        # "data": result
    }


# INIT TEMPLATE FOR A CAMPAIGN
# SEE TABLE EMAIL CAMPAIGN
@router.get("/init-template")
def init_template_brevo(
    *,
    db: Session = Depends(get_session),
    campaign_id_from_PVGIS_db: int = 4,
    is_active: bool = False,
):
    try:
        if not campaign_id_from_PVGIS_db and campaign_id_from_PVGIS_db not in [4, 5, 6, 7]:
            raise HTTPException(status_code=400, detail="Invalid or empty campaign, must be one of [4, 5, 6, 7]")
        
        campaign_data = get_campaign_data(db, campaign_id_from_PVGIS_db)
        if not campaign_data:
            raise HTTPException(status_code=400, detail="Not found")
        
        list_task = get_task_list_campaign(db, campaign_id_from_PVGIS_db)
        
        if not list_task:
            raise HTTPException(status_code=400, detail="Data empty : list_task")
        
        for item in list_task:
            cms_key = item.cms_key
            name = f"{campaign_data.name} - {item.name}"
            template_html = regroupe_generate_template(cms_key)
            if not template_html:
                raise HTTPException(status_code=400, detail="Data empty : template_html")
            
            create_template(name, template_html, is_active)
            
        return {
            "campaign": campaign_id_from_PVGIS_db,
            "message": f"Template created for the campaign {campaign_data.name}"
        }
    except HTTPException:
        raise
    except Exception as exc:
        raise HTTPException(status_code=500, detail=f"Unexpected error: {type(exc).__name__}")

# USE @router.get("/init-template")
# CREATE TEMPLATE IN BREVO FOREACH TASK
# EMAIL FOR pvgis-email-pro-mixte-1-j-1
# .....
# DOESNT NEED IT ANYMORE
"""
# PRO MIX
'pvgis-email-pro-mixte-1-j-1'
'pvgis-email-pro-mixte-2-j-3'
'pvgis-email-pro-mixte-3-j-5'
'pvgis-email-pro-mixte-4-j-7'
'pvgis-email-pro-mixte-5-j-9'
'pvgis-email-pro-mixte-6-j-11'
'pvgis-email-pro-mixte-7-j-13'
'pvgis-email-pro-mixte-8-j-15'
'pvgis-email-pro-mixte-9-j-18'
'pvgis-email-pro-mixte-10-j-21'
'pvgis-email-pro-mixte-11-j-23'
'pvgis-email-pro-mixte-12-j-30'
# SPIN 1
'pvgis-email-campaign-day1-verify-quote'
'pvgis-email-campaign-day3-exaggerated-estimates'
'pvgis-email-campaign-day5-long-term-loss'
'pvgis-email-campaign-day7-reliable-analysis-9eur'
'pvgis-email-campaign-day14-compare-offers'
'pvgis-email-campaign-day16-profitability-check'
'pvgis-email-campaign-day18-poor-choice-cost'
'pvgis-email-campaign-day20-validate-project'
# SPIN 2 PROJECT
'pvgis-email-projet-spin-1-j-1'
'pvgis-email-projet-spin-2-j-3'
'pvgis-email-projet-spin-3-j-5'
'pvgis-email-projet-spin-4-j-7'
'pvgis-email-projet-spin-5-j-14'
'pvgis-email-projet-spin-6-j-16'
'pvgis-email-projet-spin-7-j-18'
'pvgis-email-projet-spin-8-j-20'
# SPIN 3 CONTROL
'pvgis-email-control-spin-1-j-1'
'pvgis-email-control-spin-2-j-3'
'pvgis-email-control-spin-3-j-5'
'pvgis-email-control-spin-4-j-7'
'pvgis-email-control-spin-5-j-14'
'pvgis-email-control-spin-6-j-16'
'pvgis-email-control-spin-7-j-18'
'pvgis-email-control-spin-8-j-20'
"""
@router.get("/template-for-brevo/{cms_key}")
def get_template_for_brevo(
    *,
    cms_key: str = "pvgis-email-pro-mixte-1-j-1",
    template_id: int = 120
):
    try:
        template = regroupe_generate_template(cms_key)
        # try:
        #     # WE CAN UPDATE DIRECTLY THE TEMPLATE IN BREVO, PARAMS THE TEMPLATE ID
        #     # EXAMPLE : 21
        #     update_template(template_id, html_content=template, is_active=True)
        # except Exception as exc:
        #     print(f"[WARNING] update_template failed: {type(exc).__name__}: {exc}")

        buffer = BytesIO()
        buffer.write(template.encode("utf-8"))
        buffer.seek(0)

        return StreamingResponse(
            buffer,
            media_type="text/plain; charset=utf-8",
            headers={
                "Content-Disposition": f'attachment; filename="template-{cms_key}.txt"'
            },
        )
    except HTTPException:
        raise
    except Exception as exc:
        raise HTTPException(status_code=500, detail=f"Unexpected error: {type(exc).__name__}")


# GET LIST DATA CUTSOMER TO SEND MAIL
# CAMPAIGN : 8 - 9 - 10 -12
# DEPENDS IN DB
# CHOOSE CAMPAIGN TO SEND
@router.get("/data-user-email-to-send-mail/{campaign}")
async def data_user_email(
    request: Request,
    db: Session = Depends(get_session),
    campaign: int = 0,
    to_email: Optional[str] = None,
    account_type: Optional[int] = None,
):
    try:
        if not campaign or campaign not in [8, 20, 28, 36]:
            raise HTTPException(status_code=400, detail="Invalid or empty campaign, must be one of [8, 20, 28, 36]")
        
        if account_type and account_type not in [1, 2, 3]:  
            raise HTTPException(status_code=400, detail="Invalid account type, must be one of [1, 2, 3]")
    
        try:
            data_user = get_data_user_mail(db, campaign, local_hour=heure_sent, to_email=to_email, account_type=account_type)
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Error fetching customer: {e}")

        if not data_user:
            return {"rows": [], "message": "No user data found for this campaign."}

        filtered_users = [user for user in data_user if user.email]  
        if not filtered_users:
            return {"rows": [], "message": "All users are missing email addresses."}

        try:
            rows = generate_payload_to_brevo(db, filtered_users, campaign)
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Error generating payload: {e}")

        return {"rows": rows, "total": len(rows)}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Unexpected error in get data user: {e}")

# SEND LIST DATA CUTSOMER TO SEND MAIL
# TO BREVO
# SEND 8 - 20 -28 - 36
# Pro Mix : 8
# Spin 1 : 20
# Spin 2 : 28
# Spin 3 : 36
# Task handle in brevo : 0 to 12
@router.post("/import-data-user-email-to-send-mail/{campaign_id}")
def import_data(
    *,
    db: Session = Depends(get_session),
    x_app_key: str,
    campaign_id: int,
    to_email: Optional[str] = None,
    account_type: Optional[int] = None,
):
    if x_app_key != settings.X_APP_KEY:
        return HTTPException(status_code=401)
    
    if account_type and account_type not in [1, 2, 3]:  
        raise HTTPException(status_code=400, detail="Invalid account type, must be one of [1, 2, 3]")

    if not campaign_id or campaign_id not in [8, 20, 28, 36]:
        raise HTTPException(status_code=400, detail="Invalid or empty campaign, must be one of [8, 20, 28, 36]")

    try:
        rows = get_data_user_mail(db, campaign_id, local_hour=heure_sent, to_email=to_email, account_type=account_type)
        if not rows:
            print("No contacts to import.")
            return
    except Exception as e:
        print("Error import data ", e)
    
    try:
        data = generate_payload_to_brevo(db, rows, campaign_id)
        if not data:
            print("Empty data")
            return
            
        reset_and_import_contacts(list_id_contact_in_brevo, data)
        return {
            'data': data
        }
    except Exception as e:
        print("Error import data ", e)    
    
# FOREACH WORKFLOW
# WE CALL AFTER AUTOMATION DONE
@router.post("/webhook-from-workflow/{current_task}")
async def brevo_webhook_workflow(
    request: Request,
    db: Session = Depends(get_session),
    current_task: int = 0,
):
    try:
        try:
            payload = await request.json()
        except Exception:
            body = await request.body()
            raise HTTPException(status_code=400, detail=f"Invalid JSON body: {body[:200]!r}")
        
        user_email = payload.get("email")
        try:
            user_brevo = get_contact_by_list(user_email, list_id_contact_in_brevo)
        except Exception:
            raise HTTPException(status_code=400, detail=f"Contact from brevo error : {body[:200]!r}")
        
        attributes = user_brevo.get("attributes", {})
        # # task = attributes.get('CAMPAIGN_EMAIL_NUMBER')
        campaign_id = attributes.get('CAMPAIGN_ID')
        workflow = payload.get('workflow_id')
        
        if not user_email:
            raise HTTPException(status_code=400, detail="Missing user email in webhook payload")
        
        try:
            customer = get_customer_from_webhook_brevo(db, None, user_email)
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Error fetching customer: {e}")
        
        if customer:
            try:
                task_condition = max(current_task - 1, 0)
                dataEmailItems = update_customer_email_item_work_flow(db, customer.id, campaign_id, task_condition, workflow)
            except Exception as e:
                raise HTTPException(status_code=500, detail=f"Error updating customer email workflow: {e}")
            

        try:
            # dataUpdate = update_attribute(user_email, current_task)
            # dataUpdate = update_attribute_v2(user_email, current_task, list_id_contact_in_brevo)
            # print(f"Brevo contact updated: {dataUpdate}")
            print("Done Process workflow")
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Error updating Brevo contact: {e}")

        return {"status": "success", "email": user_email, "task": current_task}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Unexpected error in webhook: {e}")
  
  
# WEBHOOK SEND MAIL FROM BREVO
@router.post("/webhook-send-mail")
async def brevo_webhook_send_mail(
    request: Request,
    db: Session = Depends(get_session)
):
    
    try:
        payload = await request.json()
    except Exception:
        body = await request.body()
        raise HTTPException(status_code=400, detail=f"Invalid JSON body: {body[:200]!r}")
    
    attrs = payload.get("attributes", {}) or {}
    
    if not attrs:
        raise HTTPException(status_code=400, detail="Missing attributes in payload")
    
    if not payload.get("email") or not attrs.get("COUNTRY") or not attrs.get("CAMPAIGN_ID"):
        raise HTTPException(status_code=400, detail="Missing required attributes in payload")
    
    to_email = payload.get("email")
    country_name = attrs.get("COUNTRY")
    campaign_id_brevo = attrs.get("CAMPAIGN_ID")
    task_campaign = attrs.get("CAMPAIGN_EMAIL_NUMBER", 0)
    
    data = get_data_email(db, to_email, country_name, campaign_id_brevo, task_campaign)
    if not data:
        raise HTTPException(status_code=400, detail="No data found for sending email")

    if not data.get("template_id") or not data.get("customer") or not data.get("sender"):
        raise HTTPException(status_code=400, detail="Missing required data for sending email")
    
    try:
        send_email_with_sender(
            template_id=data.get("template_id"),
            to_email=data.get("customer"),
            sender=data.get("sender"),
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error sending email: {e}")
    
    return {"status": "ok", "data": data}
    
    

# HANDLE ALL EMAIL SENT IN BREVO
# EACH ITEM CALL THIS ENDPOINT TO STORE DATA IN DB
@router.post("/webhook")
async def brevo_webhook(
    request: Request,
    db: Session = Depends(get_session)
):
    try:
        payload = await request.json()
    except Exception:
        body = await request.body()
        raise HTTPException(status_code=400, detail=f"Invalid JSON body: {body[:200]!r}")

    event_type = payload.get("event")
    if not event_type:
        raise HTTPException(status_code=400, detail="Missing 'event' in payload")
    
    user_email = payload.get("email")
    if not user_email:
        raise HTTPException(status_code=400, detail="Missing 'mail' in payload")
    
    try:
        contact_brevo = get_contact_by_list(user_email, list_id_contact_in_brevo)
    except Exception:
        raise HTTPException(status_code=400, detail=f"Contact from brevo error : {body[:200]!r}")
    
    try:
        customer = get_customer_from_webhook_brevo(db, None, user_email)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching customer: {e}")
    
    print("EVENT FROM BREVO : ", event_type) 
    
    rules_email = rules_email_from_brevo_track 

    handle_email_item_tracking(db, event_type, payload, contact_brevo, customer, rules_email)
    
    if event_type != "delivered":
        return {"status": "ignored", "reason": f"Event {event_type} ignored"}
   
    sender_email = payload.get("sender_email")
    user_email = payload.get("email")
    if not sender_email or not isinstance(sender_email, str) or not user_email or not isinstance(user_email, str):
        raise HTTPException(status_code=400, detail="Missing or invalid data in payload")

    
    contact_attributes = contact_brevo.get('attributes')

    sent_at_raw = payload.get("date")
    sent_at = None
    if sent_at_raw:
        try:
            sent_at = datetime.fromisoformat(sent_at_raw.replace("Z", "+00:00"))
        except Exception:
            raise HTTPException(status_code=400, detail="Invalid date format in 'date' field")

    x_mailin_custom = payload.get("X-Mailin-custom")
    metadata = decode_x_mailin_custom(x_mailin_custom) or {}

    customer = get_customer_from_webhook_brevo(db, None, user_email)

    email_item = CustomerEmailItem(
        customer_id=metadata.get("customerId") or customer.id,
        country_id=metadata.get("countryId") or customer.country_id,
        current_task=contact_attributes.get('CAMPAIGN_EMAIL_NUMBER'),
        campaign_number_brevo=contact_attributes.get('CAMPAIGN_ID'),
        customer_email_task_id=contact_attributes.get('CAMPAIGN_ID'),
        sender_email=sender_email,
        sent_at=sent_at,
        language=contact_attributes.get('LANGUAGE'),
        error=None,
        brevo=True,
        brevo_template=payload.get("template_id"),
    )
    
    try:
        db_obj = create_or_update_email_item_from_brevo(db, email_item)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
    return {"status": "ok"}


# FOR TEST
# UTILS FOR NEXT TIME

        # payload = {
    #     "appName": "workflow-action-processor",
    #     "attributes": {
    #         "CAMPAIGN_EMAIL_NUMBER": 0,
    #         "CAMPAIGN_ID": "20",
    #         "COUNTRY": "United States",
    #         "FULL_NAME": "Ric Man 3",
    #         "HOMEPAGE_URL": "http://localhost:8001/en",
    #         "HOME_PAGE_URL": "http://localhost:8001",
    #         "IS_SUBSCRIBED": False,
    #         "LANGUAGE": "en",
    #         "PVGIS24_EXTRANETURL": "http://localhost:8001/pvgis24/app/2/pvgis24",
    #         "PVGIS24_PREMIUM_SUBSCRIPTIONURL": "http://localhost:8001/subscribe/premium",
    #         "PVGIS24_SUBSCRIPTIONURL": "http://localhost:8001/en/subscription",
    #         "PVGIS24_URL": "http://localhost:8001/pvgis24/app/2/pvgis24",
    #         "PVGIS_OFFER_LINK": "http://localhost:8001/en/subscription?requires_login=1",
    #         "PVGIS_UI_URL": "http://localhost:8001/en",
    #         "SUBJECT": "Ready to take things to the next level?",
    #         "SUBSCRIBE_URL": "http://localhost:8001/en/subscription",
    #         "TESTOMONY_LINK": "http://localhost:8001/en/subscription",
    #         "TIMEZONE": "Pacific/Honolulu",
    #         "TOKEN": "ZXlKaGJHY2lPaUpJVXpJMU5pSXNJblI1Y0NJNklrcFhWQ0o5LmV5SjBiMnRsYmw5MGVYQmxJam9pYVc1MmFYUmhkR2x2YmlJc0ltTjFjM1J2YldWeUlqcDdJbWxrSWpveUxDSmhkWFJvWDNWelpYSmZhV1FpT2pRME5IMHNJbTV2WDJ4cGJXbDBJanAwY25WbExDSmxlSEFpT2pFM05qQXdPVFE0TVRSOS52Z193a3dkLU9BYVF1eEZ0TlBjdThVeTRaaUJwMl9BbjRPY01wX1dOSFhV",
    #         "UNSUBSCRIBEURL": "http://localhost:8001/customer/unsubscribe",
    #         "UNSUBSCRIBE_URL": "http://localhost:8001/unsubscribe",
    #         "USER_TYPE": "Particulier",
    #         "YEAR": "2025"
    #     },
    #     "contact_id": 1388,
    #     "email": "<EMAIL>",
    #     "step_id": 15,
    #     "workflow_id": 27
    # }
    # print("PAYLOAD FROM BREVO : ")
    # print(json.dumps(payload, indent=4))
    # print("----------------------------------------")

# @router.get("/send-custom-test")
# def send_custom_test_email(
#   *,
#   db: Session = Depends(get_session),
#   x_app_key: str,
# ):
#     if x_app_key != settings.X_APP_KEY:
#         return HTTPException(status_code=401)

#     template_vars = {
#         "firstname": "Eric",
#         "lastname": "Ratovonirina",
#         "year": "2025",
#         "homePageUrl": "https://pvgis.com",
#         "unsubscribeUrl": "https://pvgis.com/unsubscribe",
#         "pvgis24ExtranetUrl": "https://ratovonirina.onrender.com/"
#     }

#     metadata = {
#         "userId": 2,
#         "customerId": 2,
#         "countryId": 58,
#         "customerEmailTaskId": 2,
#         "language": "en"
#     }

#     html_body = f"""
#         <html>
#         <body style="font-family: Arial, sans-serif; line-height: 1.6;">
#             <h2>Hello {template_vars['firstname']} {template_vars['lastname']} 👋</h2>
#             <p>Welcome to PVGIS {template_vars['year']}!</p>
#             <p>Visit your <a href="{template_vars['homePageUrl']}">homepage</a>.</p>
#             <p>Check out the <a href="{template_vars['pvgis24ExtranetUrl']}">Extranet</a>.</p>
#             <p style="font-size:12px;color:gray;">
#             <a href="{template_vars['unsubscribeUrl']}">Unsubscribe</a>
#             </p>
#         </body>
#         </html>
#     """

#     response = send_custom_email(
#         to_email="<EMAIL>",
#         subject=f"Welcome {template_vars['firstname']}!",
#         html_content=html_body,
#         metadata=metadata
#     )

#     return {
#         "status": "sent",
#         "brevo_response": str(response),
#     }

# @router.get("/send-test")
# def send_test_email(
#     *,
#     db: Session = Depends(get_session),
#     x_app_key: str,
# ):
#     if x_app_key != settings.X_APP_KEY:
#         return HTTPException(status_code=401)
    
#     template_vars = {
#         "firstname": "Eric",
#         "lastname": "Ratovonirina",
#         "year": "2025",
#         "homePageUrl": "https://pvgis.com",
#         "unsubscribeUrl": "https://pvgis.com/unsubscribe",
#         "pvgis24ExtranetUrl": "https://ratovonirina.onrender.com/"
#     }

#     metadata = {
#         "userId": 2,
#         "customerId": 4,               
#         "countryId": 100,               
#         "customerEmailTaskId": 43,      
#         "language": "en"
#     }

#     response = send_email(
#         to_email="<EMAIL>",
#         template_id=2,
#         params=template_vars,
#         metadata=metadata
#     )

#     return {
#         "status": "sent",
#         "brevo_response": str(response),
#     }