import io
import pandas as pd
from typing import Any
from app.config.settings import get_settings
from app.config.db.database import get_session
from app.api.api_v2.deps import get_user
from app.models.account_information import AccountInformation
from app.models.account_type import AccountType
from app.models.country import Country
from app.models.customer import Customer
from app.models.customer_email_item import CustomerEmailItem
from app.models.subscription import Subscription
from sqlalchemy.sql import and_, literal, func
from sqlalchemy import func
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session, aliased
from fastapi.responses import StreamingResponse
from app.enums.subscription_status_enum import SubscriptionStatusEnum
from app.crud.pvgis_service import create_token
import jwt

router = APIRouter()

settings = get_settings()

@router.post("/registered_user")
def export_registered(
  *,
  db: Session = Depends(get_session),
  current_user: Any = Depends(get_user),
  x_app_key: str,
) -> Any:
  if x_app_key != settings.X_APP_KEY:
    return HTTPException(status_code=401)

  subscription_sub_query = (
      db.query(
          Subscription.customer_id.label("customer_id"),
          literal(True).label("is_subscribed_user")
      )
      .filter(
          and_(
              Subscription.subscription_status == "ACTIVE",
              Subscription.product_id != ********,
              Subscription.product_id.isnot(None),
          )
      )
      .subquery()
  )

  rows = (
    db.query(
      Customer.id,
      Customer.full_name,
      Customer.email,
      Customer.settings_json,
      Customer.auth_user_id,
      Country.name.label("country_name"),
      AccountType.name.label("user_type"),
      func.coalesce(subscription_sub_query.c.is_subscribed_user, False).label("is_subscribed_user"),
    )
    .outerjoin(subscription_sub_query, subscription_sub_query.c.customer_id == Customer.id)
    .outerjoin(AccountInformation, AccountInformation.customer_id == Customer.id)
    .outerjoin(AccountType, AccountType.id == AccountInformation.account_type_id)
    .outerjoin(Country, Country.id == Customer.country_id)
    .filter(
        Customer.auth_user_id.isnot(None),
        Customer.deleted_at.is_(None),
    )
    .group_by(
      Customer.id,
    )
    .all()
  )

  data = []
  for r in rows:
    language = None
    if r.settings_json and isinstance(r.settings_json, dict):
      language = r.settings_json.get("language") or r.settings_json.get("lang")
    language = language or "en"

    token_data = {
      "token_type": "invitation",
      "customer": {
          "id": r.id,
          "auth_user_id": r.auth_user_id,
      }, 
      "no_limit": True,
    }
    
    data.append({
        "Email Address": r.email,
        "Full Name": r.full_name or "",
        "Language": language,
        "Country": r.country_name or "",
        "Sign-In Token": create_token(token_data),
        "User Type": r.user_type or "",
        "Is Subscribed User": r.is_subscribed_user,
    })

  # Export to Excel
  df = pd.DataFrame(data, columns=[
    "Email Address",
    "Full Name",
    "Language",
    "Country",
    "Sign-In Token",
    "User Type",
    "Is Subscribed User",
  ])
  
  output = io.BytesIO()
  with pd.ExcelWriter(output, engine="openpyxl") as writer:
      df.to_excel(writer, index=False, sheet_name="Registered Users")
  output.seek(0)

  headers = {
      "Content-Disposition": 'attachment; filename="registered_users.xlsx"'
  }
  return StreamingResponse(
      output,
      media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      headers=headers,
  )