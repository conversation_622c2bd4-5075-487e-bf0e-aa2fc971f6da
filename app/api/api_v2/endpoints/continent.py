from typing import Any
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File
from sqlalchemy.orm import Session
import json

from app.api.api_v2.deps import get_user
from app.config.db.database import get_session
from app.schemas.continent import ContinentCreate, ContinentUpdate

from app.crud.crud_continent import continent
from app import schemas
from app import crud
from fastapi.encoders import jsonable_encoder

router = APIRouter()

import ast

@router.get("")
def read_continent(
    db: Session = Depends(get_session),
    offset: int = 0,
    limit: int = 100,
    where: str = "",
    order_by: str = "id",
    order: str = "ASC",
    relations: str="[]",
    base_columns: str = "['created_at']"
) -> Any:
    wheres = []
    base_columns = ast.literal_eval(base_columns)
    relations = ast.literal_eval(relations)
    if where is not None and where != "" and where != []:
        wheres += ast.literal_eval(where)
    count = crud.continent.get_count_where_array(
        db=db,
        where=wheres,
    )
    continent_list = crud.continent.get_multi_where_array_v2(
        db=db,
        where=wheres,
        order_by=order_by,
        order=order,
        limit=limit,
        skip=offset,
        relations=relations,
        base_columns=base_columns,
    )

    return {
        "count": count,
        "data": continent_list,
    }


@router.post("", response_model=Any)
def create_continent(
    *,
    db: Session = Depends(get_session),
    continent_in: schemas.ContinentCreate,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Create new crud.continent.
    """
    data = crud.continent.create(db=db, obj_in=continent_in)

    return data

@router.post("/import-json", response_model=Any)
def upload_json_continents(
    *,
    db: Session = Depends(get_session),
    file: UploadFile = File(...),
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Upload a JSON file and create continents in the database while filling the corresponding countries.
    """
    try:
        content = file.file.read()
        continents_data = json.loads(content)
        
        if not isinstance(continents_data, list):
            raise HTTPException(status_code=400, detail="invalid_json_file")
        
        country_iso_codes = []
        continent_names = []
        # --------------------- Create continents only ---------------------
        created_continents = []
        for country in continents_data:
            if(country["cca2"]):
                country_iso_codes.append(country["cca2"])
            if(country["continents"]):
                continent_names.append(country["continents"][0])
                continent_in = {
                    "name": country["continents"][0],
                }
                continent_exists = crud.continent.get_first_where_array_v2(db=db, where=[{"key":"name", "operator":"==", "value": continent_in['name']}])
                if continent_exists is None:
                    continent_schema = schemas.CountryCreate(**continent_in)
                    if continent_schema not in created_continents:
                        created_continents.append(continent_schema)

        created_continent = crud.continent.create_multi(db=db, objs_in=created_continents)
        
        # --------------------- Manage countries ---------------------
        countries = crud.country.get_multi_where_array_v2(db=db, where=[{"key":"code_alpha_2", "operator":"in", "value": country_iso_codes}], base_columns=['code_alpha_2'], no_limit=True)
        continents = crud.continent.get_multi_where_array_v2(db=db, where=[{"key":"name", "operator":"in", "value": continent_names}], base_columns=['name'], no_limit=True)

        # Create mappings for easy lookup
        continent_map = {continent.name: continent.id for continent in continents}
        country_map = {country.code_alpha_2: country.id for country in countries}
        
        country_updates = []
        for item in continents_data:
            if(item["cca2"]):
                country_code = item['cca2']
                continent_name = item["continents"][0]
                country_id = country_map.get(country_code)
                continent_id = continent_map.get(continent_name)
                
                if country_id and continent_id:
                    country_updates.append(
                        {
                            'id': country_id,
                            'continent_id': continent_id
                        }
                    )
        # Perform the bulk update
        if country_updates:
            crud.country.bulk_update_continent_country(db=db, country_updates=country_updates)
        return {"created_continent":created_continent, "country_updates":country_updates}

    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="invalid_json_file")
    except Exception as e:
        raise
        # raise HTTPException(status_code=500, detail=f"An error occurred: {str(e)}")


@router.get("/{id}")
def read_continent(
    *,
    db: Session = Depends(get_session),
    id: int,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Get continent by ID.
    """
    data = crud.continent.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="continent_not_found")

    return data


@router.put("/{id}")
def update_continent(
    *,
    db: Session = Depends(get_session),
    id: int,
    continent_in: schemas.ContinentUpdate,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Update an crud.continent.
    """
    data = crud.continent.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="continent_not_found")

    data = crud.continent.update(db=db, db_obj=data, obj_in=continent_in)

    return data


@router.delete("/{id}", response_model=Any)
def delete_continent(
    *,
    db: Session = Depends(get_session),
    id: int,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Delete an crud.continent.
    """

    data = crud.continent.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="continent_not_found")

    return crud.continent.soft_delete(db=db, id=id)
