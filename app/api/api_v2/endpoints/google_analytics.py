import ast
from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from app.config.db.database import get_session

from app.crud import GA4Report
import os
from datetime import datetime, timedelta
from app.config.settings import get_settings

settings = get_settings()

router = APIRouter()
PROPERTY_ID = settings.GOOGLE_ANALYTICS_PROPERTY_ID 
os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = settings.GOOGLE_ANALYTICS_CREDENTIALS_URI 

@router.get("/reports")
def get_reports(
    dimensions: str = "[]",
    metrics: str= "['activeUsers']",
    limit: int = 10, 
    days: int = 0,
    start_date: datetime = None, 
    end_date: datetime = None, 
):
    dimensions = ast.literal_eval(dimensions)
    metrics = ast.literal_eval(metrics)
    if(days > 0 and start_date is None and end_date is None): 
        end_date = datetime.today()
        start_date = end_date - timedelta(days=days)
        
    ga4_report = GA4Report(PROPERTY_ID)
    response=ga4_report.query_report(
        start_date=start_date,
        end_date=end_date,
        metrics=metrics,
        dimensions=dimensions,
        limit=limit   
    )
    return response

@router.get("/active_users")
def get_active_users(
    db: Session = Depends(get_session),
):
    ga4_report = GA4Report(PROPERTY_ID)
    return ga4_report.get_active_users(db=db)
