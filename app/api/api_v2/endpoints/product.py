import ast
from typing import Any, Optional
from fastapi import APIRouter, Depends, HTTPException
from fastapi.encoders import jsonable_encoder
from sqlalchemy.orm import Session

from app.api.api_v2.deps import get_optional_customer, get_user
from app.config.db.database import get_session
from app.schemas.product import (
    ProductBase,
    Product,
    ProductCreate,
    ResponseProduct,
    ProductUpdate,
)

from app.crud.crud_product import product
from app.crud.crud_features import features
from app.utils.utils import to_dict
from app import crud
from app import schemas

router = APIRouter()


@router.get("")
def read_products(
    db: Session = Depends(get_session),
    offset: int = 0,
    limit: int = 100,
    where: str = "",
    order_by: str = "id",
    order: str = "ASC",
    relations: str="['product_features', 'product_features.features']",
    base_columns: str = "['created_at']",
    current_customer:Optional[Any] =Depends(get_optional_customer)
) -> Any:
    
    wheres = []
    base_columns = ast.literal_eval(base_columns)
    relations = ast.literal_eval(relations)
    if where is not None and where != "" and where != []:
        wheres += ast.literal_eval(where)
    count = crud.product.get_count_where_array(
        db=db,
        where=wheres,
    )
    product_list = crud.product.get_multi_where_array_v2(
        db=db,
        where=wheres,
        order_by=order_by,
        order=order,
        limit=limit,
        skip=offset,
        relations=relations,
        base_columns=base_columns,
    )
    subscriptionList=[]
    if(current_customer is not None):
        wheres = [
            {
                "key" : "customer_id",
                "operator": "==",
                "value" : current_customer.id
            },
            {
                "key" : "subscription_status",
                "operator" : "==",
                "value" : "active"
            },
            {
                "key":"product_json",
                "operator":"isNotNull"
            }
        ]
        relations = []
        base_columns = ["created_at", "product_json", "start_date", "is_auto_renew", "expired_date"]
        subscriptionList=crud.subscription.get_multi_where_array_v2(
            db=db,
            where=wheres,
            relations=relations,
            base_columns=base_columns,
        )
    for product in product_list:
        product.validate(subscriptionList)
    
    

    return {
        "count": count,
        "data": product_list,
    }


@router.post("", response_model=Any)
def create_product(
    *,
    db: Session = Depends(get_session),
    product_in: schemas.ProductCreate,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Create new product.
    """
    data = product.create(db=db, obj_in=product_in)

    return data


@router.get("/{id}")
def read_product(
    *,
    db: Session = Depends(get_session),
    id: int,
) -> Any:
    """
    Get product by ID.
    """
    data = product.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="Product not found")

    return data


@router.put("/{id}", response_model=ProductUpdate)
def update_product(
    *,
    db: Session = Depends(get_session),
    id: int,
    product_in: schemas.ProductUpdate,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Update an product.
    """
    data = product.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="Issue not found")

    data = product.update(db=db, db_obj=data, obj_in=product_in)

    return to_dict(data)


@router.delete("/{id}", response_model=Any)
def delete_product(
    *,
    db: Session = Depends(get_session),
    id: int,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Delete an product.
    """

    data = product.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="Product not found")

    return to_dict(product.soft_delete(db=db, id=id))
