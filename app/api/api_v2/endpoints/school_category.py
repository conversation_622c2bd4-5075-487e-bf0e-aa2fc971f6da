from typing import Any
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.api.api_v2.deps import get_user
from app.config.db.database import get_session
from app.schemas.school_category import SchoolCategoryCreate, SchoolCategoryUpdate

from app.crud.crud_school_category import school_category
from app import schemas
from app import crud

router = APIRouter()

import ast

@router.get("")
def read_school_categorys(
    db: Session = Depends(get_session),
    offset: int = 0,
    limit: int = 100,
    where: str = "",
    order_by: str = "default_order",
    order: str = "ASC",
    relations: str="[]",
    base_columns: str = "['created_at']"
) -> Any:
    wheres = []
    base_columns = ast.literal_eval(base_columns)
    relations = ast.literal_eval(relations)
    if where is not None and where != "" and where != []:
        wheres += ast.literal_eval(where)
    count = crud.school_category.get_count_where_array(
        db=db,
        where=wheres,
    )
    school_category_list = crud.school_category.get_multi_where_array_v2(
        db=db,
        where=wheres,
        order_by=order_by,
        order=order,
        limit=limit,
        skip=offset,
        relations=relations,
        base_columns=base_columns,
    )

    return {
        "count": count,
        "data": school_category_list,
    }


@router.post("", response_model=Any)
def create_school_category(
    *,
    db: Session = Depends(get_session),
    school_category_in: schemas.SchoolCategoryCreate,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Create new crud.school_category.
    """
    data = crud.school_category.create(db=db, obj_in=school_category_in)

    return data


@router.get("/{id}")
def read_school_category(
    *,
    db: Session = Depends(get_session),
    id: int,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Get school_category by ID.
    """
    data = crud.school_category.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="SchoolCategory not found")

    return data


@router.put("/{id}")
def update_school_category(
    *,
    db: Session = Depends(get_session),
    id: int,
    school_category_in: schemas.SchoolCategoryUpdate,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Update an crud.school_category.
    """
    data = crud.school_category.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="SchoolCategory not found")

    data = crud.school_category.update(db=db, db_obj=data, obj_in=school_category_in)

    return data


@router.delete("/{id}", response_model=Any)
def delete_school_category(
    *,
    db: Session = Depends(get_session),
    id: int,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Delete an crud.school_category.
    """

    data = crud.school_category.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="SchoolCategory not found")

    return crud.school_category.soft_delete(db=db, id=id)
