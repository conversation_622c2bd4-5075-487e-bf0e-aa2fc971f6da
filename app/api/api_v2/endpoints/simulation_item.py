import ast
from typing import Any

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app import crud, schemas
from app.api.api_v2.deps import get_user
from app.config.db.database import get_session
from app.schemas.simulation_item import SimulationItemUpdate
from app.utils.utils import to_dict
from datetime import datetime

from fastapi.responses import HTMLResponse, FileResponse, StreamingResponse

router = APIRouter()


@router.get("")
def read_simulation_items(
        db: Session = Depends(get_session),
        offset: int = 0,
        limit: int = 100,
        where: str = "",
        order_by: str = "id",
        order: str = "ASC",
        relations: str = "[]",
        base_columns: str = "['created_at']",
) -> Any:
    wheres = []
    base_columns = ast.literal_eval(base_columns)
    relations = ast.literal_eval(relations)
    if where is not None and where != "" and where != []:
        wheres += ast.literal_eval(where)
    count = crud.simulation_item.get_count_where_array(
        db=db,
        where=wheres,
    )
    simulation_item_list = crud.simulation_item.get_multi_where_array_v2(
        db=db,
        where=wheres,
        order_by=order_by,
        order=order,
        limit=limit,
        skip=offset,
        relations=relations,
        base_columns=base_columns,
    )

    return {
        "count": count,
        "data": simulation_item_list,
    }


@router.post("")
def create_simulation_item(
        *,
        db: Session = Depends(get_session),
        simulation_item_in: schemas.SimulationItemCreate,
        current_user: Any = Depends(get_user),
) -> Any:
    """
    Create new crud.simulation_item.
    """
    simulation_item_in.created_by = current_user["id"]

    crud.simulation_item.get_simulation_by_id(db=db, id=simulation_item_in.simulation_id)
    simulation_item_in.updated_at = datetime.now()
    return crud.simulation_item.create(
        db=db, obj_in=simulation_item_in, user_id=current_user["id"]
    )


@router.get("/{id}")
def read_simulation_item(
        *,
        db: Session = Depends(get_session),
        id: int,
        current_user: Any = Depends(get_user),
) -> Any:
    """
    Get simulation_item by ID.
    """
    data = crud.simulation_item.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="Product not found")

    return data


@router.get("/simulation/{simulation_id}")
def read_simulation_item_by_simulation(
        db: Session = Depends(get_session),
        simulation_id: int = 1,
        current_user: Any = Depends(get_user)
) -> Any:
 

    return crud.simulation_item.get_multi_where_array(db=db, where=[
        {"key": "simulation_id", "operator": "==", "value": simulation_id}])


@router.get("/get_excel_file/by_simulation_type/", response_class=FileResponse)
def read_all_simulation_by_simulation_type(
        db: Session = Depends(get_session),
        simulation_type: str = "all",
) -> Any:
    output_bytes = crud.simulation_item.write_simulation_data_to_excel(
        db=db, simulation_type=simulation_type) 
    headers = {
        "Content-Disposition": f"attachment; filename=simulation_{simulation_type}.xlsx"
    }
    return StreamingResponse(output_bytes, media_type="application/xlsx", headers=headers)


@router.put("/{id}", response_model=SimulationItemUpdate)
def update_simulation_item(
        *,
        db: Session = Depends(get_session),
        id: int,
        simulation_item_in: schemas.SimulationItemUpdate,
        current_user: Any = Depends(get_user),
) -> Any:
    """
    Update an crud.simulation_item.
    """
    data = crud.simulation_item.get(db=db, id=id)
    if not data:
        raise HTTPException(status_code=404, detail="Issue not found")

    data = crud.simulation_item.update(db=db, db_obj=data, obj_in=simulation_item_in, user_id=current_user["id"])

    return to_dict(data)


@router.delete("/{id}", response_model=Any)
def delete_simulation_item(
        *,
        db: Session = Depends(get_session),
        id: int,
        current_user: Any = Depends(get_user),
) -> Any:
    """
    Delete an crud.simulation_item.
    """

    data = crud.simulation_item.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="Product not found")

    return to_dict(crud.simulation_item.soft_delete(db=db, id=id))
