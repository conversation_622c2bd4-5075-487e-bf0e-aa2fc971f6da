from typing import Any
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.api.api_v2.deps import get_user
from app.config.db.database import get_session
from app.schemas.account_type import AccountTypeCreate, AccountTypeUpdate

from app.crud.crud_account_type import account_type
from app import schemas
from app import crud

router = APIRouter()


import ast

@router.get("")
def read_account_type(
    db: Session = Depends(get_session),
    offset: int = 0,
    limit: int = 100,
    where: str = "",
    order_by: str = "id",
    order: str = "ASC",
    relations: str="[]",
    base_columns: str = "['created_at']"
) -> Any:
    wheres = []
    base_columns = ast.literal_eval(base_columns)
    relations = ast.literal_eval(relations)
    if where is not None and where != "" and where != []:
        wheres += ast.literal_eval(where)
    count = crud.account_type.get_count_where_array(
        db=db,
        where=wheres,
    )
    account_type_list = crud.account_type.get_multi_where_array_v2(
        db=db,
        where=wheres,
        order_by=order_by,
        order=order,
        limit=limit,
        skip=offset,
        relations=relations,
        base_columns=base_columns,
    )

    return {
        "count": count,
        "data": account_type_list,
    }


@router.post("", response_model=Any)
def create_account_type(
    *,
    db: Session = Depends(get_session),
    account_type_in: schemas.AccountTypeCreate,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Create new account_type.
    """
    data = crud.account_type.create(db=db, obj_in=account_type_in)

    return data


@router.get("/{id}")
def read_account_type(
    *,
    db: Session = Depends(get_session),
    id: int,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Get account_type by ID.
    """
    data = crud.account_type.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="AccountType not found")

    return data


@router.put("/{id}")
def update_account_type(
    *,
    db: Session = Depends(get_session),
    id: int,
    account_type_in: schemas.AccountTypeUpdate,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Update an account_type.
    """
    data = crud.account_type.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="AccountType not found")

    data = crud.account_type.update(db=db, db_obj=data, obj_in=account_type_in)

    return data


@router.delete("/{id}", response_model=Any)
def delete_account_type(
    *,
    db: Session = Depends(get_session),
    id: int,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Delete an account_type.
    """

    data = crud.account_type.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="AccountType not found")

    return crud.account_type.soft_delete(db=db, id=id)
