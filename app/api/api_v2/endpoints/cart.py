from typing import Any
from fastapi import APIRouter, Depends, HTTPException
from fastapi.encoders import jsonable_encoder
from sqlalchemy.orm import Session
from typing import Optional
from app.api.api_v2.deps import get_user
from app.api.api_v2.deps import get_optional_customer
from app.config.db.database import get_session
from app.schemas.cart import AddToCart,DeleteCart

from app.crud.crud_cart import cart
from app.crud.crud_product import product

from app.utils.utils import to_dict
from datetime import datetime

import random
import string

from app import schemas
from app import crud

router = APIRouter()

import ast

@router.get("")
def read_carts(
    db: Session = Depends(get_session),
    offset: int = 0,
    limit: int = 100,
    where: str = "",
    order_by: str = "id",
    order: str = "ASC",
    relations: str="[]",
    base_columns: str = "['created_at']"
) -> Any:
    wheres = []
    base_columns = ast.literal_eval(base_columns)
    relations = ast.literal_eval(relations)
    if where is not None and where != "" and where != []:
        wheres += ast.literal_eval(where)

    wheres.append({"key":"product.allow_new_subscribers","operator":"==","value":True})
    count = crud.cart.get_count_where_array(
        db=db,
        where=wheres,
    )
    cart_list = crud.cart.get_multi_where_array_v2(
        db=db,
        where=wheres,
        order_by=order_by,
        order=order,
        limit=limit,
        skip=offset,
        relations=relations,
        base_columns=base_columns,
    )

    return {
        "count": count,
        "data": cart_list,
    }

@router.post("")
def add_to_cart(
    *,
    db: Session = Depends(get_session),
    cart_in: schemas.AddToCart,
) -> Any:
    """
    Create new cart.
    """
     
   
    product_is_allowed_subscription=bool(crud.product.get_allowed_subscription_product_by_id(db=db,product_id=cart_in.product_id))
    if not product_is_allowed_subscription:
        raise HTTPException(status_code=404, detail="Product not allowed to subscribe")

    cart_ob = None
    cart_data = crud.cart.get_first_where_array(
        db=db,
        where=[
            {
                "key": "cart_reference",
                "operator": "==",
                "value": cart_in.cart_reference,
            },
            {"key": "converted_at", "operator": "isNull"},
        ],
    )
    if not cart_data:
        characters = (
            string.ascii_letters + string.digits
        )  # Uppercase, lowercase, and digits
        cart_reference = "".join(random.choice(characters) for _ in range(20))
        cart_ob = crud.cart.create(
            db=db,
            obj_in={
                "cart_reference": cart_reference,
                "product_id": cart_in.product_id, 
                "quantity": 1,
                "country_id": cart_in.country_id,
                "ip_address": cart_in.ip_address,
                "language_iso_2": cart_in.language_iso_2,
            },
        )
        
    else:
        cart_ob = crud.cart.update(
            db=db,
            db_obj=cart_data,
            obj_in={
                "product_id": cart_in.product_id,  
                "updated_at": datetime.now(),
                "country_id": cart_in.country_id,
                "ip_address": cart_in.ip_address,
                "language_iso_2": cart_in.language_iso_2,
                },
        )
    
    if(cart_in.customer_id):
        customer_ob=crud.customer.get(db=db,id=cart_in.customer_id)
        crud.customer.update(db=db,db_obj=customer_ob,obj_in={"cart_reference":cart_in.cart_reference})
    return to_dict(cart_ob)


@router.get("/by_reference/{cart_reference}")
def read_cart_by_reference(
    *,
    db: Session = Depends(get_session),
    cart_reference: str,
    current_customer: Optional[Any] = Depends(get_optional_customer)
    
) -> Any:
    """
    Get cart by reference.
    """
    data = crud.cart.get_by_ref_for_checkout(db, cart_reference)

    if not data:
        raise HTTPException(status_code=404, detail="Product not found")

    product_is_allowed_subscription=data.product.allow_new_subscribers
    if not product_is_allowed_subscription:
        customer_id = current_customer.id if current_customer else None
        cart_in = DeleteCart(customer_id=customer_id)
        crud.cart.remove_cart_by_reference(db=db, cart_reference=cart_reference, cart_in=cart_in)
        raise HTTPException(status_code=404, detail="Removed product because not allowed to subscribe anymore")

    subscriptionList=[]
    if(current_customer):
        wheres = [
            {
                "key" : "customer_id",
                "operator": "==",
                "value" : current_customer.id
            },
            {
                "key" : "subscription_status",
                "operator" : "==",
                "value" : "active"
            },
            {
                "key":"product_json",
                "operator":"isNotNull"
            }
        ]
        relations = []
        base_columns = ["created_at", "product_json", "start_date", "is_auto_renew", "expired_date"]
        subscriptionList=crud.subscription.get_multi_where_array_v2(
            db=db,
            where=wheres,
            relations=relations,
            base_columns=base_columns,
        )
     

    data.validateCart(db,subscriptionList)

    return data


@router.delete("/{cart_reference}", response_model=Any)
def update_customer(
    *,
    db: Session = Depends(get_session),
    cart_reference: str,
    cart_in:DeleteCart
) -> Any:
    """
    Update an user.
    """
    crud.cart.remove_cart_by_reference(db=db,cart_reference=cart_reference,cart_in=cart_in)
    return {
        "status_code": 200,
        "message": f"cart {cart_reference} removed successfully",
    }
