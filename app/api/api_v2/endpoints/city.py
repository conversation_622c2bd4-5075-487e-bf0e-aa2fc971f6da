from typing import Any
from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import JSONResponse
from app.config.settings import get_settings
from app.services.city_migration_service import  bulk_update_all_horizon_data, fetch_timezone_offset, integrate_cities_by_chunk, update_country_default_customer_data, update_country_timezone_offset, update_customer_timezone
from sqlalchemy.orm import Session

from app.api.api_v2.deps import get_user
from app.config.db.database import get_session
from app.schemas.city import CityCreate, CityMigrate, CityUpdate

from app.crud.crud_city import city
from app import schemas
from app import crud
settings = get_settings()
router = APIRouter()

import ast

@router.get("")
def read_city(
    db: Session = Depends(get_session),
    offset: int = 0,
    limit: int = 100,
    where: str = "",
    order_by: str = "id",
    order: str = "ASC",
    relations: str="[]",
    base_columns: str = "['created_at']"
) -> Any:
    wheres = []
    base_columns = ast.literal_eval(base_columns)
    relations = ast.literal_eval(relations)
    if where is not None and where != "" and where != []:
        wheres += ast.literal_eval(where)
    count = crud.city.get_count_where_array(
        db=db,
        where=wheres,
    )
    city_list = crud.city.get_multi_where_array_v2(
        db=db,
        where=wheres,
        order_by=order_by,
        order=order,
        limit=limit,
        skip=offset,
        relations=relations,
        base_columns=base_columns,
    )

    return {
        "count": count,
        "data": city_list,
    }
@router.get("/ip/{ip}")
def get_one_by_ip( 
    db: Session = Depends(get_session),
    ip: str = None, 
) -> Any:
    """
    Get city by IP.
    """
    data = crud.city.get_one_by_ip(db, ip)

    if not data:
        raise HTTPException(status_code=404, detail="city_not_found")

    return data

@router.post("/migrate-cities")
def read_city(
    *,
    db: Session = Depends(get_session),  
    city_migrate: CityMigrate,
    x_app_key:str
) -> Any:
    """
    Migrate cities into the database
    """
    if x_app_key != settings.X_APP_KEY:
        return HTTPException(status_code=401)
    integrate_cities_by_chunk(city_migrate)
    
     
    return {"message": "Chunk migrated successfully"}

@router.post("/migrate-horizon-data")
def migrate_horizon_data(
    *,
    db: Session = Depends(get_session),  
    city_migrate: CityMigrate,
    current_user: Any = Depends(get_user),
) -> Any:
    bulk_update_all_horizon_data(city_migrate.chunk_size)
    return {"message": "Chunk migrated successfully"}

@router.post("/migrate-country-timezone-offset")
def migrate_country_timezone_offset(
    *,
    db: Session = Depends(get_session),  
    current_user: Any = Depends(get_user),
) -> Any:
    update_country_timezone_offset()
    return {"message": "Chunk migrated successfully"}

@router.post("/migrate-country-default-customer-data")
def migrate_country_timezone_offset(
    *,
    db: Session = Depends(get_session),  
    current_user: Any = Depends(get_user),
) -> Any:
    results = update_country_default_customer_data(db) 
   
    return {"message": "Sender and default customer per country updated"}

@router.post("/migrate-customer-timezone")
def migrate_country_timezone_offset(
    *,
    db: Session = Depends(get_session),  
    current_user: Any = Depends(get_user),
) -> Any:
    update_customer_timezone() 
   
    return {"message": "Customer timezone successfully updated", "example":fetch_timezone_offset('Antananarivo', "Madagascar")}


@router.post("", response_model=Any)
def create_city(
    *,
    db: Session = Depends(get_session),
    city_in: schemas.CityCreate,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Create new crud.city.
    """
    data = crud.city.create(db=db, obj_in=city_in)

    return data


@router.get("/{id}")
def read_city(
    *,
    db: Session = Depends(get_session),
    id: int,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Get city by ID.
    """
    data = crud.city.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="city_not_found")

    return data


@router.put("/{id}")
def update_city(
    *,
    db: Session = Depends(get_session),
    id: int,
    city_in: schemas.CityUpdate,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Update an crud.city.
    """
    data = crud.city.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="city_not_found")

    data = crud.city.update(db=db, db_obj=data, obj_in=city_in)

    return data


@router.delete("/{id}", response_model=Any)
def delete_city(
    *,
    db: Session = Depends(get_session),
    id: int,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Delete an crud.city.
    """

    data = crud.city.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="city_not_found")

    return crud.city.soft_delete(db=db, id=id)
