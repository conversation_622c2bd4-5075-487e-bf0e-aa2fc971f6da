from typing import Any
from fastapi import <PERSON>Router, Depends, HTTPException
from sqlalchemy.orm import Session

from app.api.api_v2.deps import get_user
from app.config.db.database import get_session
from app.schemas.customer_contact_platform_information import CustomerContactPlatformInformationCreate, CustomerContactPlatformInformationUpdate

from app.crud.crud_customer_contact_platform_information import customer_contact_platform_information
from app import schemas
from app import crud

router = APIRouter()


import ast

@router.get("")
def read_customer_contact_platform_information(
    db: Session = Depends(get_session),
    offset: int = 0,
    limit: int = 100,
    where: str = "",
    order_by: str = "id",
    order: str = "ASC",
    relations: str="[]",
    base_columns: str = "['created_at']"
) -> Any:
    wheres = []
    base_columns = ast.literal_eval(base_columns)
    relations = ast.literal_eval(relations)
    if where is not None and where != "" and where != []:
        wheres += ast.literal_eval(where)
    count = crud.customer_contact_platform_information.get_count_where_array(
        db=db,
        where=wheres,
    )
    customer_contact_platform_information_list = crud.customer_contact_platform_information.get_multi_where_array_v2(
        db=db,
        where=wheres,
        order_by=order_by,
        order=order,
        limit=limit,
        skip=offset,
        relations=relations,
        base_columns=base_columns,
    )

    return {
        "count": count,
        "data": customer_contact_platform_information_list,
    }


@router.post("", response_model=Any)
def create_customer_contact_platform_information(
    *,
    db: Session = Depends(get_session),
    contact_platform_in: schemas.ContactPlatformCreate,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Create new customer_contact_platform_information.
    """
    data = crud.customer_contact_platform_information.create(db=db, obj_in=contact_platform_in)

    return data


@router.get("/{id}")
def read_customer_contact_platform_information(
    *,
    db: Session = Depends(get_session),
    id: int,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Get customer_contact_platform_information by ID.
    """
    data = crud.customer_contact_platform_information.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="ContactPlatform not found")

    return data


@router.put("/{id}")
def update_customer_contact_platform_information(
    *,
    db: Session = Depends(get_session),
    id: int,
    contact_platform_in: schemas.ContactPlatformUpdate,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Update an customer_contact_platform_information.
    """
    data = crud.customer_contact_platform_information.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="ContactPlatform not found")

    data = crud.customer_contact_platform_information.update(db=db, db_obj=data, obj_in=contact_platform_in)

    return data


@router.delete("/{id}", response_model=Any)
def delete_customer_contact_platform_information(
    *,
    db: Session = Depends(get_session),
    id: int,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Delete an customer_contact_platform_information.
    """

    data = crud.customer_contact_platform_information.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="ContactPlatform not found")

    return crud.customer_contact_platform_information.soft_delete(db=db, id=id)
