from typing import Any
from fastapi import APIRouter, Depends, HTTPException
from fastapi.encoders import jsonable_encoder
from sqlalchemy.orm import Session

from app.api.api_v2.deps import get_user
from app.config.db.database import get_session
from app.schemas.payment_method import PaymentMethodUpdate, PaymentMethodCreate

from app.crud.crud_payment_method import payment_method
from app.utils.utils import to_dict

from app import schemas
from app import crud

router = APIRouter()

import ast

@router.get("")
def read_payment_methods(
    db: Session = Depends(get_session),
    offset: int = 0,
    limit: int = 100,
    where: str = "",
    order_by: str = "id",
    order: str = "ASC",
    relations: str="[]",
    base_columns: str = "['created_at']"
) -> Any:
    wheres = []
    base_columns = ast.literal_eval(base_columns)
    relations = ast.literal_eval(relations)
    if where is not None and where != "" and where != []:
        wheres += ast.literal_eval(where)
    count = crud.payment_method.get_count_where_array(
        db=db,
        where=wheres,
    )
    payment_method_list = crud.payment_method.get_multi_where_array_v2(
        db=db,
        where=wheres,
        order_by=order_by,
        order=order,
        limit=limit,
        skip=offset,
        relations=relations,
        base_columns=base_columns,
    )

    return {
        "count": count,
        "data": payment_method_list,
    }


@router.post("")
def create_payment_method(
    *,
    db: Session = Depends(get_session),
    payment_method_in: schemas.PaymentMethodCreate,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Create new payment_method.
    """
    return crud.payment_method.create(db=db, obj_in=payment_method_in)


@router.get("/{id}")
def read_payment_method(
    *,
    db: Session = Depends(get_session),
    id: int,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Get payment_method by ID.
    """
    data = crud.payment_method.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="Product not found")

    return data


@router.put("/{id}", response_model=PaymentMethodUpdate)
def update_payment_method(
    *,
    db: Session = Depends(get_session),
    id: int,
    product_in: schemas.PaymentMethodUpdate,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Update an payment_method.
    """
    data = crud.payment_method.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="Issue not found")

    data = crud.payment_method.update(db=db, db_obj=data, obj_in=product_in)

    return to_dict(data)


@router.delete("/{id}", response_model=Any)
def delete_payment_method(
    *,
    db: Session = Depends(get_session),
    id: int,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Delete an payment_method.
    """

    data = crud.payment_method.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="Product not found")

    return to_dict(crud.payment_method.soft_delete(db=db, id=id))
