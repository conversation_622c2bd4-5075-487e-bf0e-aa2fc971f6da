from io import BytesIO
import json
from typing import Any
from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi.encoders import jsonable_encoder
from fastapi.responses import StreamingResponse
import requests
from sqlalchemy.orm import Session
from app.enums.billing_period_interval_enum import BillingPeriodIntervalEnum
import ast

from app.api.api_v2.deps import get_user
from app.config.db.database import get_session
from app.models.customer import Customer
from app.schemas.subscription_payment_transaction import (
    SubscriptionPaymentTransactionUpdate,
)
from app.crud.pvgis_service import (
    generatePaymentReference
)
from app.utils.utils import get_from_emb_dict, to_dict
from app import schemas
from app.config.settings import get_settings
from app import crud
import stripe

router = APIRouter()

settings = get_settings()


@router.get("")
def read_subscription_payment_transactions(
    db: Session = Depends(get_session),
    offset: int = 0,
    limit: int = 100,
    where: str = "",
    order_by: str = "id",
    order: str = "ASC",
    relations: str="['payment_method', 'subscription']",
    base_columns: str = "['created_at']"
) -> Any:
    wheres = []
    base_columns = ast.literal_eval(base_columns)
    relations = ast.literal_eval(relations)
    if where is not None and where != "" and where != []:
        wheres += ast.literal_eval(where)
    count = crud.subscription_payment_transaction.get_count_where_array(
        db=db,
        where=wheres,
    )
    subscription_payment_transaction_list = crud.subscription_payment_transaction.get_multi_where_array_v2(
        db=db,
        where=wheres,
        order_by=order_by,
        order=order,
        limit=limit,
        skip=offset,
        relations=relations,
        base_columns=base_columns,
    )
    for transaction in subscription_payment_transaction_list:
        transaction.invoice_receipt_displayed_name = getInvoiceCustomerName(transaction.customer_json)
    return {
        "count": count,
        "data": subscription_payment_transaction_list,
    }

@router.get("/cron/populate_missing_subscription_id")
def cron_populate_missing_subscription_id(
    *,
    db: Session = Depends(get_session),
    x_app_key: str,
    limit: int = 100,
) -> Any:
    if x_app_key != settings.X_APP_KEY:
        return HTTPException(status_code=401)
    
    subscription_payment_transaction_list = crud.subscription_payment_transaction.get_multi_where_array(
        db=db,
        where=[{"key":"subscription_id", "operator": "isNull"}],
        order_by="id", 
        order="DESC",
        limit=limit,
    )
    subscription_stripe_ids = [subscription_payment_transaction.subscription_stripe_id for subscription_payment_transaction in subscription_payment_transaction_list]
    subscription_list = crud.subscription.get_multi_where_array(
        db=db,      
        where=[{"key":"subscription_stripe_id", "operator": "in", "value": subscription_stripe_ids}],
        limit=limit,
    )
    updated_subscription_payment_transaction = []
    subscription_dict = {subscription.subscription_stripe_id: subscription.id for subscription in subscription_list}
    for subscription_payment_transaction in subscription_payment_transaction_list:
        subscription_stripe_id = subscription_payment_transaction.subscription_stripe_id
        if subscription_stripe_id in subscription_dict:
            subscription_id = subscription_dict[subscription_stripe_id]
            updated_subscription_payment_transaction.append({
                "id": subscription_payment_transaction.id,
                "subscription_id": subscription_id,
                "subscription_stripe_id": subscription_stripe_id,                                               
            })
            crud.subscription_payment_transaction.update(
                db=db,
                db_obj=subscription_payment_transaction,
                obj_in={"subscription_id": subscription_id},
            )
            
    return {
        "message": "Subscription ID populated successfully",
        "updated_count": len(updated_subscription_payment_transaction),
        "updated_subscription_payment_transaction": updated_subscription_payment_transaction,
    }

@router.post("")
def create_subscription_payment_transaction(
    *,
    db: Session = Depends(get_session),
    subscription_payment_transaction_in: schemas.SubscriptionPaymentTransactionCreate,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Create new crud.subscription_payment_transaction.
    """
    crud.subscription_payment_transaction.get_subscription_by_id_with_number_of_simulation(
        db=db, id=subscription_payment_transaction_in.subscription_id
    )
    crud.subscription_payment_transaction.get_payment_method_by_id(
        db=db, id=subscription_payment_transaction_in.payment_method_id
    )
    
    new_subscription_payement=  crud.subscription_payment_transaction.create(
        db=db, obj_in=subscription_payment_transaction_in
    )
    reference= generatePaymentReference(new_subscription_payement.id)
    new_subscription_payement_with_ref = crud.subscription_payment_transaction.update(
        db=db, db_obj=reference, obj_in=new_subscription_payement
    )
    return new_subscription_payement_with_ref


# @router.post("/test_subscription_transaction_email")
# def create_subscription_payment_transaction() -> Any:
#     """
#     Create new crud.subscription_payment_transaction.
#     """
#     res = crud.subscription_payment_transaction.send_email_after_subscription_payment("IT'S JUST A TEST", 3000, "$")
#     return res


@router.get("/{id}")
def read_subscription_payment_transaction(
    *,
    db: Session = Depends(get_session),
    id: int,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Get subscription_payment_transaction by ID.
    """
    data = crud.subscription_payment_transaction.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="Product not found")

    return data


@router.put("/{id}", response_model=SubscriptionPaymentTransactionUpdate)
def update_subscription_payment_transaction(
    *,
    db: Session = Depends(get_session),
    id: int,
    product_in: schemas.SubscriptionPaymentTransactionUpdate,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Update an crud.subscription_payment_transaction.
    """
    data = crud.subscription_payment_transaction.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="Issue not found")

    data = crud.subscription_payment_transaction.update(
        db=db, db_obj=data, obj_in=product_in
    )

    return to_dict(data)


@router.delete("/{id}", response_model=Any)
def delete_subscription_payment_transaction(
    *,
    db: Session = Depends(get_session),
    id: int,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Delete an crud.subscription_payment_transaction.
    """

    data = crud.subscription_payment_transaction.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="Product not found")

    return to_dict(crud.subscription_payment_transaction.soft_delete(db=db, id=id))

def getInvoiceCustomerName(customer: dict) :
    first_name = customer['first_name']
    last_name = customer['last_name']
    account_information = customer['account_information']
    title = customer['title']

    company_name = account_information['company_name']
    account_type_id = account_information['account_type_id']

    customer_name = None

    if (account_type_id != 2) :
        customer_name = (company_name
            if company_name
            else f"{title or ''} {first_name or ''} {last_name or ''}".strip()
            )
    else:
        customer_name = (f"{title or ''} {first_name or ''} {last_name or ''}".strip())
    

    return customer_name



@router.get("/download/{doc_type}/{subscription_payment_id}")
def download_document(
    *,
    db: Session = Depends(get_session),
    current_user: Any = Depends(get_user),
    doc_type: str,
    language: str =  Query(default="en"),
    customer_info_str = Query(default="null"),
    subscription_payment_id:int # can be receipt, can be transaction
) -> Any:
    
    custom_customer_info = json.loads(customer_info_str) if customer_info_str and customer_info_str != "null" else None
    
    # if current user is not admin, customCustomerInfo must not be provided
    
    if not ('isAdmin' in current_user and current_user['isAdmin'] )and custom_customer_info is not None:
        raise HTTPException(status_code=401, detail="Unauthorized")
    
    relations = ['payment_method','subscription_payment_receipts{id,reference,created_at,stripe_reference}', 'subscription','subscription{customer_id,product_json,start_date,expired_date}','cart','cart{product_json}']
    baseColumns = ['stripe_terminal_status','created_at','payment_date','paid','amount_paid','reference','payment_transaction_json']
    where = None
    if doc_type == 'invoice':
        where = [{"key":"id","operator":"==","value":subscription_payment_id}]
    elif doc_type == 'receipt' :
        where = [{"key":"subscription_payment_receipts.id","operator":"==","value":subscription_payment_id}]
    else:
        raise HTTPException(status_code=404, detail="Unknown document")
    
    subscription_payment_transaction = crud.subscription_payment_transaction.get_first_where_array_v2(
        db=db, 
        where=where,
        relations= relations,
        base_columns=baseColumns
    )
     
    if not subscription_payment_transaction:
        raise HTTPException(status_code=404, detail="Document not found")
    
    data = subscription_payment_transaction
    customer_json = subscription_payment_transaction.customer_json
    if customer_json is None or customer_json['id'] != subscription_payment_transaction.subscription.customer_id:
        raise HTTPException(status_code=401, detail="Unauthorized")
    
    account_pvgis_info = requests.get(f"{settings.SETTINGS_API_URL}/setting/key/pvgis__ui.account_pvgis_info")
    account_pvgis_info=account_pvgis_info.json()["value"]
     
   
    templatename = "pvgis/subscription-receipt" if doc_type == 'receipt' else  "pvgis/invoice"
    
    payment_transaction_json = jsonable_encoder(subscription_payment_transaction.payment_transaction_json)
    
    discount = get_from_emb_dict(payment_transaction_json, 'discount', 'coupon', 'percent_off') or 0
    product_json = jsonable_encoder(data.cart.product_json )
    subtotal = (
        (get_from_emb_dict(payment_transaction_json, 'subtotal') or 0)/100 
        or product_json['monthly_price']
    )
    discount_value = (subtotal * discount) / 100


    metadata = None
    payment_transaction_json_id = get_from_emb_dict(payment_transaction_json, 'id')
    if (payment_transaction_json_id.startswith("in_")):
        metadata = get_from_emb_dict(payment_transaction_json, 'lines', 'data', 0, 'metadata')
    elif (payment_transaction_json_id.startswith("pi_")):
        metadata = get_from_emb_dict(payment_transaction_json, 'metadata')
    elif (payment_transaction_json['object'] == "checkout.session"):
        metadata = get_from_emb_dict(payment_transaction_json, 'metadata')
   
    
    accounting_json_str = get_from_emb_dict(metadata, 'accountingJsonStr')
    accounting = json.loads(accounting_json_str or 'null')
   
    
    #Specific variables:
    stripe_reference = None
    invoice_number = data.reference 
    receipt_number = None
    if doc_type == 'receipt':
        subscription_payment_receipt = next((r for r in data.subscription_payment_receipts if r.id == subscription_payment_id), None)
        # Retro compat
        if accounting: 
            accounting['amount_paid'] = subscription_payment_receipt.amount_paid
            accounting['remaining_to_pay'] = subscription_payment_receipt.remaining_to_pay
            accounting['amount_already_paid'] = subscription_payment_receipt.amount_already_paid
         
        receipt_number = subscription_payment_receipt.reference
        date = subscription_payment_receipt.created_at
         
         
            
        stripe_reference = subscription_payment_receipt.stripe_reference
        
         
    else : 
        date = data.payment_date
        stripe_reference = payment_transaction_json['number'] if payment_transaction_json['object'] == 'invoice' else payment_transaction_json['id']
    
    
    stripe_reference = stripe_reference[:35] if stripe_reference and len(stripe_reference) else '-'
    filename = (doc_type)+"-"
    account_info = customer_json['account_information']
    filename_ref = receipt_number if doc_type == 'receipt' else invoice_number
    if (account_info['account_type_id'] != 2 and account_info['company_name'] != ""):
        filename += account_info['company_name'] + "-" + filename_ref + ".pdf"
    else:
        filename += customer_json['first_name'] + "-" + filename_ref + ".pdf"
        
    customer_info = {
        "customer_name": getInvoiceCustomerName(customer_json),
        "postal_code": customer_json['district_postal_code'] or '',
        "city": customer_json['city'] or '',
        "country": customer_json['country'] or '',
        "street_address": customer_json['street_address'] or '',
        "mobile_number": customer_json['mobile_number'] or '',
        "email": customer_json['email'] or '',
    }
    if custom_customer_info:
        customer_info.update({
            "customer_name": get_from_emb_dict(custom_customer_info, 'displayed_name') or '',
            "postal_code": get_from_emb_dict(custom_customer_info, 'postal_code') or '' ,
            "city": get_from_emb_dict(custom_customer_info, 'city') or '' ,
            "country": get_from_emb_dict(custom_customer_info, 'country') or '',
            "street_address": get_from_emb_dict(custom_customer_info, 'street_address') or '',
            "mobile_number": get_from_emb_dict(custom_customer_info, 'mobile_number') or '' ,
            "email":  get_from_emb_dict(custom_customer_info, 'email') or '',
        })
    print_data = {
        "language": language,
        "printTemplate": templatename,
        "templateVars": {
            "docType": doc_type,
            "language": language,
            "customer_info": customer_info,
            "payment": {
                "accounting": accounting,
                "date": date,
                "payment_method": "Stripe (Card)",
                "stripe_reference": stripe_reference,
                "invoice_number": invoice_number,
                "receipt_number": receipt_number,
                 
                "subtotal": subtotal,
                "total": data.amount_paid,
                "amount_paid": data.amount_paid,
                "discount": discount,
                "discount_value": discount_value,
            },
            "product": [
                {
                    "name": product_json['name'],
                    "quantity": 1,
                    "billing_period_interval": product_json['billing_period_interval'] or BillingPeriodIntervalEnum.month.value,
                    "unit_price": product_json['monthly_price'],
                    "price":  product_json['monthly_price'],
                    
                     
                    "started_date": data.subscription.start_date,
                    "expired_date": data.subscription.expired_date,
                    "user_count": product_json['user_count'],
                    "monthly_credit": product_json['monthly_credit'],
                    "product_features": product_json['product_features'],
                },
            ],
            "accountPvgisInfo": account_pvgis_info,
        },
        "margin": {
            "top": "10mm",
            "bottom": "10mm",
            "left": "10mm",
            "right": "10mm",
        },
    }
    print_data = jsonable_encoder(print_data)
    
     
    response = requests.post(settings.PRINT_URL, json=print_data)
    response.raise_for_status()  # will raise an error if request failed
    bytesRes = BytesIO(response.content)

     
    return StreamingResponse(bytesRes, media_type="application/pdf", headers={
        "Content-Disposition": f"attachment; filename={filename}"
    })

