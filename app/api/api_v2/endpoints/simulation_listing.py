from typing import Any
from fastapi import APIRouter, Depends, HTTPException
from app.crud.simulation_listing_service import publishSimListingOnFacebook
from sqlalchemy.orm import Session

from app.api.api_v2.deps import get_user
from app.config.db.database import get_session
from app.schemas.simulation_listing import SimulationListingCreate, SimulationListingUpdate, SimulationListingPublish

from app.crud.crud_simulation_listing import simulation_listing
from app import schemas
from app import crud

router = APIRouter()

import ast

@router.get("/list")
def read_simulation_listing(
    db: Session = Depends(get_session),
    offset: int = 0,
    limit: int = 100,
    where: str = "",
    order_by: str = "id",
    order: str = "ASC",
    relations: str="[]",
    base_columns: str = "['created_at']",
    ip: str = None
) -> Any:
    print("/simulation_listing, clientIp", ip)
    
    wheres = []
    base_columns = ast.literal_eval(base_columns)
    relations = ast.literal_eval(relations)
    if where is not None and where != "" and where != []:
        wheres += ast.literal_eval(where)
    count = crud.simulation_listing.get_count_where_array(
        db=db,
        where=wheres,
    )
    simulation_listing_list = crud.simulation_listing.get_multi_where_array_v2(
        db=db,
        where=wheres,
        order_by=order_by,
        order=order,
        limit=limit,
        skip=offset,
        relations=relations,
        base_columns=base_columns,
    )

    return {
        "count": count,
        "data": simulation_listing_list,
    }


@router.post("", response_model=Any)
def create_simulation_listing(
    *,
    db: Session = Depends(get_session),
    simulation_listing_in: schemas.SimulationListingCreate,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Create new crud.simulation_listing.
    """
    data = crud.simulation_listing.create(db=db, obj_in=simulation_listing_in)

    return data


@router.get("/{id}")
def get_by_id(
    *,
    db: Session = Depends(get_session),
    id: int,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Get simulation_listing by ID.
    """
    data = crud.simulation_listing.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="simulation_listing_not_found")

    return data


@router.put("/{id}")
def update_simulation_listing(
    *,
    db: Session = Depends(get_session),
    id: int,
    simulation_listing_in: schemas.SimulationListingUpdate,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Update an crud.simulation_listing.
    """
    data = crud.simulation_listing.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="simulation_listing_not_found")

    data = crud.simulation_listing.update(db=db, db_obj=data, obj_in=simulation_listing_in)

    return data


@router.delete("/{id}", response_model=Any)
def delete_simulation_listing(
    *,
    db: Session = Depends(get_session),
    id: int,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Delete an crud.simulation_listing.
    """

    data = crud.simulation_listing.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="simulation_listing_not_found")

    return crud.simulation_listing.soft_delete(db=db, id=id)


@router.post("/publish/facebook", response_model=Any)
def create_simulation_listing(
    *,
    db: Session = Depends(get_session),
    simulation_listing_publish: SimulationListingPublish,
    
) -> Any:
    """
    Publish to facebook
    """ 
    return publishSimListingOnFacebook(simulation_listing_publish)
