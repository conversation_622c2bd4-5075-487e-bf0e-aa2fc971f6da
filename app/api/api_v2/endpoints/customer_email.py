import copy
import json
import traceback
from typing import Any
import tempfile
from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi.encoders import jsonable_encoder
from tzlocal import get_localzone
from app.models.country import Country
from app.schemas.customer_email_item import CustomerEmailItemGenerateParams
from app.services.country_migration_service import integrate_senders, integrate_senders_and_default_data
from app.services.customer_email.customer_email_item_factories import generate_email_items, generate_query_welcome_and_discovery, get_sender, send_to_when_welcome_and_discovery
from app.services.stripe.stripe_service import get_welcome_discount_validity_period
from app.utils.utils import send_email
from requests import Session
from app.config.db.database import get_session
from app.schemas.customer import (
    CustomerCreate,
    CustomerUpdate,
    RegistrationData,
    CustomerUpdateInfos,
    CustomerAccountInfoCreate,
)
from app.schemas.account_information import AccountInformationBase
import ast
from app.crud.crud_customer import customer
from app.crud.crud_account_information import account_information
from app.crud.crud_login import login
from app.utils.utils import to_dict
from app.api.api_v2.deps import get_user
import requests
from dotenv import load_dotenv
import os
from app import schemas
from app.api.api_v2.deps import get_current_language
from app import crud
from datetime import datetime, timedelta, timezone
from app.api.api_v2.deps import get_optional_customer, get_current_language
from typing import Optional
from app.enums.subscription_status_enum import SubscriptionStatusEnum
from app.services.notification.notification import send_notif
from app.config.settings import get_settings
from sqlalchemy import func, text
from app.crud.pvgis_service import create_token, verify_token
from app.models.customer import Customer
from app.enums.title_enum import TitleEnum
from app.services.customer_email.customer_email_item_factories import queries

from app.services.customer_email.customer_email_service import execute_customer_email_campaign, execute_customer_email_tasks, execute_email_task_for_customer, generate_daily_report_dataframe, is_customer_eligible_for_extended_promotion, send_customer_email_item,send_customer_email_item_admin_confirmation,send_daily_report

from app.models.customer_email_item import CustomerEmailItem
import jwt
from fastapi.responses import StreamingResponse
import pandas as pd
import io
from fastapi.responses import FileResponse
from openpyxl.utils import get_column_letter

router = APIRouter()

settings = get_settings()

@router.get("/local-timezone-date")
def get_local_timezone():
    local_tz = get_localzone()
    return {"local_timezone_date": datetime.now(local_tz)}

@router.get("/integrate-senders")
def integrate_senders_( db: Session = Depends(get_session)):
    integrate_senders(db, 'installer_invitation')
    return {"message": "done"}

# senders, default language, default customer data
@router.get("/integrate-senders-and-default-data")
def integrate_senders_and_default_data_(
    db: Session = Depends(get_session),
    campaign_key: str = Query(default=None, title="Campaign key")
    ):
    results = integrate_senders_and_default_data(db, campaign_key or 'installer_invitation')
    return {"message": "done", "results": results}


@router.get('/excel-report')
def excel_report(
    *,
    x_app_key: str,
    db: Session = Depends(get_session), 
    current_datetime:datetime =  Query(default=datetime.now(), title="Current date")
):
    if x_app_key != settings.X_APP_KEY:
        return HTTPException(status_code=401)
    df = generate_daily_report_dataframe(db, current_datetime)
    
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine="openpyxl") as writer:
        df.to_excel(writer, index=False, sheet_name="Report")
        # Access the worksheet
        worksheet = writer.sheets["Report"]
        
        # Auto-adjust column widths
        for col_num, column_title in enumerate(df.columns, start=1):
            max_length = max(
                len(str(column_title)),  # Length of the header
                *(len(str(value)) for value in df[column_title])  # Length of data
            )
            worksheet.column_dimensions[get_column_letter(col_num)].width = max_length + 2

    output.seek(0)

     
    headers = {
        "Content-Disposition": f"attachment; filename=customer_email_report_{current_datetime}.xlsx"
    }
    return StreamingResponse(output, media_type="application/xlsx", headers=headers)
    
    
@router.get("")
def read_customers(
        db: Session = Depends(get_session),
        offset: int = 0,
        limit: int = 100,
        where: str = "",
        order_by: str = "id",
        order: str = "ASC",
        relations: str = "[]",
        base_columns: str = "['created_at']",
) -> Any:
    wheres = []
    base_columns = ast.literal_eval(base_columns)
    relations = ast.literal_eval(relations)
    if where is not None and where != "" and where != []:
        wheres += ast.literal_eval(where)
    count = crud.customer_email_item.get_count_where_array(
        db=db,
        where=wheres,
    )
    items_list = crud.items.get_multi_where_array_v2(
        db=db,
        where=wheres,
        order_by=order_by,
        order=order,
        limit=limit,
        skip=offset,
        relations=relations,
        base_columns=base_columns,
    )

    return {
        "count": count,
        "data": items_list,
    }

    
@router.get('/is-eligible-for-extended-promotion')
def is_eligible_for_extended_promotion(
    *, 
    current_user: Any = Depends(get_user),
    db: Session = Depends(get_session), 
    current_datetime =  Query(default=None, title="Current date")
):
    customer=crud.customer.get_by_user_id(db,current_user["id"])
    if current_datetime is None:
        current_datetime = datetime.now()
    is_eligible = is_customer_eligible_for_extended_promotion(db, customer.id, current_datetime)
    return {
        'is_eligible': is_eligible
    }
    

@router.get('/send-emails')
def send_customers_emails(
    *,
    x_app_key: str,
    db: Session = Depends(get_session), 
    campaign_key: str = Query(default=None, title="Campaign key"),
    account_type_id:int =  Query(default=2, title="Account type id"),
    chunk_size_per_task:int =  Query(default=200, title="Chunk size per task"),
    chunk_size:int =  Query(default=None, title="Chunk size"),
    current_datetime: datetime =  Query(default=None, title="Current datetime")
):
    if x_app_key != settings.X_APP_KEY:
        return HTTPException(status_code=401, detail="Invalid x-app-key")
    current_datetime = current_datetime or datetime.now()
    
    
    # Define the target datetime in UTC
    target_time_start = datetime(2025, 6, 5, 14, 0, tzinfo=timezone.utc) 
    # Get current UTC time
    now_utc = datetime.now(timezone.utc)

    # Condition to execute code if current time is before target time
    if now_utc < target_time_start:
        return {"message": "Too early, will start sending emails at "+target_time_start.strftime('%Y-%m-%d %H:%M:%S %Z%z')}
        
    if campaign_key is None:
        return HTTPException(status_code=401, detail="Campaign key is required")
        
    if not chunk_size:
        chunk_size = int(settings.DEFAULT_CAMPAIGN_MAIL_CHUNK_SIZE)
    validity_month_count= get_welcome_discount_validity_period()
    special_offer_duration = timedelta(days=validity_month_count*30)
    
    params=  CustomerEmailItemGenerateParams(
                   current_datetime=current_datetime, 
                   account_type_id = account_type_id,
                   chunk_size_per_task=chunk_size_per_task,
                   chunk_size = chunk_size,
                   special_offer_duration=special_offer_duration
                   ) 
     
    
    # removed email 5 for now
    email_items = execute_customer_email_campaign(db,campaign_key, params) 
    return {"sent_emails": email_items}




@router.get('/send-email/{email_task_key}')
def send_customers_emails_by_key(
    *,
    x_app_key: str,
    db: Session = Depends(get_session),
    # current_user: Any = Depends(get_user),
    email_task_key: str = None, 
    current_datetime:datetime =  Query(default=datetime.now(), title="Current date"),
    chunk_size_per_task: int = Query(default=200, title="Chunk size per task"),
):
    if x_app_key != settings.X_APP_KEY:
        return HTTPException(status_code=401)
    validity_month_count= get_welcome_discount_validity_period()
    special_offer_duration = timedelta(days=validity_month_count*30)
    params=  CustomerEmailItemGenerateParams(
                   current_datetime =current_datetime, 
                   account_type_id=2,
                   chunk_size_per_task=chunk_size_per_task,
                   special_offer_duration=special_offer_duration
                   ) 
    email_items = execute_customer_email_tasks(db, params, email_task_key) 
    return {"sent_emails": email_items}
 
@router.get('/send-email/{email_task_key}/{customer_id}')
def send_customers_emails(
    *,
    x_app_key: str,
    email_task_key: str = None, 
    customer_id: str = None, 
    db: Session = Depends(get_session), 
):
    if x_app_key != settings.X_APP_KEY:
        return HTTPException(status_code=401)
    current_datetime = datetime.now()
    email_items = execute_email_task_for_customer (db, customer_id,email_task_key, current_datetime)
    return {"sent_emails": email_items}

@router.post('/send-report-email')
def send_report_email(
    *,
    x_app_key: str,
    db: Session = Depends(get_session),
    current_datetime:datetime =  Query(default=None, title="Current date")
):
    if current_datetime is None:
        current_datetime = datetime.now()  
   
    if x_app_key != settings.X_APP_KEY:
        return HTTPException(status_code=401)
    try:
        send_daily_report(db, current_datetime)
        return {"message": "Report Email sent successfully!", "report_date": current_datetime}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error sending email: {str(e)}")

@router.post('/send_test_rapport_email')
def send_testreport_email(
    *,
    x_app_key: str,
    db: Session = Depends(get_session),
    current_datetime:datetime =  Query(default=datetime.now(), title="Current date")
):
     
    if x_app_key != settings.X_APP_KEY:
        return HTTPException(status_code=401)
    try:
        send_daily_report(db, current_datetime)
        return {"message": "Report Email sent successfully!"}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error sending email: {str(e)}")

 
@router.get('/customers-to-reach/{email_task_key}')
def get_customers_for_welcome_and_discovery(
    *,
    x_app_key: str,
    db: Session = Depends(get_session),
    # current_user: Any = Depends(get_user),
    email_task_key: str = None,
    current_datetime:datetime =  Query(default=datetime.now(), title="Current date"),
    chunk_size_per_task:int =  Query(default=200, title="Chunk size per task")
):
    if x_app_key != settings.X_APP_KEY:
        return HTTPException(status_code=401)
    validity_month_count= get_welcome_discount_validity_period()
    special_offer_duration = timedelta(days=validity_month_count*30)
    tasks = crud.customer_email_task.load_all(db)
    task = next((t for t in tasks if t.key == email_task_key), None )
    query = queries[email_task_key](task, db, CustomerEmailItemGenerateParams(
            current_datetime,
            1,
            special_offer_duration,
            chunk_size_per_task
        ))
    return {"query": str(query.statement.compile(compile_kwargs={"literal_binds": True})).replace('\n', ' '), "results": query.all()}

@router.get('/welcome-offer-validity-period')
def welcome_offer_validity_period():
    return {"validity_period": get_welcome_discount_validity_period()}

@router.get('/email-stats')
def get_email_stats(
    *,
    x_app_key: str,
    db: Session = Depends(get_session),
    current_user: Any = Depends(get_user),
    current_datetime:datetime =  Query(default=datetime.now(), title="Current date")
): 
    if x_app_key != settings.X_APP_KEY:
        return HTTPException(status_code=401)
    return crud.customer_email_item.get_customer_email_item_stats(db, current_datetime)

@router.get('/email_test')
def test_discov(
    *,
    x_app_key: str,
    db: Session = Depends(get_session),
    current_user: Any = Depends(get_user),
    
):
    if x_app_key != settings.X_APP_KEY:
        return HTTPException(status_code=401)
    tasks = crud.customer_email_task.load_all(db)
    
     
    return {"message": send_to_when_welcome_and_discovery(tasks[0], db, datetime.now(), 1)}


@router.post('/send_test_email')
def send_test_email(
    *,
    x_app_key: str,
    db: Session = Depends(get_session)
):
    if x_app_key != settings.X_APP_KEY:
        return HTTPException(status_code=401)
    country = Country(
        name="Testland",
        code_alpha_2= 'ts',
        sender_json= {"name": "Maria", "email": "<EMAIL>"}
    )
    email_tasks = crud.customer_email_task.load_all(db)
    customer_email_task=next((t for t in email_tasks if t.email_number == 1) , None)
    sender = get_sender(country, customer_email_task)
    customer = Customer(
        id=12, 
        first_name="Zola",  
        last_name="Fotsiny", 
        email="<EMAIL>",  
        pseudo="johndoe123",  
        title="Mr",
        street_address="123 Test Street", 
        district_postal_code="12345",  
        city="Test City", 
        country="Testland", 
    )
    email_item = CustomerEmailItem(
        id=1, 
        sender_email=sender.email,
        language="fa",  
        customer_id=12,  
        country_id=456,  
        customer_email_task_id=1,  
        sent_at=datetime.now(),
        customer_email_task=customer_email_task,
        customer=customer

    )
    email_item.sender = sender
    email_item.country = country
    try:

        email_res = send_customer_email_item(db, email_item)
        if not email_res: 
            raise Exception('ERROR SENDING EMAIL')
        return {"message": "Email sent successfully!"}

    except Exception as e:
        traceback.print_exc()  # Log the error for debugging
        raise HTTPException(status_code=500, detail=f"Error sending email: {str(e)}")
    


@router.post('/send_test_confirmation_email')
def send_test_confirmation_email(
    *,
    x_app_key: str,
    db: Session = Depends(get_session)
):
    if x_app_key != settings.X_APP_KEY:
        return HTTPException(status_code=401)
    email_tasks = crud.customer_email_task.load_all(db)

    customer = Customer(
        id=12, 
        first_name="Zola",  
        last_name="Fotsiny", 
        email="<EMAIL>",  
        pseudo="johndoe123",  
        title="Mr",
        street_address="123 Test Street", 
        district_postal_code="12345",  
        city="Test City", 
        country="Testland", 
    ) 
    email_item = CustomerEmailItem(
        id=1, 
        sender_email="<EMAIL>", 
        language="fa",  
        customer_id=12,  
        country_id=456,  
        customer_email_task_id=1,  
        sent_at=datetime.now(),
        customer_email_task=next((t for t in email_tasks if t.email_number == 1) , None),
        customer=customer

    )

    try:

        send_customer_email_item_admin_confirmation(email_item)
        return {"message": "Email sent successfully!"}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error sending email: {str(e)}")
    





# @router.post('/send_test_rapport_email')
# def send_test_rapport_email(
#     *,
#     #x_app_key: str,
#     db: Session = Depends(get_session),
#     current_datetime:datetime =  Query(default=datetime.now(), title="Current date")
# ):
#     # if x_app_key != settings.X_APP_KEY:
#     #     return HTTPException(status_code=401)
#     try:
#         # send_daily_report(db, current_datetime)
#         return {"message": "Report Email sent successfully!"}

#     except Exception as e:
#         raise HTTPException(status_code=500, detail=f"Error sending email: {str(e)}")