import io
import requests
import pandas as pd
from typing import Any
from app.config.settings import get_settings
from app.config.db.database import get_session
from app.api.api_v2.deps import get_user
from app.models.account_information import AccountInformation
from app.models.account_type import AccountType
from app.models.country import Country
from app.models.customer import Customer
from app.models.customer_email_item import CustomerEmailItem
from app.models.customer_email_task import CustomerEmailTask
from app.models.subscription import Subscription
from sqlalchemy.sql import and_, literal, func, case
from sqlalchemy import func
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session, aliased
from fastapi.responses import StreamingResponse
from app.enums.subscription_status_enum import SubscriptionStatusEnum
from app.crud.pvgis_service import create_token
import jwt
from app.services.brevo.brevo import reset_and_import_contacts
from app.services.brevo.data import get_data_user_mail
router = APIRouter()

settings = get_settings()

@router.post("/import-brevo")
def import_brevo(
  *,
  db: Session = Depends(get_session),
  x_app_key: str,
  campaign_id: int,
) -> Any:
  if x_app_key != settings.X_APP_KEY:
    return HTTPException(status_code=401)

  subscription_sub_query = (
      db.query(
          Subscription.customer_id.label("customer_id"),
          literal(True).label("is_subscribed_user")
      )
      .filter(
          and_(
              Subscription.subscription_status == "ACTIVE",
              Subscription.product_id != 11142024,
              Subscription.product_id.isnot(None),
          )
      )
      .subquery()
  )
  
  latest_email_item_subquery = (
    db.query(
        CustomerEmailItem.customer_id,
        func.max(CustomerEmailItem.sent_at).label("latest_sent_at"),
    )
    .join(CustomerEmailTask, CustomerEmailTask.id == CustomerEmailItem.customer_email_task_id)
    .filter(
      and_(
        CustomerEmailTask.customer_email_campaign_id == campaign_id,
      )
    )
    .group_by(CustomerEmailItem.customer_id)
    .subquery()
  )
  
  print(latest_email_item_subquery)

  # Main query
  # rows = (
  #   db.query(
  #       Customer.id,
  #       Customer.full_name,
  #       Customer.email,
  #       Customer.settings_json,
  #       Customer.auth_user_id,
  #       Customer.timezone_offset,
  #       Country.name.label("country_name"),
  #       Country.timezone_name.label("timezone_name"),
  #       AccountType.name.label("user_type"),
  #       func.coalesce(subscription_sub_query.c.is_subscribed_user, False).label("is_subscribed_user"),

  #       CustomerEmailItem.id.label("email_item_id"),
  #       CustomerEmailItem.sent_at.label("email_item_sent_at"),
  #       CustomerEmailItem.customer_email_task_id.label("customer_email_task_id"),
  #       CustomerEmailItem.language.label("email_item_language"),
  #       CustomerEmailItem.sender_email.label("email_item_sender"),
  #       CustomerEmailItem.error.label("email_item_error"),

  #       # Default current_task to 1 if not found
  #       case(
  #         [(CustomerEmailItem.current_task.is_(None), -1)],
  #         else_=CustomerEmailItem.current_task
  #       ).label("current_task")
  #   )
  #   .outerjoin(subscription_sub_query, subscription_sub_query.c.customer_id == Customer.id)
  #   .outerjoin(AccountInformation, AccountInformation.customer_id == Customer.id)
  #   .outerjoin(AccountType, AccountType.id == AccountInformation.account_type_id)
  #   .outerjoin(Country, Country.id == Customer.country_id)

  #   # Join with latest email item subquery
  #   .outerjoin(
  #       latest_email_item_subquery,
  #       latest_email_item_subquery.c.customer_id == Customer.id,
  #   )
  #   # Join with CustomerEmailItem to get full record
  #   .outerjoin(
  #       CustomerEmailItem,
  #       and_(
  #           CustomerEmailItem.customer_id == Customer.id,
  #           CustomerEmailItem.sent_at == latest_email_item_subquery.c.latest_sent_at,
  #           CustomerEmailItem.customer_email_task_id.in_(
  #               db.query(CustomerEmailTask.id)
  #               .filter(CustomerEmailTask.customer_email_campaign_id == campaign_id)
  #           )
  #       )
  #   )

  #   .filter(
  #       Customer.auth_user_id.isnot(None),
  #       Customer.deleted_at.is_(None),
  #   )
  #   .group_by(Customer.id, CustomerEmailItem.id)  # Ensure uniqueness
  #   .all()
  # )
  
  rows = get_data_user_mail(db, campaign_id)
  
  data = []
  for r in rows:
    language = None
    if r.settings_json and isinstance(r.settings_json, dict):
      language = r.settings_json.get("language") or r.settings_json.get("lang")
    language = language or "en"

    token_data = {
      "token_type": "invitation",
      "customer": {
          "id": r.id,
          "auth_user_id": r.auth_user_id,
      }, 
      "no_limit": True,
    }
    
    data.append({
      "email": r.email,
      "attributes": {
        "EMAIL": r.email,
        "FULL_NAME": r.full_name or "",
        "LANGUAGE": language,
        "COUNTRY": r.country_name or "",
        "TIMEZONE": r.timezone_name or "",
        "USER_TYPE": r.user_type or "",
        "IS_SUBSCRIBED": "Yes" if r.is_subscribed_user else "No",
        "TOKEN": create_token(token_data),
        "CAMPAIGN_ID": campaign_id,
        "CAMPAIGN_EMAIL_NUMBER": 0 if r.current_task in [-1, None] else r.current_task + 1
      },
    })
     
  return data
  
  if not data:
    print("No contacts to import.")
    return
  
  # payload = {
  #   "listIds": [int(settings.BREVO_LIST_ID)],
  #   "updateExistingContacts": True,
  #   "jsonBody": data,
  # }
  
  # headers = {
  #   "accept": "application/json",
  #   "content-type": "application/json",
  #   "api-key": settings.BREVO_API_KEY,
  # }

  # print(f"Importing {len(data)} contacts to Brevo list ID {settings.BREVO_LIST_ID}...")
  # response = requests.post(settings.BREVO_API_URL, json=payload, headers=headers)

  # if response.status_code == 202:
  #   print("Successfully initiated contact import process.")
  #   print("Response:", response.json())
  # else:
  #   print(f"Error importing contacts: {response.status_code}")
  #   print("Response:", response.text)
  
  # reset_and_import_contacts(data)

  # reset_and_import_contacts(int(settings.BREVO_LIST_ID), data)

  return data
