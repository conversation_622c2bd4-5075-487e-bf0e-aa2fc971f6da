from ast import List
from typing import Any
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.api.api_v2.deps import get_user
from app.config.db.database import get_session
from app.schemas.features import FeaturesCreate, FeaturesUpdate

from app.crud.crud_features import features
from app.utils.utils import to_dict
from app import schemas
from app import crud

router = APIRouter()


import ast

@router.get("")
def read_features(
    db: Session = Depends(get_session),
    offset: int = 0,
    limit: int = 100,
    where: str = "",
    order_by: str = "id",
    order: str = "ASC",
    relations: str="[]",
    base_columns: str = "['created_at']"
) -> Any:
    wheres = []
    base_columns = ast.literal_eval(base_columns)
    relations = ast.literal_eval(relations)
    if where is not None and where != "" and where != []:
        wheres += ast.literal_eval(where)
    count = crud.features.get_count_where_array(
        db=db,
        where=wheres,
    )
    features_list = crud.features.get_multi_where_array_v2(
        db=db,
        where=wheres,
        order_by=order_by,
        order=order,
        limit=limit,
        skip=offset,
        relations=relations,
        base_columns=base_columns,
    )

    return {
        "count": count,
        "data": features_list,
    }


@router.post("", response_model=Any)
def create_features(
    *,
    db: Session = Depends(get_session),
    features_in: schemas.FeaturesCreate,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Create new crud.features.
    """
    data = crud.features.create(db=db, obj_in=features_in)

    return data


@router.get("/{id}")
def read_features(
    *,
    db: Session = Depends(get_session),
    id: int,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Get features by ID.
    """
    data = crud.features.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="Features not found")

    return data


@router.put("/{id}", response_model=FeaturesUpdate)
def update_features(
    *,
    db: Session = Depends(get_session),
    id: int,
    features_in: schemas.FeaturesUpdate,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Update an crud.features.
    """
    data = crud.features.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="Features not found")

    data = crud.features.update(db=db, db_obj=data, obj_in=features_in)

    return to_dict(data)


@router.delete("/{id}", response_model=Any)
def delete_features(
    *,
    db: Session = Depends(get_session),
    id: int,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Delete an crud.features.
    """

    data = crud.features.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="Features not found")

    return to_dict(crud.features.soft_delete(db=db, id=id))
