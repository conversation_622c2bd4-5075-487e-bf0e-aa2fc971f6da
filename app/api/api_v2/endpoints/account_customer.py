from typing import Any, Optional
import json
import urllib.parse
from fastapi import APIRouter, Depends, HTTPException, Header
import requests
from requests import Session
from app.config.db.database import get_session
from app import schemas, crud
from app.utils.utils import send_email, send_email_cms
from app.config.settings import get_settings
from app.api.api_v2.deps import get_user, get_current_language
from app.services.notification.notification import send_notif
from app.crud.pvgis_service import create_token, verify_token
from app.services.stripe import stripe_service

settings = get_settings()
router = APIRouter()

@router.post("/forgot_password")
def register(
    *,
    db: Session = Depends(get_session),
    data: schemas.ForgotPasswordData,
) -> Any:
    """
    Handles the 'forgot password' process by sending a request to the authentication API.
    """
    payload = {
        "username": data.email,
        "emailInfo": {
          "templateName": "pvgis-com/forgot-password",
          "templateVars": {
            "firstname": "",
            "lastname": "",
            "verification_url": f"{settings.PVGIS_UI_URL}/account/reset-password/",
            "username": data.email,
          },
          "mailVars": {
            "from": settings.SMPT_USER_MAIL,
            "to": data.email,
            "subject": data.subject if data.subject else "Change Password",
          },
          "sentByApp": settings.APP_KEY,
          "sentByProcess": "forgot-password",
          "sentToUser": data.email,
        },
        "language": data.lang if data.lang == "fr" else "en",
    }
    response = requests.post(f"{settings.AUTH_API_URL}/user/forgot-password", json=payload)
    if response.status_code == 202:
        return response.json()
    else:
        raise HTTPException(status_code=response.status_code, detail=response.text)
    

@router.post("/reset_password/{usernameHash}")
def reset_password(
    *,
    db: Session = Depends(get_session),
    body: schemas.ResetPasswordData,
    usernameHash: Any,
    lang: str = Depends(get_current_language),
) -> Any:
    """
    Handles the 'forgot password' process by sending a request to the authentication API.
    """
    try:
        body_data = body.dict()

        # Fetch the hash data
        hash_response = requests.get(f"{settings.AUTH_API_URL}/hash-store/hash-data/{usernameHash}")
        hash_response.raise_for_status()  # Raises an error for bad responses

        # Extract username from hash response
        username = json.loads(hash_response.json()["data"])["additionalData"]

        # Fetch user data based on the username
        user_response = requests.get(f"{settings.AUTH_API_URL}/user/username/{username}")
        user_response.raise_for_status()

        user_data = user_response.json()

        # Update the password
        update_response = requests.post(
            f"{settings.AUTH_API_URL}/user/update-password/{usernameHash}", 
            json=body_data
        )
        update_response.raise_for_status()

        # Prepare notification data
        notification_data = [
            {
                "id": user_data["id"],
                "roleId": 2,
                "appKey": "1284939136",
                "vars": {}
            }
        ]
        
        # Send notification
        send_notif(event_code="reset-password-completed", users=notification_data, mailLanguage=lang)

        return update_response.json()

    except requests.RequestException as e:
        raise HTTPException(status_code=500, detail=f"External service error: {str(e)}")

    except json.JSONDecodeError as e:
        raise HTTPException(status_code=500, detail=f"Error decoding JSON: {str(e)}")

    except KeyError as e:
        raise HTTPException(status_code=500, detail=f"Unexpected response structure: {str(e)}")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred: {str(e)}")


@router.post("/change_email")
def request_email_change(
    *,
    db: Session = Depends(get_session),
    data: schemas.RequestChangeEmailData,
    authorization: Optional[str] = Header(None)
) -> Any:
    """
    Send request to the authentication API for email  change.
    """
    if not authorization:
        raise HTTPException(status_code=401, detail="Authorization header missing")
    
    token = authorization.replace("Bearer ", "")
    
    lang = data.lang
    cms_key = "change-email-request"
    change_email_url = f"{settings.PVGIS_UI_URL}/pvgis24/app/{data.customer_id}/account/change-email?_x={urllib.parse.quote(token, safe='')}&_l={lang}"
    email_param = {
        "templateVars": {
            "first_name": data.firstname,
            "last_name": data.lastname,
            "change_email_url": change_email_url,
        },
        "mailVars": {
            "from": settings.SMPT_USER_MAIL,
            "to": data.email,
            "subject": data.subject if data.subject else "Change Email",
        },
        "baseUrls": {"cmsUrl": settings.CMS_URL, "translationUrl": settings.TRANSLATION_URL},
        "sentByApp": settings.APP_KEY,
        "sentByProcess": "Customer",
        "sentToUser": data.email,
        "type": "MAIL",
    }

    return send_email_cms(email_param, lang, cms_key)

    
@router.post("/confirm_new_email")
def confirm_new_email(
    *,
    db: Session = Depends(get_session),
    data: schemas.RequestChangeEmailData,
    authorization: Optional[str] = Header(None)
) -> Any:
    """
    Send request new email addr.
    """
    if not authorization:
        raise HTTPException(status_code=401, detail="Authorization header missing")
    
    token = authorization.replace("Bearer ", "")
    
    lang = data.lang
    cms_key = "confirm-new-email"
    change_email_url = f"{settings.PVGIS_UI_URL}/pvgis24/app/{data.customer_id}/account/email-update?_x={urllib.parse.quote(token, safe='')}&_l={lang}&from={create_token(data={'email': data.email})}"
    email_param = {
        "templateVars": {
            "first_name": data.firstname,
            "last_name": data.lastname,
            "change_email_url": change_email_url,
        },
        "mailVars": {
            "from": settings.SMPT_USER_MAIL,
            "to": data.email,
            "subject": data.subject if data.subject else "Change Email",
        },
        "baseUrls": {"cmsUrl": settings.CMS_URL, "translationUrl": settings.TRANSLATION_URL},
        "sentByApp": settings.APP_KEY,
        "sentByProcess": "Customer",
        "sentToUser": data.email,
        "type": "MAIL",
    }

    return send_email_cms(email_param, lang, cms_key)


@router.post("/email_update")
def email_update(
    *,
    db: Session = Depends(get_session),
    body: schemas.ChangeEmailData,
    authorization: Optional[str] = Header(None)
) -> Any:
    """
    Handles email change
    """
    if not authorization:
        raise HTTPException(status_code=401, detail="Authorization header missing")
    
    token = authorization.replace("Bearer ", "")
    try:
        new_email = verify_token(body.encoded_email) #email encoded in the token
        
        data = crud.customer.get(db=db, id=body.customer_id)
        if not data:
            raise HTTPException(status_code=404, detail="Customer Not Found")
        
        if not new_email["email"]:
            raise HTTPException(status_code=404, detail="New mail hash invalid")
        
        # update in auth API
        body_data = {
            "oldEmail": data.email,
            "newEmail": new_email["email"],
            "language": body.lang
        }
        update_response = requests.post(
            f"{settings.AUTH_API_URL}/user/email-update/{token}", 
            json=body_data
        )
        update_response.raise_for_status()

        data = crud.customer.update(db=db, db_obj=data, obj_in={"email": new_email["email"]})
        stripe_customer_id = data.stripe_customer_id
        
        # update stripe email
        if stripe_customer_id:
            updated_cust = stripe_service.update_stripe_customer_email(stripe_customer_id, new_email["email"])
            # print('updated_cust check stripe response',updated_cust)
            
            
        # Prepare notification data
        notification_data = [
            {
                "id": data.id,
                "roleId": 2,
                "appKey": "1284939136",
                "vars": {}
            }
        ]
        
        # Send notification
        send_notif(event_code="change-email-completed", users=notification_data, mailLanguage=body.lang)
        return data

    except requests.RequestException as e:
        raise HTTPException(status_code=500, detail=f"External service error: {str(e)}")

    except json.JSONDecodeError as e:
        raise HTTPException(status_code=500, detail=f"Error decoding JSON: {str(e)}")

    except KeyError as e:
        raise HTTPException(status_code=500, detail=f"Unexpected response structure: {str(e)}")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred: {str(e)}")
