from typing import Any
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.api.api_v2.deps import get_user
from app.config.db.database import get_session
from app.schemas.professional_category import (
    ProfessionalCategoryCreate,
    ProfessionalCategoryUpdate,
)

from app.crud.crud_professional_category import professional_category
from app import schemas
from app import crud

router = APIRouter()


import ast

@router.get("")
def read_professional_category(
    db: Session = Depends(get_session),
    offset: int = 0,
    limit: int = 100,
    where: str = "",
    order_by: str = "id",
    order: str = "ASC",
    relations: str="[]",
    base_columns: str = "['created_at']"
) -> Any:
    wheres = []
    base_columns = ast.literal_eval(base_columns)
    relations = ast.literal_eval(relations)
    if where is not None and where != "" and where != []:
        wheres += ast.literal_eval(where)
    count = crud.professional_category.get_count_where_array(
        db=db,
        where=wheres,
    )
    professional_category_list = crud.professional_category.get_multi_where_array_v2(
        db=db,
        where=wheres,
        order_by=order_by,
        order=order,
        limit=limit,
        skip=offset,
        relations=relations,
        base_columns=base_columns,
    )

    return {
        "count": count,
        "data": professional_category_list,
    }


@router.post("", response_model=Any)
def create_professional_category(
    *,
    db: Session = Depends(get_session),
    professional_category_in: schemas.ProfessionalCategoryCreate,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Create new crud.professional_category.
    """
    data = crud.professional_category.create(db=db, obj_in=professional_category_in)

    return data


@router.get("/{id}")
def read_professional_category(
    *,
    db: Session = Depends(get_session),
    id: int,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Get professional_category by ID.
    """
    data = crud.professional_category.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="ProfessionalCategory not found")

    return data


@router.put("/{id}")
def update_professional_category(
    *,
    db: Session = Depends(get_session),
    id: int,
    professional_category_in: schemas.ProfessionalCategoryUpdate,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Update an crud.professional_category.
    """
    data = crud.professional_category.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="ProfessionalCategory not found")

    data = crud.professional_category.update(
        db=db, db_obj=data, obj_in=professional_category_in
    )

    return data


@router.delete("/{id}", response_model=Any)
def delete_professional_category(
    *,
    db: Session = Depends(get_session),
    id: int,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Delete an crud.professional_category.
    """

    data = crud.professional_category.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="ProfessionalCategory not found")

    return crud.professional_category.soft_delete(db=db, id=id)
