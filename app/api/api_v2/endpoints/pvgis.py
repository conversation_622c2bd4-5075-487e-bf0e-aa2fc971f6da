import os


from fastapi import APIRouter, Query

from app.config import settings
from app.crud.pvgis_service import (
    get_degreedays,
    get_extent_db,
    get_horizon_profile,
    get_pvgis_daily,
    get_pvgis_grid_connected_or_tracking,
    get_pvgis_hourly,
    get_pvgis_monthly,
    get_pvgis_offgrid,
    get_pvgis_tmy,
)
from app.schemas.dtos.pvgis_daily_dto import PvgisDailyDto
from app.schemas.dtos.pvgis_degreedays_dto import PvgisDegreedaysDto
from app.schemas.dtos.pvgis_extent_dto import PvgisExtentDto
from app.schemas.dtos.pvgis_grid_connected_or_tracking_dto import (
    PvgisGridConnectedOrTrackingDto,
)
from app.schemas.dtos.pvgis_horizon_profile_dto import PvgisHorizonProfileDto
from app.schemas.dtos.pvgis_hourly_dto import PvgisHourlyDto
from app.schemas.dtos.pvgis_monthly_dto import PvgisMonthlyDto
from app.schemas.dtos.pvgis_offgrid_dto import PvgisOffgridDto
from app.schemas.dtos.pvgis_tmy_dto import PvgisTmyDto
from app.config.settings import get_settings

setts = get_settings()
defaultApiVersion = "v5_2"
router = APIRouter() 


@router.post(r"/{api_version}/pvgis_hourly")
def get_pvgis_hourly_route(api_version: str, dto: PvgisHourlyDto):
    return get_pvgis_hourly(api_version, dto)


@router.post(r"/{api_version}/pvgis_daily")
def get_pvgis_daily_route(api_version: str, dto: PvgisDailyDto):
    return get_pvgis_daily(api_version, dto)


@router.post(r"/{api_version}/pvgis_monthly")
def get_pvgis_monthly_route(api_version: str, dto: PvgisMonthlyDto):
    return get_pvgis_monthly(api_version, dto)


@router.post(r"/{api_version}/tmy")
def get_pvgis_tmy_route(api_version: str, dto: PvgisTmyDto):
    return get_pvgis_tmy(api_version, dto)


@router.post(r"/{api_version}/offgrid")
def get_pvgis_offgrid_route(api_version: str, dto: PvgisOffgridDto):
    return get_pvgis_offgrid(api_version, dto)


@router.post(r"/{api_version}/pvgis_grid_connected_or_tracking")
def get_pvgis_grid_connected_or_tracking_route(api_version: str, dto: PvgisGridConnectedOrTrackingDto):
    return get_pvgis_grid_connected_or_tracking(api_version, dto)


@router.post(r"/{api_version}/horizon_profile")
def get_horizon_profile_route(api_version: str, dto: PvgisHorizonProfileDto):
    return get_horizon_profile(api_version, dto)


@router.post(r"/{api_version}/degreedays")
def get_degreedays_route(api_version: str, dto: PvgisDegreedaysDto):
    return get_degreedays(api_version, dto)


@router.post(r"/{api_version}/extent")
def get_extent_route(api_version: str, dto: PvgisExtentDto):
    return get_extent_db(api_version, dto)




# needs to be removed when migrated
@router.post("/pvgis_hourly")
def get_pvgis_hourly_route( dto: PvgisHourlyDto):
    return get_pvgis_hourly(defaultApiVersion, dto)


@router.post("/pvgis_daily")
def get_pvgis_daily_route( dto: PvgisDailyDto):
    return get_pvgis_daily(defaultApiVersion, dto)


@router.post("/pvgis_monthly")
def get_pvgis_monthly_route( dto: PvgisMonthlyDto):
    return get_pvgis_monthly(defaultApiVersion, dto)


@router.post("/tmy")
def get_pvgis_tmy_route( dto: PvgisTmyDto):
    return get_pvgis_tmy(defaultApiVersion, dto)


@router.post("/offgrid")
def get_pvgis_offgrid_route( dto: PvgisOffgridDto):
    return get_pvgis_offgrid(defaultApiVersion, dto)


@router.post("/pvgis_grid_connected_or_tracking")
def get_pvgis_grid_connected_or_tracking_route( dto: PvgisGridConnectedOrTrackingDto):
    return get_pvgis_grid_connected_or_tracking(defaultApiVersion, dto)


@router.post("/horizon_profile")
def get_horizon_profile_route( dto: PvgisHorizonProfileDto):
    return get_horizon_profile(defaultApiVersion, dto)


@router.post("/degreedays")
def get_degreedays_route( dto: PvgisDegreedaysDto):
    return get_degreedays(defaultApiVersion, dto)


@router.post("/extent")
def get_extent_route( dto: PvgisExtentDto):
    return get_extent_db(defaultApiVersion, dto)


@router.get('/test-link')
def get_pvgis_api_url():
    link = setts.get_pvgis_api_url()
    return link