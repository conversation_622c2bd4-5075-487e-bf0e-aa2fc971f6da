from typing import Any
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.api.api_v2.deps import get_user
from app.config.db.database import get_session
from app.schemas.region import RegionCreate, RegionUpdate  # Importer les schémas RegionCreate et RegionUpdate
from app.crud.crud_region import region  # CRUD pour la gestion des régions
from app import schemas
from app import crud

router = APIRouter()

import ast

@router.get("")
def read_regions(
    db: Session = Depends(get_session),
    offset: int = 0,
    limit: int = 100,
    where: str = "",
    order_by: str = "id",
    order: str = "ASC",
    relations: str="[]",
    base_columns: str = "['created_at']",
    no_limit: bool = False,
) -> Any:
    """
    Lire les régions avec options de pagination et filtres.
    """
    wheres = []
    base_columns = ast.literal_eval(base_columns)
    relations = ast.literal_eval(relations)
    if where is not None and where != "" and where != []:
        wheres += ast.literal_eval(where)
    count = crud.region.get_count_where_array(
        db=db,
        where=wheres,
    )
    region_list = crud.region.get_multi_where_array_v2(
        db=db,
        where=wheres,
        order_by=order_by,
        order=order,
        limit=limit,
        skip=offset,
        relations=relations,
        base_columns=base_columns,
        no_limit=no_limit,
    )

    return {
        "count": count,
        "data": region_list,
    }


@router.post("", response_model=Any)
def create_region(
    *,
    db: Session = Depends(get_session),
    region_in: schemas.RegionCreate,  # Utilisation du schéma RegionCreate pour la création d'une région
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Créer une nouvelle région.
    """
    if current_user and "id" in current_user:
        last_user_id = current_user["id"]
    else:
        last_user_id = None 

    data = crud.region.create(db=db, obj_in=region_in, user_id=last_user_id)

    return data


@router.get("/{id}")
def read_region(
    *,
    db: Session = Depends(get_session),
    id: int,
) -> Any:
    """
    Lire une région spécifique par son ID.
    """
    data = crud.region.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="region_not_found")

    return data


@router.put("/{id}")
def update_region(
    *,
    db: Session = Depends(get_session),
    id: int,
    region_in: schemas.RegionUpdate,  # Utilisation du schéma RegionUpdate pour la mise à jour d'une région
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Mettre à jour une région existante.
    """
    data = crud.region.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="region_not_found")
    
    if current_user and "id" in current_user:
        last_user_id = current_user["id"]
    else:
        last_user_id = None 

    data = crud.region.update(db=db, db_obj=data, obj_in=region_in, user_id=last_user_id)

    return data


@router.delete("/{id}", response_model=Any)
def delete_region(
    *,
    db: Session = Depends(get_session),
    id: int,
    current_user: Any = Depends(get_user),
) -> Any:
    """
    Supprimer une région par son ID.
    """
    data = crud.region.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="region_not_found")
    
    if current_user and "id" in current_user:
        last_user_id = current_user["id"]
    else:
        last_user_id = None 

    return crud.region.soft_delete(db=db, id=id, user_id=last_user_id)
