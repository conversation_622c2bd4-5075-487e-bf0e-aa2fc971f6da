from typing import Any
from fastapi import APIRouter, Depends, HTTPException
from fastapi.encoders import jsonable_encoder
from sqlalchemy.orm import Session

from app.api.api_v2.deps import get_user, get_current_language
from app.config.db.database import get_session
from app.schemas.subscription_user import SubscriptionUserCreate, SubscriptionUserUpdate

from app import crud
from app.utils.utils import to_dict
from app import schemas
import requests
from app.config.settings import get_settings
from app.services.notification.notification import send_notif

router = APIRouter()
settings = get_settings()

import ast


@router.get("")
def read_subscription_users(
        db: Session = Depends(get_session),
        offset: int = 0,
        limit: int = 100,
        where: str = "",
        order_by: str = "id",
        order: str = "ASC",
        relations: str = "[]",
        base_columns: str = "['created_at']"
) -> Any:
    wheres = []
    base_columns = ast.literal_eval(base_columns)
    relations = ast.literal_eval(relations)
    if where is not None and where != "" and where != []:
        wheres += ast.literal_eval(where)
    # count = crud.subscription_user.get_count_where_array(
    #     db=db,
    #     where=wheres,
    # )
    subscription_user_list = crud.subscription_user.get_multi_where_array_v2(
        db=db,
        where=wheres,
        order_by=order_by,
        order=order,
        limit=limit,
        skip=offset,
        relations=relations,
        base_columns=base_columns,
    )

    return {
        "count": len(subscription_user_list),
        "data": subscription_user_list,
    }


@router.post("")
def create_subscription_user(
        *,
        db: Session = Depends(get_session),
        user_in: schemas.SubscriptionUserCreate,
        current_user: Any = Depends(get_user),
) -> Any:
    """
    Create new subscription_user.
    """
    crud.subscription_user.get_customer_by_id(db=db, id=user_in.user_id)
    crud.subscription_user.get_subscription_by_id_with_number_of_simulation(db=db, id=user_in.subscription_id)

    return crud.subscription_user.create(db=db, obj_in=user_in)


@router.post("/copy_user_to_new_subscription/{customer_id}")
def create_subscription_user(
        *,
        db: Session = Depends(get_session),
        customer_id: int,
) -> Any:
    """
    Copy old user from old subscription to new subscription.
    """
    # get last subscription with user
    customer_auth_user_id = 1
    new_subscription_id = 1
    old_subscription_id = 1
    return crud.subscription_user.copy_user_to_new_subscription(db=db,
                                                                customer_auth_user_id=customer_auth_user_id,
                                                                new_subscription_id=new_subscription_id,
                                                                old_subscription_id=old_subscription_id)


@router.get("/{id}")
def read_subscription_user(
        *,
        db: Session = Depends(get_session),
        id: int,
        current_user: Any = Depends(get_user),
) -> Any:
    """
    Get subscription_user by ID.
    """
    data = crud.subscription_user.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="Product not found")

    return data


@router.get("/by_subscription_id/{subscription_id}")
def read_subscription_user(
        *,
        db: Session = Depends(get_session),
        subscription_id: int,
        current_user: Any = Depends(get_user),
) -> Any:
    """
    Get subscription_user by ID.
    """
    data = crud.subscription_user.get_multi_where_array(db=db, where=[
        {"key": "subscription_id", "operator": "==", "value": subscription_id}])

    return data


@router.put("/{id}", response_model=SubscriptionUserUpdate)
def update_subscription_user(
        *,
        db: Session = Depends(get_session),
        id: int,
        product_in: schemas.SubscriptionUserUpdate,
        current_user: Any = Depends(get_user),
        lang: str = Depends(get_current_language)
) -> Any:
    subscription_id = crud.subscription_user.get(db=db, id=id).subscription_id
    customer = crud.subscription.get(db=db, id=subscription_id, relations=["customer"]).customer
    customer_email = customer.email
    customer_user_id = customer.auth_user_id
    """
    Update an subscription_user.
    """
    data = crud.subscription_user.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="Issue not found")

    ban_vars = {
        "customer_email": customer_email,
        "banned_user": data.email,
        "email": data.email,
    }
    if product_in.is_active == False:
        crud.subscription_user.send_handle_active_user_mail("baned", customer_user_id, data.auth_user_id, ban_vars,
                                                            lang)
    elif product_in.is_active == True:
        crud.subscription_user.send_handle_active_user_mail("active", customer_user_id, data.auth_user_id, ban_vars,
                                                            lang)
    data = crud.subscription_user.update(db=db, db_obj=data, obj_in=product_in)

    return to_dict(data)


@router.put("/reset_password/{id}", response_model=SubscriptionUserUpdate)
def reset_password(
        *,
        db: Session = Depends(get_session),
        id: int,
        ob_in: schemas.SubscriptionUserResetPassword,
        current_user: Any = Depends(get_user),
) -> Any:
    """
    Reset password of an subscription_user.
    """
    data = crud.subscription_user.get(db=db, id=id)
    subscription_id = crud.subscription_user.get(db=db, id=id).subscription_id
    customer = crud.subscription.get(db=db, id=subscription_id, relations=["customer"]).customer
    customer_user_id = customer.auth_user_id
    if not data:
        raise HTTPException(status_code=404, detail="SubscriptionUser not found")

    auth_user_id = data.auth_user_id
    auth_payload = {
        "password": ob_in.password
    }
    auth_res = requests.patch(f"{settings.AUTH_API_URL}/user/reset-pwd/{auth_user_id}", json=auth_payload,
                              headers={'Notifinfo': '{}'})
    auth_res_json = auth_res.json()
    if auth_res.status_code == 200:
        vars = {
            "username": data.email,
            "email": data.email,
            "password": ob_in.password
        }
        send_subscription_reset_password_mail(customer_user_id, auth_user_id, vars)

    return auth_res_json


@router.delete("/{id}", response_model=Any)
def delete_subscription_user(
        *,
        db: Session = Depends(get_session),
        id: int,
        current_user: Any = Depends(get_user),
) -> Any:
    """
    Delete an subscription_user.
    """

    data = crud.subscription_user.get(db=db, id=id)

    if not data:
        raise HTTPException(status_code=404, detail="Product not found")

    return to_dict(crud.subscription_user.soft_delete(db=db, id=id))


@router.post("/create_or_update/{subscription_id}")
def create_subscription_user(
        *,
        db: Session = Depends(get_session),
        su: schemas.SubscriptionUserCreateOrUpdate,
        current_user: Any = Depends(get_user),
        subscription_id: int
) -> Any:
    """
    Create new subscription_user.
    """
    users_already_exists = []
    users_created = []
    subscription = crud.subscription.get(db=db, id=subscription_id, relations=["customer"])
    customer = subscription.customer
    su_in = {"email": su.email, "subscription_id": subscription_id}
    su_in_customer = crud.customer.get_first_where_array(db=db,
                                                         where=[{"key": "email", "operator": "==", "value": su.email}])
    su_with_email = crud.subscription_user.get_first_where_array(db=db, where=[
        {"key": "email", "operator": "==", "value": su.email}])
    su_with_subscription_id = crud.subscription_user.get_first_where_array(db=db, where=[
        {"key": "email", "operator": "==", "value": su.email},
        {"key": "subscription_id", "operator": "==", "value": subscription_id}])
    found_su = su_with_subscription_id
    if su_in_customer:
        users_already_exists.append({"email": su.email})
        raise HTTPException(status_code=409, detail={"message": "pvgis.subscription_users.user_already_exist",
                                                     "data": users_already_exists})

    # user exist in other subscription
    if su_with_email and not su_with_subscription_id:
        users_already_exists.append({"email": su.email})
        raise HTTPException(status_code=409, detail={"message": "pvgis.subscription_users.user_already_exist",
                                                     "data": users_already_exists})

    elif su_with_subscription_id:
        crud.subscription_user.update(db, db_obj=found_su, obj_in=su_in)
        # reset password must be set here
    elif (not su_with_email and not su_with_subscription_id):
        auth_payload = {
            "firstname": su.email,
            "lastname": su.email,
            "username": su.email,
            "pseudo": su.email,
            "password": su.password,
            "appId": 1,
            "roleId": 2
        }
        auth_res = requests.post(f"{settings.AUTH_API_URL}/user/full", json=auth_payload, headers={'Notifinfo': '{}'})
        auth_res_json = auth_res.json()
        if auth_res.status_code == 201:
            auth_user_id = auth_res_json['userCreated']['id']
            su_in['auth_user_id'] = auth_user_id
            su_in['is_active'] = True
            su_created = crud.subscription_user.create(db=db, obj_in=su_in)
            vars = {
                "username": su.email,
                "email": su.email,
                "password": su.password
            }
            send_subscription_user_created_mail(customer.auth_user_id, auth_user_id, vars)

        elif auth_res_json["statusCode"] == 400 and auth_res_json["message"] == "user.email_or_pseudo_already_exist":
            
            users_already_exists.append({"email": su.email})

    if (len(users_already_exists) > 0):
        raise HTTPException(status_code=409, detail={"message": "pvgis.subscription_users.user_already_exist",
                                                     "data": users_already_exists})
    return {"message": "subscription user insert successful"}


def send_subscription_user_created_mail(customer_user_id, user_id, vars, lang: str = 'en'):
    users = [
        {
            "id": customer_user_id,
            "roleId": 2,
            "appKey": settings.APP_KEY,
            "vars": vars
        },
        {
            "id": user_id,
            "roleId": 2,
            "appKey": settings.APP_KEY,
            "vars": vars
        }
    ]
    send_notif(event_code="subscription-user-credential-created", users=users, mailLanguage=lang)


def send_subscription_reset_password_mail(customer_user_id, user_id, vars, lang: str = 'en'):
    users = [
        {
            "id": customer_user_id,
            "roleId": 2,
            "appKey": settings.APP_KEY,
            "vars": vars
        },
        {
            "id": user_id,
            "roleId": 2,
            "appKey": settings.APP_KEY,
            "vars": vars
        }
    ]
    send_notif(event_code="subscription-user-reset-password", users=users, mailLanguage=lang)
