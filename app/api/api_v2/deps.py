from typing import Annotated, Any
from app.enums.subscription_status_enum import SubscriptionStatusEnum
from fastapi import Depends, HTTPException
from fastapi.security import OAuth2PasswordBearer
from sqlalchemy.orm import Session

from sqlalchemy.orm.exc import NoResultFound
from fastapi import Header, HTTPException

from dotenv import load_dotenv
import os

import requests
import jwt
from urllib.parse import unquote
import json
from typing import Annotated
from fastapi.encoders import jsonable_encoder

from fastapi import Cookie, FastAPI, Request

from app.config.db.database import get_session
from app.config.settings import get_settings
from app.models.customer import Customer
from app import crud
from typing import Optional
load_dotenv()
AUTH_API_URL = os.getenv("AUTH_API_URL")
APP_KEY = os.getenv("APP_KEY")
PVGIS_UI_URL = os.getenv("PVGIS_UI_URL")

settings = get_settings()

reusable_oauth2 = OAuth2PasswordBearer(tokenUrl="auth/login/access-token")


def get_user(
    db: Session = Depends(get_session), token: Any = Depends(reusable_oauth2)
) -> Any:
    response = requests.post(
        f"{settings.AUTH_API_URL}/user/check-token", json={"token": token}
    )

    if response.status_code == 202:
        return decode_token(token)
    else:
        raise HTTPException(status_code=401, detail="Invalid token")
def get_optional_user(
    db: Session = Depends(get_session),  authorization: str = Header(None)
) -> Any:
    if authorization is None or not authorization.startswith("Bearer "):
            return None  # Handle missing or improperly formatted token
    token = authorization.split(" ")[1]

    response = requests.post(
        f"{settings.AUTH_API_URL}/user/check-token", json={"token": token}
    )

    if response.status_code == 202:
        return decode_token(token)
    else:
        raise HTTPException(status_code=401, detail="Invalid token")

def get_optional_customer(
    db: Session = Depends(get_session),  current_user=Depends(get_optional_user)
) -> Any:
    if current_user is None:
        return None

    connected_user_id = current_user.get("connectedUserId", current_user["id"])
    is_different_user = connected_user_id != current_user["id"]
    is_admin = ("isAdmin" in current_user) and (current_user["isAdmin"] == True)
    
    if is_different_user and not is_admin:
        return None
    customer=crud.customer.get_by_user_id(db, current_user["id"])
    if customer is None :
        subscription_conditions = [
            {"key": "auth_user_id", "value": current_user["id"], "operator": "=="},
            {"key": "is_active", "value": 1, "operator": "=="},
            # why we will need check disabled is null of subscription ?
            # {"key": "subscription.disabled_at", "operator":"isNull"},
            {"key": "subscription.deleted_at", "operator":"isNull"},
            {"key": "subscription.subscription_status", "value": SubscriptionStatusEnum.ACTIVE, "operator": "=="}
        ]
        subscription_user = crud.subscription_user.get_first_where_array_v2(
            db=db, 
            where=subscription_conditions,
            relations=["subscription.customer"],
            base_columns=["id"]
        )
        return subscription_user.subscription.customer
    if customer is None:
        raise HTTPException(status_code=401, detail="customer not found")
    return customer
    




def get_token(token: Any = Depends(reusable_oauth2)) -> Any:
    response = requests.post(
        f"{settings.AUTH_API_URL}/user/check-token", json={"token": token}
    )

    if response.status_code == 202:
        return token
    else:
        raise HTTPException(status_code=403, detail="Token Expired, please login!")


def decode_token(token: str):
    return jwt.decode(token, options={"verify_signature": False})


def get_current_language(db: Session = Depends(get_session),  current_user=Depends(get_optional_user)) -> Any:
    if current_user is None:
       
        return
    customer=crud.customer.get_by_user_id(db,current_user["id"])
     
    if customer:
        if jsonable_encoder(customer)["settings_json"]:
            language = jsonable_encoder(customer)["settings_json"]["language"]
        else:
            language = 'en'
        return language or 'en'
    else:
        return 'en'
