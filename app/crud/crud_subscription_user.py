from fastapi import HTT<PERSON>Exception
from fastapi.encoders import jsonable_encoder
from app.crud.base import CRUDBase
from app.models.subscription import Subscription
from app.models.subscription_user import SubscriptionUser
from sqlalchemy.orm import Session
from app.models.customer import Customer
from app.schemas.subscription_user import SubscriptionUserCreate, SubscriptionUserUpdate
from app import crud
from app.services.notification.notification import send_notif
from app.config.settings import get_settings
settings = get_settings()
from fastapi import Depends


class CRUDSubscriptionUser(
    CRUDBase[SubscriptionUser, SubscriptionUserCreate, SubscriptionUserUpdate]
):
    def get_customer_by_id(self, db: Session, id: int):
        try:
            return db.query(Customer.id).filter(Customer.id == id).one()
        except Exception as e:
            raise HTTPException(
                status_code=404,
                detail="User not found when creating a subscription user",
            )
            
    def create_subscription_user(self, db:Session, subscription_user_in: SubscriptionUserCreate, commit: bool = True):
        db_obj_subscription_user = SubscriptionUser(
            first_name = subscription_user_in.first_name,
            last_name = subscription_user_in.last_name,
            is_active = subscription_user_in.is_active,
            auth_user_id = subscription_user_in.auth_user_id,
            email = subscription_user_in.email,
            pseudo = subscription_user_in.pseudo,
            subscription_id = subscription_user_in.subscription_id
        )
        db.add(db_obj_subscription_user)
        if(commit): 
            db.commit()
        else:
            db.flush()
        return db_obj_subscription_user
    
    def copy_user_to_new_subscription(self, db:Session, customer_auth_user_id: int, new_subscription_id: int, old_subscription_id, commit: bool = True):
        result = []
        old_subscription_user = crud.subscription_user.get_multi_where_array(
            db=db,
            where=[{"key": "subscription_id","operator": "==","value": old_subscription_id}, 
                {"key": "is_active","operator": "==","value": True}, 
                {"key": "auth_user_id","operator": "!=","value": customer_auth_user_id}
                ], 
        )
        for old_user in old_subscription_user: 
            old_user.subscription_user=new_subscription_id
            result.append(jsonable_encoder(self.add_subscription_user(db=db, subscription_user_in=old_user)))
            if(commit): 
                db.commit()
            else: 
                db.flush()
        return result
    
    def send_handle_active_user_mail(self, active, customer_user_id, subscription_user_id, ban_vars, lang: str = 'en'):
        notification_data = [
            {
                "id": customer_user_id,
                "roleId": 2,
                "appKey": settings.APP_KEY,
                "vars": ban_vars
            },
            {
                "id": subscription_user_id,
                "roleId": 2,
                "appKey": settings.APP_KEY,
                "vars": ban_vars
            }
        ]
        event_code = "subscription-user-banned-user" if active == "baned" else "subscription-user-activation"
        
        send_notif(event_code=event_code, users=notification_data, mailLanguage=lang)


subscription_user = CRUDSubscriptionUser(SubscriptionUser)
