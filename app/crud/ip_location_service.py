import os
import traceback
import requests
from dotenv import load_dotenv, find_dotenv
from sqlalchemy.orm import Session
from app import crud
from app.models.country import Country
from sqlalchemy.sql import text

load_dotenv(find_dotenv())

ip_location_url = os.environ.get("IP_LOCATION_URL")
google_geocode_url = os.environ.get("GOOGLE_GEOCODE_URL")
google_api_key = os.environ.get("GOOGLE_MAPS_API_KEY")

def get_location_from_ip(ip: str):
    res = requests.get(ip_location_url + "/" + ip)
    if not res.ok:
        try:
            err_msg = res.json()
        except Exception as e:
            traceback.print_exc()
            res.raise_for_status()
        else:
            raise requests.HTTPError(err_msg["message"])
    return res.json()

def google_geocode(city_name: str, country_name: str=None): 
    geocode_address = city_name if not country_name else f"{city_name}, {country_name}"
    params = {
        'address': geocode_address,
        'key': google_api_key
    }
    res = requests.get(google_geocode_url, params=params)
    if not res.ok:
        try:
            err_msg = res.json()
        except Exception as e:
            traceback.print_exc()
            res.raise_for_status()
        else:
            raise requests.HTTPError(err_msg["message"])
    return res.json()

def google_geocode_by_loc(lat: float, lng: float):
    params = {
        'latlng': f"{lat},{lng}",
        'key': google_api_key
    }
    res = requests.get(google_geocode_url, params=params)
    if not res.ok:
        try:
            err_msg = res.json()
        except Exception as e:
            traceback.print_exc()
            res.raise_for_status()
        else:
            raise requests.HTTPError(err_msg["message"])

    results = res.json().get('results', [])
    for component in results:
        for address_component in component.get('address_components', []):
            if 'country' in address_component.get('types', []):
                return address_component.get('short_name')
    return None
    
def get_google_place_ids_by_city_ip(db: Session, ip: str):
    ip_location_data = get_location_from_ip(ip)
    city_name = ip_location_data["city"]
    country_code = ip_location_data["countryCode"]
    country = db.query(Country).filter(text("LOWER(code_alpha_2) = :value")).params(value=country_code).first() 
    geocode_res = google_geocode(city_name, getattr(country, 'name', None))
    google_place_ids = (p["place_id"] for p in geocode_res["results"])
    return google_place_ids