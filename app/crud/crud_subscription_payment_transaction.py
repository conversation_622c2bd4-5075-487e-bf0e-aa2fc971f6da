from app.models.subscription import Subscription
from app.utils.utils import send_email
from fastapi import HTTPException
from fastapi.encoders import jsonable_encoder
from app.crud.base import CRUDBase
from sqlalchemy.orm import Session
from sqlalchemy import desc, func, text
from app.models.payment_method import PaymentMethod
from app.models.subscription_payment_transaction import SubscriptionPaymentTransaction
from app.schemas.subscription_payment_transaction import (
    SubscriptionPaymentTransactionCreate,
    SubscriptionPaymentTransactionUpdate,
)
from app import crud
from dateutil.relativedelta import relativedelta
from datetime import datetime
from app.enums.payment_status_enum import PaymentStatusEnum

from app.config.settings import get_settings
settings = get_settings()
class CRUDSubscriptionPaymentTransaction(
    CRUDBase[
        SubscriptionPaymentTransaction,
        SubscriptionPaymentTransactionCreate,
        SubscriptionPaymentTransactionUpdate,
    ]
):

    def get_payment_method_by_id(self, db: Session, id: int):
        try:
            return db.query(PaymentMethod.id).filter(PaymentMethod.id == id).one()
        except Exception as e:
            raise HTTPException(
                status_code=404,
                detail="Subscription not found when creating a subscription user",
            )
            
    def create_subscription_payment_transaction(self,db:Session,spt_in:SubscriptionPaymentTransactionCreate,
                                                customer_id,
                                                user_id):
           
         
        created_subscription_payment_transaction = crud.subscription_payment_transaction.create(db=db, obj_in=spt_in, user_id=user_id,  commit=False, flush=True)
        
        
        if spt_in.cart_id:
            cart_db=crud.cart.get(db=db,id=spt_in.cart_id)
            # TODO change get_allowed_subscription_product_by_id to get_product_details after payment
            product_json=crud.product.get_product_details_or_throw(db=db, product_id=cart_db.product_id)
            crud.cart.update(db=db,db_obj=cart_db,obj_in={"converted_at":func.now(),"product_json":jsonable_encoder(product_json)},
                               commit=False, flush=True)
            customer_db=crud.customer.get(db=db,id=customer_id)
            crud.customer.update(db=db,db_obj=customer_db,obj_in={"cart_reference":None},
                                   commit=False, flush=True)
            
            
        return created_subscription_payment_transaction
    
    
    def send_email_after_subscription_payment(self,product_name,purchase_amount,purchase_currency):
        amount_value=f"{purchase_amount}  {purchase_currency}"
        email_param = {
        "templateVars": {"product_name": product_name, "purchase_amount": amount_value},
        "mailVars": {
            "from": settings.SMPT_USER_MAIL,
            "to": settings.BILLING_EMAIL,
        },
        "sentByApp": "app",
        "sentByProcess": "Form submission",
        "sentToUser": "",
        "type": "MAIL",
        }
        lang = "en"
        template_name = "pvgis-com%2F" + "new-purchase"
        send_email(email_param, lang, template_name)
    
    # def get_checkout_status(self,checkout_status:str):
    #     if checkout_status=="complete":
    #         return PaymentStatusEnum.PENDING
    #     elif checkout_status=="requires_payment_method":
    #         return PaymentStatusEnum.PENDING
    #     else:
    #         return PaymentStatusEnum.FAILURE
        
    def get_stripe_amount_real_value(self,amount_in:float,currency:str):
        if currency=='jpy':
            return amount_in
        else:
            return amount_in/100
        
    def fix_transaction_subs_relations(self, db: Session):
         
        db.execute(text("""
                   UPDATE 
                   subscription_payment_transaction spt
                   join subscription s on s.subscription_stripe_id = spt.subscription_stripe_id
                   set spt.subscription_id = s.id 
                   where spt.subscription_id is null
                """))
        db.execute(text("""
                   UPDATE 
                   subscription_payment_receipt receipt
                   join subscription_payment_transaction invoice on invoice.stripe_object_id = receipt.stripe_object_id
                   set receipt.subscription_payment_transaction_id = invoice.id 
                   where receipt.subscription_payment_transaction_id is null
                """))
        db.commit()
    
    def fill_stripe_ob_id(self, db: Session, batch_size = 100):
        # Fill stripe_object_id in subscription_payment_transaction table
        # Select the subscription_payment_transaction records where stripe_object_id is null, extract the id from payment_transaction_json, and update the stripe_object_id field with the extracted id
        
        try:
            while True:
            # Get a batch of records where stripe_object_id is null but payment_transaction_json exists
                transactions = db.query(SubscriptionPaymentTransaction).filter(
                    SubscriptionPaymentTransaction.stripe_object_id.is_(None),
                    SubscriptionPaymentTransaction.payment_transaction_json.isnot(None)
                ).order_by(desc(SubscriptionPaymentTransaction.created_at)).limit(batch_size).all()
                
                if not transactions or len(transactions) == 0:
                    break  # No more records to process
                    
                updated_count = 0
                for transaction in transactions:
                    try:
                        stripe_object_id = transaction.payment_transaction_json.get('id', None)
                        if stripe_object_id:
                            transaction.stripe_object_id = transaction.payment_transaction_json['id']
                            updated_count += 1
                    except Exception as e:
                        print(f"Error processing transaction {transaction.id}: {e}")
                        
                if updated_count > 0:
                    db.commit()
                    print(f"Updated {updated_count} records with stripe_object_id")
                else:
                    break  # No updates in this batch, likely no more valid records
        except Exception as e:
            print(f"Error processing transactions: {e}")
            db.rollback()
            raise e

    
        
    
subscription_payment_transaction = CRUDSubscriptionPaymentTransaction(
    SubscriptionPaymentTransaction
)
