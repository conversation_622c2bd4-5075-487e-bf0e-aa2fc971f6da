  
import json
from typing import Any, Optional
from datetime import datetime, timedelta
import copy
 
from app.crud.simulation_activity_service import generate_random_string
from app.enums.billing_period_interval_enum import BillingPeriodIntervalEnum
from app.enums.subscription_action_type_enum import SubscriptionActionTypeEnum
from app.models.customer import Customer
from app.models.product import Product 
from app.schemas.subscription_payment_receipt import SubscriptionPaymentReceiptCreate
from app.services.notification.notification import send_notif
from app.services.stripe import stripe_service
from fastapi import HTTPException
from fastapi.encoders import jsonable_encoder
from sqlalchemy import Date, and_, cast, func, not_, or_, distinct
from app.crud.base import CRUDBase
from app.models.subscription import Subscription
from app.models.subscription_user import SubscriptionUser
from sqlalchemy.orm import Session
from app.enums.subscription_status_enum import SubscriptionStatusEnum
from app import crud
from app.crud.pvgis_service import generatePaymentReference
from app.schemas.subscription import SubscriptionCreate, SubscriptionUpdate
from app.schemas.subscription_payment_transaction import SubscriptionPaymentTransactionCreate
from app.enums.payment_status_enum import PaymentStatusEnum
from app.enums.receipt_status_enum import ReceiptStatusEnum
import stripe
import time
from dateutil.relativedelta import relativedelta
from app.config.settings import get_settings
import requests
from dateutil.relativedelta import relativedelta
from app.utils.utils import get_from_emb_dict, get_from_emb_dict_or_throw, send_email, send_email_cms
settings = get_settings()
class CRUDSubcription(CRUDBase[Subscription, SubscriptionCreate, SubscriptionUpdate]):

    # Not allowed user to do downgrade
    # Upgrade
    # price = new_price - old_price
    # credit = new_credit - (old_product_monthly_credit - old_credit_balance)
    # required product_id, customer_id, credit_balance 
    
    # Called for product creation, upgrade or downgrade, additional credits (not called on automatic renewal)
   
    def change_subscription(self,
                             db:Session,
                             change_to_product: Product, 
                             customer_id:int,
                              
                             active_sub: Subscription = None,
                             #State of subscription before update 
                             subscription_stripe_ob=None,
                            
                             ):
        
        # TODO change get_allowed_subscription_product_by_id to get_product_details after payment
        product_json=jsonable_encoder(change_to_product)
        new_balance=self.get_new_balance(
            db=db,
            active_subscription=active_sub,
            new_product=change_to_product,
        )
        if not active_sub:
            active_sub= crud.subscription.get_first_where_array(
                db=db,
                where=[{"key": "customer_id","operator": "==","value": customer_id}, 
                    {"key": "subscription_status","operator": "==","value": SubscriptionStatusEnum.ACTIVE},
                    {"key":"is_auto_renew","operator":"==","value":True},
                    {"key":"product_json","operator":"isNotNull"}
                ]
            )
        current_period_start = datetime.fromtimestamp(subscription_stripe_ob['current_period_start'])
        
        #Current period start is set to now if past due
        # set_start_date_to_now= subscription_stripe_ob['status'] == "past_due"
        # if set_start_date_to_now:
        #     current_period_start=datetime.now()
            
        obj_in={
            "credit_balance":new_balance,
            "product_json":product_json,
            "product_id":change_to_product.id,
            "subscription_stripe_id": subscription_stripe_ob['id'],
            "expired_date":Subscription.calculate_expired_date(
                current_period_start, 
                BillingPeriodIntervalEnum(change_to_product.billing_period_interval), 
                ),
            # **({"start_date":current_period_start} if set_start_date_to_now else {})
            }
        updated_obj=crud.subscription.update(db=db, db_obj=active_sub, obj_in=obj_in, commit=False, flush=True)
        return updated_obj
    
    def add_additional_credit(self,db:Session,product_id : int, current_active_sub: Subscription):
        product=crud.product.get(db=db,id=product_id)
        additional_credit=product.monthly_credit
         
        new_credit_balance=current_active_sub.credit_balance+additional_credit
        additional_credit_balance=(current_active_sub.additional_credit_balance or 0)+additional_credit
        # new_credit=(db_obj.additional_credit_balance or 0)+additional_credit
        obj_in={"credit_balance":new_credit_balance,"additional_credit_balance":additional_credit_balance}
        updated_obj=crud.subscription.update(db=db, db_obj=current_active_sub, obj_in=obj_in, commit=False, flush=True)
        return updated_obj
    
    def renew_subscription(self,db:Session,subscription_ob,subscription_period):
        additional_credit=subscription_ob.additional_credit_balance
        subscription_credit_balance=subscription_ob.credit_balance
        # Use product_json in subscription
        # product=crud.product.get(db=db,id=subscription_ob.product_id)
        product=subscription_ob.product_json
        new_credit= Product.get_credit_from_json(product)
        
        if additional_credit:
            if subscription_credit_balance>additional_credit:
                subscription_credit_balance=additional_credit
            new_credit=new_credit+subscription_credit_balance
        else: 
            subscription_credit_balance=0
        obj_in={
            "start_date":subscription_period['start_date'],
            "expired_date": subscription_period['end_date'],
            "credit_balance":new_credit,
            "additional_credit_balance": subscription_credit_balance,
        }
        updated_obj=crud.subscription.update(db=db, db_obj=subscription_ob, obj_in=obj_in,commit=False, flush=True)
        return updated_obj
    
    # returns 0 if negative
    def get_new_balance(self,db:Session,new_product: Product,active_subscription:Subscription):
        # Get required numbers
        new_product_credit=new_product.get_credit()
        old_product_credit= Product.get_credit_from_json(active_subscription.product_json) 
        if Product.get_price_from_json(active_subscription.product_json)==0:
            old_credit_balance=1 
        current_balance = active_subscription.credit_balance
        
         # Calculation
        consumed_credit = old_product_credit - current_balance
        new_balance = new_product_credit - consumed_credit 
          
        return new_balance if new_balance>0 else 0
    
    def create_subscription_after_payment(self,db:Session,
                                          product_to_buy: Product,
                                          active_subscription: Subscription,checkout_json:json,
                                          subscription_ob,
                                          customer_id:int,
                                          coupon_end_date,
                                          cart, conditionBtwOffer=False, sceneBtwOffer=[]):
        subscription_action_type = SubscriptionActionTypeEnum(checkout_json['metadata']['subscriptionActionType'])
       
        
        accounting = self.get_accounting_from_stripe_ob(checkout_json)
        product_id = product_to_buy.id
        subscription_stripe_id=subscription_ob['id']
        checkout_json = checkout_json
        
        # Get stripe customer id
        stripe_customer_id=None
        if "customer" in checkout_json:
            stripe_customer_id = checkout_json["customer"]
        
        # Get  customer instance    
        customer=None
        if(customer_id):
            customer=crud.customer.get(db=db,id=customer_id)
        else:
            customer=crud.customer.get_first_where_array(db=db,where=[{'key':'stripe_customer_id',"value":stripe_customer_id,"operator":"=="}])
       
        
        user_id = customer.auth_user_id
        
        # Save customer_stripe_id if we have one  
        if(stripe_customer_id is not None):
            crud.customer.update(db=db,db_obj=customer,obj_in={"stripe_customer_id":stripe_customer_id}, commit=False, flush=True)
        
        # print("-----------------------------------------------")
        # print("Status Btw Offer : ", conditionBtwOffer)
        # print("Type : ", subscription_action_type)
        # print("sceneBtwOffer : ", sceneBtwOffer)
        # print("-----------------------------------------------")
      
     
        # Get current subscription
        
        created_subscription=None
        created_refund_invoice = None
        
        refund_amount = float(checkout_json['metadata'].get('refundAmount',0))
        
        # handle additional purshase
        if subscription_action_type == SubscriptionActionTypeEnum.ADDITIONAL:
            if not active_subscription:
                raise HTTPException(status_code=401, detail="No active subscription found")
             
            created_subscription=self.add_additional_credit(db=db,product_id=product_id, current_active_sub=active_subscription)
            
       
        if conditionBtwOffer:
            # Change offer particular to pro or pro to particular
            # [0] : init
            # [1] : destination
            # 1 : pro and 2 : individual
            
             # Upgrade / Downgrade from pro to individual
            #  from pro to individual (no refund, stay on pro services until end of subscription, then move to individual)
            if (sceneBtwOffer[0] == '1' and sceneBtwOffer[1] == '2' and subscription_action_type in [SubscriptionActionTypeEnum.UPGRADE, SubscriptionActionTypeEnum.DOWNGRADE, SubscriptionActionTypeEnum.SWITCH]):
                if not active_subscription:
                    raise HTTPException(status_code=401, detail="No active subscription found")
                # created_subscription=self.change_subscription(
                #     db=db,
                #     change_to_product=product_to_buy,
                #     subscription_stripe_ob=subscription_ob,
                #     customer_id = customer_id,
                #     active_sub=active_subscription
                # ) 
                subscription_in=SubscriptionCreate(
                    product_id= product_id,
                    subscription_stripe_id=subscription_stripe_id,
                    start_date= datetime.fromtimestamp(subscription_ob['current_period_start']),
                    expired_date=datetime.fromtimestamp(subscription_ob['current_period_end']) + relativedelta(years=1),
                    customer_id= customer.id,
                    coupon_end=coupon_end_date,
                    product_json=jsonable_encoder(product_to_buy),
                    subscription_status = SubscriptionStatusEnum.ACTIVE,
                    credit_balance= product_to_buy.get_credit(),
                    created_by = user_id
                ) 
                crud.subscription.update(db=db, db_obj=active_subscription, obj_in={
                            "disabled_at": datetime.now(),
                            "is_auto_renew": False
                }, commit=False, flush=True)
                subscription_in.start_date = active_subscription.expired_date
                created_subscription = crud.subscription.create(db=db, obj_in=subscription_in, user_id=user_id, commit=False, flush=True)
            
            # Upgrade / Downgrade from individual to pro
            # from individual to pro (take payment, shift to pro services straight away)
            elif (sceneBtwOffer[0] == '2' and sceneBtwOffer[1] == '1' and subscription_action_type in [SubscriptionActionTypeEnum.UPGRADE, SubscriptionActionTypeEnum.DOWNGRADE, SubscriptionActionTypeEnum.SWITCH]):
                if not active_subscription:
                    raise HTTPException(status_code=401, detail="No active subscription found")
                
                # if (refund_amount > 0):
                #     created_refund_invoice = crud.subscription.save_invoice(db=db,
                #         session_stripe_ob=checkout_json,
                #         customer=customer,
                #         subscription= active_subscription,
                #         subscription_stripe_id = subscription_stripe_id,
                #         cart_id = cart.id if cart else None,
                #         total_amount = float(accounting['total'])
                #         )
                created_subscription=self.change_subscription(
                    db=db,
                    change_to_product=product_to_buy,
                    subscription_stripe_ob=subscription_ob,
                    customer_id = customer_id,
                    active_sub=active_subscription
                ) 

            else:
                raise HTTPException(status_code=401, detail="Unknown subscription action type")
            
        else:
            # handle upgrade or downgrade
            if (subscription_action_type in [
                SubscriptionActionTypeEnum.UPGRADE,
                SubscriptionActionTypeEnum.SWITCH
                ]
                or (
                    subscription_action_type == SubscriptionActionTypeEnum.DOWNGRADE 
                    and refund_amount > 0
                    )
                ):
                
                if not active_subscription:
                    raise HTTPException(status_code=401, detail="No active subscription found")
                    
                if (
                    subscription_action_type == SubscriptionActionTypeEnum.DOWNGRADE 
                    and refund_amount > 0
                    ):
                    created_refund_invoice = crud.subscription.save_invoice(db=db,
                            session_stripe_ob=checkout_json,
                            customer=customer,
                            subscription= active_subscription,
                            subscription_stripe_id = subscription_stripe_id,
                            cart_id = cart.id if cart else None,
                            total_amount = float(accounting['total'])
                            )
                created_subscription=self.change_subscription(
                    db=db,
                    change_to_product=product_to_buy,
                    subscription_stripe_ob=subscription_ob,
                    customer_id = customer_id,
                    active_sub=active_subscription
                )  

            elif (subscription_action_type in [SubscriptionActionTypeEnum.CREATION]
                or (
                    subscription_action_type == SubscriptionActionTypeEnum.DOWNGRADE and refund_amount == 0
                    
                    )
                ): 
                subscription_in=SubscriptionCreate(
                    product_id= product_id,
                    subscription_stripe_id=subscription_stripe_id,
                    start_date= datetime.fromtimestamp(subscription_ob['current_period_start']),
                    expired_date=datetime.fromtimestamp(subscription_ob['current_period_end']),
                    customer_id= customer.id,
                    coupon_end=coupon_end_date,
                    product_json=jsonable_encoder(product_to_buy),
                    subscription_status = SubscriptionStatusEnum.ACTIVE,
                    credit_balance= product_to_buy.get_credit(),
                    created_by = user_id
                ) 
                if  subscription_action_type == SubscriptionActionTypeEnum.DOWNGRADE:
                    crud.subscription.update(db=db, db_obj=active_subscription, obj_in={
                                "disabled_at": datetime.now(),
                                "is_auto_renew": False
                    }, commit=False, flush=True)
                    subscription_in.start_date = active_subscription.expired_date
                    
                
                
                created_subscription = crud.subscription.create(db=db, obj_in=subscription_in, user_id=user_id, commit=False, flush=True)
            
            else:
                raise HTTPException(status_code=401, detail="Unknown subscription action type")
        
       
       
       
        # Send notification
        currency_res = requests.get(f"{settings.SETTINGS_API_URL}/setting/key/pvgis__ui.default_currency")
        currency=currency_res.json()["value"]
        user_count=created_subscription.product_json['user_count']
        credit_count= Product.get_credit_from_json(created_subscription.product_json) 
        current_year = func.extract("year", func.now())
        current_month = func.extract("month", func.now())
        created_count = crud.simulation.get_count_where_array(
            db=db,
            where=[
                {"key": "subscription.customer_id", "value": customer_id, "operator": "=="},
                {"key": "created_at", "operator": "month", "value": current_month},
                {"key": "created_at", "operator": "year", "value": current_year},
            ],
        )
        balance_count=created_subscription.credit_balance
        vars = {
                    "customerUsername": created_subscription.customer.pseudo or created_subscription.customer.first_name+" "+created_subscription.customer.last_name ,
                    "subscriptionName": created_subscription.product_json['name'],
                    "startedDate": created_subscription.start_date.strftime("%d/%m/%Y"),
                    "expiredDate": created_subscription.expired_date.strftime("%d/%m/%Y"),
                    "userCount":user_count,
                    "projectCreditCount":credit_count,
                    "projectCreatedCount":created_count,
                    "projectBalanceCount":balance_count,
                    "projectBillingPeriodInterval": created_subscription.product_json.get('billing_period_interval', BillingPeriodIntervalEnum.month.value),
                    "url":f"{settings.PVGIS_UI_URL}/pvgis24/app/"+str(created_subscription.customer_id)+"/subscription",
                    "customerId":created_subscription.customer.id,
                    "refundAmount":f"{int(refund_amount):d}",
                    "currency": currency['symbol'],
                    "stripeCustomerLink": f"{settings.STRIPE_DASHBOARD_BASE_URL}/customers/{stripe_customer_id}"
                }
       
        # TODO  ask aina about that
        # for test product 
        # if(created_subscription.product_json["monthly_price"]==0):
        #     crud.customer.update(db=db,db_obj=customer,obj_in={"cart_reference":None},commit=False, flush=True)
        #     return {'subscription':created_subscription,'subscription_payment_transaction':None }
        
        return {
            'subscription':created_subscription,
            'subscription_payment_transaction':None,
            'notification_mail_vars': vars,
            'customer':customer,
            'created_refund_invoice': created_refund_invoice
            }
    
    def send_notif_mail_after_payment(self, 
                                      checkout_json,
                                      customer: Customer, 
                                      vars: Any):
        subscription_action_type = SubscriptionActionTypeEnum(checkout_json['metadata']['subscriptionActionType'])
        refund_amount = float(checkout_json['metadata'].get('refundAmount',0))
        user_id = customer.auth_user_id
        users = [
            {
                "id": user_id,
                "roleId": 2,
                "appKey": "1284939136",
                "vars": vars,
                "bcc":[settings.BILLING_EMAIL]
            }, 
        ]
        
        roles= []
        
        if settings.IS_PROD:
            roles = [
                {
                    "roleId": 1,
                    "appKey": "1284939136",
                    "vars": vars
                }
            ]
        
        
        lang = customer.settings_json.get('language', 'en')
        lang = lang if len(lang) == 2 else 'en'
        if(subscription_action_type == SubscriptionActionTypeEnum.ADDITIONAL): send_notif(event_code="subscription-additional-credit", users=users, mailLanguage=lang)
        elif(subscription_action_type == SubscriptionActionTypeEnum.CREATION): send_notif(event_code="subscription-creation", users=users, mailLanguage=lang)
        elif(subscription_action_type == SubscriptionActionTypeEnum.UPGRADE): send_notif(event_code="subscription-upgrade", users=users, mailLanguage=lang)
        elif(subscription_action_type == SubscriptionActionTypeEnum.DOWNGRADE and refund_amount == 0): send_notif(event_code="subscription-downgrade", users=users, mailLanguage=lang, isCmsTemplate=True)
        elif(subscription_action_type == SubscriptionActionTypeEnum.DOWNGRADE and refund_amount > 0): 
            send_notif(event_code="subscription-downgrade-with-refund", users=users, mailLanguage=lang, roles=roles,isCmsTemplate=True)
            
            # Sending refund email (billing)
            cms_key = 'mail-admin-subscription-downgrade-with-refund'
            email_param = {
                "templateVars": vars,
                "mailVars": {
                    "to":  settings.BILLING_EMAIL,
                },
                "baseUrls": {"cmsUrl": settings.CMS_URL, "translationUrl": settings.TRANSLATION_URL},
                "sentByApp": "app",
                "sentToUser": "Billing",
                "sentByProcess": "Pvgis", 
                "type": "MAIL", 
            }
            
            # print('EMAIL PARAMS')
            # print(email_param)
            send_email_cms(email_param, lang, cms_key)
        
       

    def get_customer_from_stripe_ob(self, invoice_ob):
        customer_id = (
            get_from_emb_dict(invoice_ob, 'metadata', 'customerId') 
            or get_from_emb_dict(invoice_ob, "lines", "data",0, 'metadata', 'customerId') 
        )
        if not customer_id:
            raise Exception ("Customer id is not found in the event metadata")
        else:
            customer_id = int(customer_id)
        return customer_id 
    
    def get_amount_paid_from_stripe_ob(self, invoice_ob):
        amount_paid=None
        if "amount_paid" in invoice_ob:
            amount_paid=invoice_ob["amount_paid"]
        elif "amount" in invoice_ob:
            amount_paid=invoice_ob["amount"]
            
        return amount_paid/100 if amount_paid else None
    
    def get_charge_id_from_stripe_ob(self, stripe_ob):
        payment_intent_id=None
        if "payment_intent" in stripe_ob and stripe_ob["object"] ==  "invoice":
            payment_intent_id=stripe_ob["payment_intent"]
        elif stripe_ob["object"] ==  "payment_intent":
            payment_intent_id=stripe_ob["id"]
        
        if payment_intent_id:
            charge = stripe.Charge.list(payment_intent=payment_intent_id, limit=1)
            if charge['data'] and len(charge['data']):
                return charge['data'][0]['id']
        return None
    
    def get_accounting_from_stripe_ob(self, invoice_ob):
        accounting_json_str = get_from_emb_dict(invoice_ob, 'metadata', 'accountingJsonStr') 
        if not accounting_json_str:
            return {}
        accounting = json.loads(accounting_json_str)
         
        return accounting
    
    def get_amount_due_from_stripe_ob(self, invoice_ob):
        amount_due=None
        if "amount_due" in invoice_ob:
            amount_due=invoice_ob["amount_due"]
        elif "amount" in invoice_ob:
            amount_due=invoice_ob["amount"]
        return amount_due
    
    
     

    # create invoice (for invoice creation event, not payment)
    def save_invoice_if_not_exists(self,
                        db:Session,  
                        cart_reference,
                        subscription_ob: Subscription,
                        stripe_ob, 
                        subscription_stripe_id,
                        cart=None,
                        customer = None) :
        customer_id = self.get_customer_from_stripe_ob(stripe_ob)
        
         # Get the total paid amount 
        
        amount_due= self.get_amount_due_from_stripe_ob(stripe_ob)
        amount_due = crud.subscription_payment_transaction.get_stripe_amount_real_value(amount_due,stripe_ob["currency"]) 
        
        # Get the corresponding cart
        cart= cart or crud.cart.get_first_where_array(db=db,where=[{'key':'cart_reference',"value":cart_reference,"operator":"=="}])
       
        if cart and cart.product_id != 11142024 and amount_due == 0:
            return None
         
        customer=customer or crud.customer.get(db=db,id=customer_id)
        customer_for_records = crud.customer.get_for_records(db=db, customer_id=customer_id)
        
        
    
        
        #Check if the invoice already exists
        existing_spt = crud.subscription_payment_transaction.get_first_where_array(
            db=db,
            where=[{"key":"stripe_object_id","operator":"==","value":stripe_ob['id']}]
        )
         
        if(existing_spt):
            return existing_spt
        
        # Save the transaction in the database to keep track of the payment and display invoice list
        spt_in=SubscriptionPaymentTransactionCreate(
            currency=stripe_ob["currency"],
            payment_date=datetime.now(),
            payment_gateway_id=1,
            subscription_id=subscription_ob.id if subscription_ob else None, 
            subscription_stripe_id=subscription_stripe_id, 
            payment_status=PaymentStatusEnum.SUCCESS,
            cart_id=cart.id if cart else None,
            payment_transaction_json=stripe_ob,
            amount_paid=amount_due,
            next_payment_date = subscription_ob.expired_date if subscription_ob else None, 
            stripe_object_id = stripe_ob['id'],
            paid=True, 
            customer_json = jsonable_encoder(customer_for_records)
        )
       
        spt_created=crud.subscription_payment_transaction.create_subscription_payment_transaction(
            db=db,
            spt_in=spt_in,
            customer_id=customer_id, 
            user_id=customer.id,
          
        )
        
        reference= generatePaymentReference(spt_created.id)
        spt_updated = crud.subscription_payment_transaction.update(
            db=db, db_obj=spt_created, obj_in={'reference': reference},
            commit=False, flush=True
        )
        return spt_updated
        
    def save_invoice(self, db, 
                                 session_stripe_ob,
                                 customer, 
                                 subscription, 
                                 subscription_stripe_id, 
                                 cart_id, 
                                 
                                 # negative if refund
                                 total_amount):
        customer_for_records = crud.customer.get_for_records(db=db, customer_id=customer.id)
        spt_in=SubscriptionPaymentTransactionCreate(
            currency=session_stripe_ob["currency"],
            payment_date=datetime.now(),
            payment_gateway_id=1,
            subscription_id=subscription.id if subscription else None, 
            subscription_stripe_id=subscription_stripe_id, 
            payment_status=PaymentStatusEnum.SUCCESS,
            cart_id=cart_id,
            payment_transaction_json=session_stripe_ob,
            amount_paid=total_amount,
            next_payment_date = subscription.expired_date , 
            stripe_object_id = session_stripe_ob['id'],
            paid=True, 
            customer_json = jsonable_encoder(customer_for_records)
        )
       
        spt_created=crud.subscription_payment_transaction.create_subscription_payment_transaction(
            db=db,
            spt_in=spt_in,
            customer_id=customer.id, 
            user_id=customer.id,
          
        )
        reference= generatePaymentReference(spt_created.id)
        spt_updated = crud.subscription_payment_transaction.update(
            db=db, db_obj=spt_created, obj_in={'reference': reference},
            commit=False, flush=True
        )
        return spt_updated
        
        
    def save_receipt(self, db,  
                     session_stripe_ob, 
                     customer,  
                     subscription_stripe_id, 
                     spt_invoice,
                     amount_paid = None,
                     remaining_to_pay = None,
                     amount_already_paid = None,
                     status = ReceiptStatusEnum.SUCCESS
                     ):
        receipt_create=SubscriptionPaymentReceiptCreate(
            reference=generate_random_string(20),
            stripe_object_id= session_stripe_ob['id'], 
            subscription_stripe_id=subscription_stripe_id,
            subscription_payment_transaction_id=spt_invoice.id,
            amount_paid=amount_paid,
            remaining_to_pay = remaining_to_pay,
            amount_already_paid = amount_already_paid,
           
            status = status
        )
       
        spr_created=crud.subscription_payment_receipt.create(
            db=db,
            obj_in=receipt_create,
            user_id=customer.id,
            commit=False, flush=True
        )
        reference= generatePaymentReference(spr_created.id)
        spr_updated = crud.subscription_payment_receipt.update(
            db=db, db_obj=spr_created, obj_in={'reference': reference},
            commit=False, flush=True
        )
        return spr_updated
        
    def save_receipt_or_renew(self,db:Session,
                                  cart_reference,
                                  subscription_ob: Subscription,
                                  invoice_ob, 
                                  subscription_stripe_id,
                                  save_invoice = True,
                                  customer_id = None,
                                  customer = None,
                                  amount_paid = None,
                                  cart =None
                                  ):
        #Get the current customer id
       
        customer_id =  customer_id or self.get_customer_from_stripe_ob(invoice_ob)
        
        # Get the total paid amount 
        amount_paid= self.get_amount_paid_from_stripe_ob(invoice_ob) if amount_paid == None  else amount_paid
        
         
         
        
        # Get the corresponding cart
        cart= cart or crud.cart.get_first_where_array(db=db,where=[{'key':'cart_reference',"value":cart_reference,"operator":"=="}])
       
        
        if cart and cart.product_id != 11142024 and amount_paid == 0 :
            return None
         
         
        customer= customer or crud.customer.get(db=db,id=customer_id)
        
        # renewal handling (subscription_ob cant be null)
        if invoice_ob and 'billing_reason' in invoice_ob and invoice_ob['billing_reason'] == "subscription_cycle":
            subscription_period=stripe_service.get_last_active_start_end_date(customer.stripe_customer_id)
            if subscription_stripe_id:
                obj_in={"subscription_stripe_id": subscription_stripe_id} 
                crud.subscription.update(db=db,db_obj=subscription_ob,obj_in=obj_in,commit=False, flush=True)
            # renewal = start and end dates updated + credit balance updated
            self.renew_subscription(db=db,subscription_ob=subscription_ob,subscription_period=subscription_period)
           

        
        invoice = crud.subscription_payment_transaction.get_first_where_array(
            db=db,
            where=[{"key":"stripe_object_id","operator":"==","value":invoice_ob['id']}]
        )
        
        # We add this condition to prevent duplicate invoices when the creation and success events are detected at the same time
        if (not invoice) and save_invoice:
            invoice = self.save_invoice_if_not_exists(db=db,
                            cart_reference=cart_reference,
                            subscription_ob=subscription_ob,
                            stripe_ob=invoice_ob, 
                            subscription_stripe_id=subscription_stripe_id,
                            cart=cart,
                            customer=customer)
        
        
        
        # Save the receipt in the database to keep track of the payment and display invoice list
        receipt_create=SubscriptionPaymentReceiptCreate(
            reference=generate_random_string(20),
            stripe_object_id=invoice_ob['id'],
            subscription_stripe_id=subscription_stripe_id,
            subscription_payment_transaction_id=invoice.id if invoice else None,
            amount_paid=amount_paid,
            stripe_reference=self.get_charge_id_from_stripe_ob(invoice_ob),
            status = ReceiptStatusEnum.SUCCESS
        )
       
        spr_created=crud.subscription_payment_receipt.create(
            db=db,
            obj_in=receipt_create,
            user_id=customer.id,
            commit=False, flush=True
        )
        reference= generatePaymentReference(spr_created.id)
        spr_updated = crud.subscription_payment_receipt.update(
            db=db, db_obj=spr_created, obj_in={'reference': reference},
            commit=False, flush=True
        )
        return spr_updated
        
        # END #

    # TODO: This code is no more relevant, since a created subscription in our database is always the result of a successful payment
    def handle_failed_payment(self,db:Session,stripe_subscription_id, stripe_customer_id):
        customer=crud.customer.get_first_where_array(db=db,where=[{"key":"stripe_customer_id","operator":"==","value":stripe_customer_id}])
        if customer is not None:
            pending_subscription = crud.subscription.get_first_where_array(
                db=db,
                where=[
                    {"key": "customer_id","operator": "==","value": customer.id}, 
                    {"key": "subscription_status","operator": "==","value": SubscriptionStatusEnum.PENDING},
                    {"key":"product_json","operator":"isNotNull"}
                ],
            )
            if pending_subscription is not None:
                obj_in={"subscription_status":SubscriptionStatusEnum.INACTIVE,"disabled_at":datetime.now(),"is_auto_renew":False}
                crud.subscription.update(db=db,db_obj=pending_subscription,obj_in=obj_in)
                # Stripe créer l'abonnement que apres le paiement est valider
                # stripe.Subscription.modify(
                #     stripe_subscription_id,
                #     cancel_at=int(time.time())  # Cancels the subscription at the end of the current period
                # )
                # please notify customer about the failed Payment when renew subscription
                 
    # def handle_trial_will_end(self,db:Session,event, lang: str = 'en'):
    #     data_ob = event['data']['object']
    #     stripe_customer_id = data_ob.get('customer')
    #     customer=crud.customer.get_first_where_array(db=db,where=[{"key":"stripe_customer_id","operator":"==","value":stripe_customer_id}])
        
    #     customer_auth_user_id=customer.auth_user_id
        
    #     customer_name=f"{customer.first_name} {customer.last_name}"
    #     subscription_end_date=data_ob.get('current_period_end')
    #     subscriptions=crud.subscription.get_multi_where_array_v2(
    #         db=db,
    #         where=[
    #             {"key":"customer_id","operator":"==","value":customer.id},
    #             {"key":"product_json","operator":"isNotNull"}
    #             ],
    #         order_by="created_at",
    #         order="DESC",
    #         base_columns=["product_json"]
    #     )
    #     subscription_name=subscriptions[0].product_json["name"]
    #     customer_stripe = stripe.Customer.retrieve(stripe_customer_id)
    #     card=stripe_service.get_default_card(stripe_customer_id)
    #     customer_payment_card = f" {card['brand']} **** **** **** {card['last4']}"
    #     subscription_price=self.calculate_price_after_trial(db=db,customer_id=customer.id,subscription_id=subscriptions[0].id)
    #     # Option 1: If the customer has a default payment method set
        
    #     users = [
    #         {
    #             "id": customer_auth_user_id,
    #             "roleId": 2,
    #             "appKey": "1284939136",
    #             "vars": {
    #                 "customerUsername": customer_name,
    #                 "subscriptionName": subscription_name,
    #                 "expiredDate": datetime.fromtimestamp(subscription_end_date).strftime('%Y-%m-%d'),
    #                 "cardNumber":customer_payment_card,
    #                 "subscriptionPrice":subscription_price
    #             }
    #         }
    #     ]
    #     send_notif(event_code="subscription-trial-end", users=users, mailLanguage=lang)
    #     return
    
    def handle_subscription_end(self,db:Session,data_ob):
        subscription_inst = None
        # subscription_stripe_id = data_ob['id']
        
        # if subscription_stripe_id :
        #     subscription_inst = db.query(Subscription).filter(Subscription.subscription_stripe_id === )
        
        customer_id=data_ob['metadata'].get('customerId')
        current_period_start = data_ob["current_period_start"] if "current_period_start" in data_ob else data_ob["items"]["data"][0]["current_period_start"]
        current_period_end = data_ob["current_period_end"] if "current_period_end" in data_ob else data_ob["items"]["data"][0]["current_period_end"]
        sub_start=datetime.fromtimestamp(current_period_start).strftime("%Y-%m-%d")
        sub_end=datetime.fromtimestamp(current_period_end).strftime("%Y-%m-%d")
        
        is_renewal_failure =  get_from_emb_dict_or_throw(data_ob,"cancellation_details",  "reason") == "payment_failed"
        query = (
             db.query(Subscription, Customer)
            .join(Customer, Customer.id == Subscription.customer_id)
            .filter( 
                or_(
                     Subscription.subscription_stripe_id == data_ob['id'],
                     and_(
                            Customer.id == customer_id, 
                            Subscription.subscription_status == SubscriptionStatusEnum.ACTIVE,
                            Subscription.product_json.isnot(None),
                            *(
                                [
                                    cast(Subscription.start_date, Date) ==  cast(sub_start, Date),
                                    # cast(Subscription.expired_date, Date) ==  cast(sub_end, Date),
                                ]
                                if not is_renewal_failure 
                                else []
                            ) 
                     )
                ) 
            )
        )
        # print(str(query.statement.compile(compile_kwargs={"literal_binds": True})).replace('\n', ' '))
        sb_result = query.first()
        
        
        
        if sb_result is not None:
            (subscription_inst, customer)= sb_result
            crud.subscription.update(db=db,db_obj=subscription_inst,obj_in={"subscription_status":SubscriptionStatusEnum.INACTIVE})
            
            
            #Send an email if the deletion is due to a failed subscription renewal (after several payment attempts)
            if is_renewal_failure:
                self.send_subscription_deactivated_email(customer)
         
    
    def handle_stripe_customer_deleted(self,db:Session,stripe_customer_id):
        customer_obj=crud.customer.get_first_where_array(db=db,where=[
            {"key":"stripe_customer_id","value":stripe_customer_id,"operator":"=="},
        ])
        if customer_obj is not None:
            crud.customer.update(db=db,db_obj=customer_obj,obj_in={"stripe_customer_id":None})
            
    
    def handle_before_renew(self,db:Session,data_ob, lang: str = 'en'):
        customer_id=data_ob["subscription_details"]["metadata"].get("customerId")
        customer=crud.customer.get(db=db,id=customer_id)
        setting_res = requests.get(f"{settings.SETTINGS_API_URL}/setting/key/pvgis__ui.renewal_days")
        renewal_days = setting_res.json()["value"]
        subscription=crud.subscription.get_first_where_array(db=db,where=[
            {"key":"customer_id","value":customer_id,"operator":"=="},
            {"key":"disabled_at","operator":"isNull"},
            {"key":"product_json","operator":"isNotNull"}
        ])
        users = [
            {
                "id": customer.auth_user_id,
                "roleId": 2,
                "appKey": "1284939136",
                "vars": {
                    "title": customer.title.value if customer.title else '',
                    "customerUsername": customer.pseudo or customer.first_name+" "+customer.last_name,
                    "subscriptionName": subscription.product_json["name"],
                    "days": renewal_days,
                    "dueDate": datetime.fromtimestamp(data_ob['next_payment_attempt']).strftime("%d/%m/%Y"),
                    "invoiceUrl":f"{settings.PVGIS_UI_URL}/pvgis24/app/{str(customer_id)}/subscription",
                    "amount":data_ob['amount_due']/100,
                    
                }
            }
        ]
        send_notif(event_code="subscription-before-renew", users=users, mailLanguage=lang)
        return 
          
    
    def calculate_price_after_trial(self,db:Session,customer_id,subscription_id):
        customer_created_at=None
        product_price=None
        customer=crud.customer.get(db=db,id=customer_id)
        subscription=crud.subscription.get(db=db,id=subscription_id)
        product_price= Product.get_price_from_json(subscription.product_json)
        customer_created_at=customer.created_at
        setting_res = requests.get(f"{settings.SETTINGS_API_URL}/setting/key/pvgis__ui.product_discount")
        percentage=setting_res.json()["value"]

        # Get the current datetime
        current_datetime = datetime.now()

        # Calculate the datetime 3 months ago from now
        three_months_ago = current_datetime - relativedelta(months=3)

        # Check if the given datetime is more recent than 3 months ago
        if customer_created_at > three_months_ago:
            return product_price-(product_price*percentage/100)
        else:
            return product_price
        
    def get_or_create_subscription_pvgis24(self, db:Session, customer_id, commit=True):
        subscription_pvgis24 = self.get_subscription_pvgis24_by_customer_id(db=db, customer_id=customer_id)
        
        if(subscription_pvgis24):
            return subscription_pvgis24
        
        customer=crud.customer.get(db=db,id=customer_id)
        subscription_in = {
            "customer_id":  customer_id, 
            "subscription_status": SubscriptionStatusEnum.PVGIS24
        }
        created_subscription = crud.subscription.create(db=db, obj_in=subscription_in, user_id=customer.auth_user_id, commit=commit)
        return created_subscription
    
    def get_subscription_pvgis24_by_customer_id(self, db:Session, customer_id):
        subscription_pvgis24 = crud.subscription.get_first_where_array(
            db=db,
            where=[
                {"key": "customer_id","operator": "==","value": customer_id}, 
                {"key": "subscription_status","operator": "==","value": SubscriptionStatusEnum.PVGIS24},
                {"key": "deleted_at","operator": "isNull"},
            ],
        )
        return subscription_pvgis24
    
    def get_subscription_by_subscription_stripe_id(self, db:Session, subscription_stripe_id):
        subscription = crud.subscription.get_first_where_array(
            db=db,
            where=[
                {"key": "subscription_stripe_id","operator": "==","value": subscription_stripe_id}, 
               
            ],
        )
        return subscription
    
    def get_latest_active_sub(self, db: Session, customer_id, is_ongoing = True):
        return db.query(Subscription).filter(
            Subscription.customer_id == customer_id, 
            Subscription.subscription_status == SubscriptionStatusEnum.ACTIVE,
            not_(Subscription.product_json.is_(None)),
            *([Subscription.start_date < datetime.now()] if is_ongoing else []), 
            ).order_by(Subscription.created_at.desc()).first()
        
    
    def send_subscription_deactivated_email(self, customer):
        cms_key = 'subscription-deactivated-mail'

        if customer is None:
            raise ValueError("Customer is not found.")
        lang = (customer.settings_json["language"] 
                if customer.settings_json.get("language", None) != None and len(customer.settings_json["language"]) == 2 
                else 'en')
        
        email_param = {
            "templateVars": {
                "firstname": customer.first_name,
                "lastname": customer.last_name,
                "url": settings.PVGIS_UI_URL+ f'/pvgis24/app/{customer.id}/subscription', 
            },
            "mailVars": {
                "to": customer.email,
            },
            "baseUrls": {"cmsUrl": settings.CMS_URL, "translationUrl": settings.TRANSLATION_URL},
            "sentByApp": "app",
            "sentToUser": customer.first_name,
            "sentByProcess": "Pvgis", 
            "type": "MAIL", 
        }
        
        # print('EMAIL PARAMS')
        # print(email_param)
        return send_email_cms(email_param, lang, cms_key)
    
    
    def send_renewal_payment_failed_email(self,db,  customer):
        
        cms_key = 'renewal-payment-failed'

        if customer is None:
            raise ValueError("Customer is not found.")
        lang = (customer.settings_json["language"] 
                if customer.settings_json["language"] != None and len(customer.settings_json["language"]) == 2 
                else 'en')
        
        email_param = {
            "templateVars": {
                "firstname": customer.first_name,
                "lastname": customer.last_name,
                "url": settings.PVGIS_UI_URL+ f'/pvgis24/app/{customer.id}/subscription', 
            },
            "mailVars": {
                "to": customer.email,
                "bcc": settings.BILLING_EMAIL
            },
            "baseUrls": {"cmsUrl": settings.CMS_URL, "translationUrl": settings.TRANSLATION_URL},
            "sentByApp": "app",
            "sentToUser": customer.first_name,
            "sentByProcess": "Pvgis", 
            "type": "MAIL", 
        }
        
        # print('EMAIL PARAMS')
        # print(email_param)
        return send_email_cms(email_param, lang, cms_key)
    
    def get_count_unsubscribed_user(self, db, where):
        subquery = db.query(
            distinct(Subscription.customer_id)).filter(
            Subscription.subscription_status == SubscriptionStatusEnum.ACTIVE
        ).subquery()
        
        unsubscribed_query = (
            db.query(
                func.count(distinct(Subscription.customer_id)).label("count"), 
            )
            .filter(
                Subscription.subscription_status == SubscriptionStatusEnum.INACTIVE,
                Subscription.product_id != '11142024',
                Subscription.customer_id.notin_(subquery)
            )
        )
         
        wheres = []
        if where is not None and where != []:
            wheres = where

        conditions = self.get_full_condition(db=db, where=wheres)


        if conditions is not None:
            unsubscribed_query = unsubscribed_query.filter(conditions)

        unsubscribed_count = unsubscribed_query.scalar()
        return unsubscribed_count
    
    
subscription = CRUDSubcription(Subscription)
