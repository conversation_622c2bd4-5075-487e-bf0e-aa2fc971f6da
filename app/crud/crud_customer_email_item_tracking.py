from app.crud.base import CRUDBase
from app.models.customer_email_item_tracking import CustomerEmailItemTracking
from app.schemas.customer_email_item_tracking import CustomerEmailItemTrackingCreate, CustomerEmailItemTrackingUpdate

class UDddCustomerEmailItemTracking(CRUDBase[CustomerEmailItemTracking, CustomerEmailItemTrackingCreate, CustomerEmailItemTrackingUpdate]):
    pass

customer_email_item_tracking= UDddCustomerEmailItemTracking(CustomerEmailItemTracking)
