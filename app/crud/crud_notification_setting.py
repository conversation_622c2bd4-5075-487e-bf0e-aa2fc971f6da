from typing import Optional
from fastapi.encoders import jsonable_encoder
from sqlalchemy import and_
from app.crud.base import CRUDBase
from app.models.notification_setting import NotificationSetting
from sqlalchemy.orm import Session

from app.schemas.notification_setting import NotificationSettingCreate, NotificationSettingUpdate


class CRUDNotificationSetting(CRUDBase[NotificationSetting, NotificationSettingCreate, NotificationSettingUpdate]):
    pass
    # def create(self, db: Session, *, obj_in: NotificationSettingCreate) -> NotificationSetting:
    #     db_obj = NotificationSetting(**jsonable_encoder(obj_in))
    #     db.add(db_obj)
    #     db.commit()
    #     db.refresh(db_obj)
    #     return db_obj

notification_setting = CRUDNotificationSetting(NotificationSetting)
