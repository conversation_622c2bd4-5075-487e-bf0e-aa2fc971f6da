from app.models.payment_gateway import PaymentGateway
from app.schemas.payment_gateway import PaymentGatewayCreate, PaymentGatewayUpdate
from fastapi.encoders import jsonable_encoder
from app.crud.base import CRUDBase
from sqlalchemy.orm import Session



class CRUDPaymentGateway(
    CRUDBase[PaymentGateway, PaymentGatewayCreate, PaymentGatewayUpdate]
):
    pass


payment_gateway = CRUDPaymentGateway(PaymentGateway)
