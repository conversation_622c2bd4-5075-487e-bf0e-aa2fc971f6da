from ast import Dict, List
import hashlib
import os
from typing import Any, Optional
from app.utils.utils import to_dict
from sqlalchemy.orm import Session
import requests
import stripe
from app import crud
from app.crud.base import CRUDBase, custom_jsonable_encoder
from app.models.google_analytics import GoogleAnalytics
from app.schemas.google_analytics import GoogleAnalyticsCreate, GoogleAnalyticsUpdate
from app.config.settings import get_settings

settings = get_settings()

class CRUDGoogleAnalytics(CRUDBase[GoogleAnalytics, GoogleAnalyticsCreate, GoogleAnalyticsUpdate]):
    
    def client_id_from_email(self, email: Optional[str]) -> str:
        if not email:
            return "555.111"
        h = hashlib.sha256(email.encode("utf-8")).hexdigest()
        n = int(h[:12], 16)
        return f"{n}.1"

    def send_ga4_report(self, payload: Any):
        url = f"https://www.google-analytics.com/mp/collect?measurement_id={settings.GOOGLE_ANALYTICS_MEASUREMENT_ID}&api_secret={settings.GOOGLE_ANALYTICS_API_SECRET}"
        response = requests.post(url, json=payload, timeout=10)
        print(response)
        if response.status_code >= 300:
            raise RuntimeError(f"GA4 MP error {response.status_code}: {response.text}")    
    
    def line_items_to_ga4(self, db: Session, cart_reference: str):
        items = []
        cart = crud.cart.get_by_reference(db=db, cart_reference=cart_reference)
        price = cart.product.monthly_price
        items.append({
            "item_id": cart.product.id,
            "item_name": cart.product.name,
            "quantity": cart.quantity,
            "price": price,
            "index": cart.id,
        })
        
        return items
    
    def send_purchase_report(self, db: Session, checkout_obj: Any):
        currency = (checkout_obj["currency"] or "usd").upper()
        amount_total = (checkout_obj["amount_total"] or 0) / 100
        total_details = checkout_obj["total_details"] or {}
        tax_total = (total_details["amount_tax"] or 0) / 100
        shipping_total = (total_details["amount_shipping"] or 0) / 100

        items = self.line_items_to_ga4(db=db, cart_reference=checkout_obj["metadata"]["cartReference"])
        
        payload = {
            "client_id": checkout_obj["metadata"]["customerId"],
            "events": [
                {
                    "name": "purchase",
                    "params": {
                        "affiliation": "PVGIS.COM",
                        "currency": currency,
                        "value": amount_total,
                        "tax": tax_total,
                        "shipping": shipping_total,
                        "items": items
                    }
                }
            ]
        }
        
        print(payload)
        
        try:
            self.send_ga4_report(payload=payload)
        except Exception as e:
            print("GA4 send failed:", e)   


google_analytics = CRUDGoogleAnalytics(GoogleAnalytics)
