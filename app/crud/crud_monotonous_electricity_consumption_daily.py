from typing import Optional, Any
from requests import Session
from app.crud.base import CRUDBase
from app.models.monotonous_electricity_consumption_daily import MonotonousElectricityConsumptionDaily
from app.schemas.monotonous_electricity_consumption_daily import MonotonousElectricityConsumptionDailyCreate, MonotonousElectricityConsumptionDailyUpdate

class CRUDMonotonousElectricityConsumptionDaily(CRUDBase[MonotonousElectricityConsumptionDaily, MonotonousElectricityConsumptionDailyCreate, MonotonousElectricityConsumptionDailyUpdate]):
    pass

monotonous_electricity_consumption_daily = CRUDMonotonousElectricityConsumptionDaily(MonotonousElectricityConsumptionDaily)
