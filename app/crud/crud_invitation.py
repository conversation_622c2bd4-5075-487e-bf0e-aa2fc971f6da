import asyncio

from sqlalchemy.orm import Session
from fastapi import HTTPException
from app.models.invitation import Invitation
from app.models.customer import Customer
from app.schemas.invitation import InvitationCreate, InvitationUpdate
from app.crud.base import CRUDBase

from sqlalchemy.orm import Session
from app.models.invitation import Invitation
from app.schemas.invitation import InvitationCreate, InvitationUpdate, InvitationAccept
from app.crud.base import CRUDBase
from fastapi import HTTPException
from app import crud
import requests
from app.config.settings import get_settings
from datetime import datetime
from sqlalchemy import func
from fastapi.encoders import jsonable_encoder
from app.services.auth_service import  create_auth_user_from_invitation, update_auth_user_after_invitation
import jwt
from datetime import datetime, timedelta
import json
import urllib.parse
from app.utils.utils import send_email, send_email_cms
from typing import Any
import time
import threading

settings = get_settings()


def distribute_customer_list(customers, senders):
    # Nombre total de clients
    total_customers = len(customers)
    total_senders = len(senders)

    # Répartition équitable
    base = total_customers // total_senders
    remainder = total_customers % total_senders

    # Diviser la liste
    distribution = []
    start_index = 0
    for i in range(total_senders):
        end_index = start_index + base + (1 if i < remainder else 0)
        distribution.append(customers[start_index:end_index])
        start_index = end_index

    return distribution


class CRUDInvitation(CRUDBase[Invitation, InvitationCreate, InvitationUpdate]):

    def check_customer_and_email(
            self,
            db: Session,
            email: str,
            customer: Any, 
    ):
        # Check user with email if exists
        if email   :
            user_response = requests.get(f"{settings.AUTH_API_URL}/user/username/{email}")
            if user_response.text != "":
                raise HTTPException(status_code=400, detail="email_taken")

        # Check customer if already has account
        if customer:

          

            if customer.auth_user_id is not None:
                raise HTTPException(status_code=400, detail="customer_already_has_account")

    def send_invitation(self, db, invitation_in, 
                        user_id=None, 
                        sender=None, 
                        effective_send_mail=True, 
                        customer = None,
                        resend = False):
        if not customer:
            if invitation_in.customer_id: 
                customer = crud.customer.get(db=db, id=invitation_in.customer_id, relations=["account_information"])
            else:
                customer = crud.customer.get_first_where_array(db=db, where=[
                        {
                            "key": "email", 
                            "operator": "==",
                            "value": invitation_in.email
                        }
                    ], relations=["account_information"])
                invitation_in.customer_id = customer.id
        if customer is None:
                raise HTTPException(status_code=400, detail="customer_not_found")
        if not resend:
            self.check_customer_and_email(db=db, email=invitation_in.email, customer=customer)

        invitation_in.invitation_sent_at = datetime.now()

        invitation = crud.invitation.create(db=db, obj_in=invitation_in, user_id=user_id)
        link_data = {
            "invitation_id": invitation.id,
            # "customer_id": invitation_in.customer_id,
            # "company": customer.account_information.company_name,
            # "last_name": customer.last_name,
            # "first_name": customer.first_name,
            # "email": invitation_in.email,
            # "country": customer.country
        }

        # Encode the link data into a JWT
        encoded_data = jwt.encode(link_data, settings.INVITATION_SECRET_KEY, algorithm="HS256")

        invitation_link = f"{settings.PVGIS_UI_URL}/invitation?data={urllib.parse.quote(encoded_data)}"

        crud.invitation.update(
            db=db,
            db_obj=invitation,
            obj_in={"invitation_link": invitation_link},
        )
        template_vars = {
            "firstname": customer.first_name,
            "lastname": customer.last_name,
            "invitation_url": invitation_link,
            "pvgis_com_url": invitation_link,
            "pvgis_24_url": invitation_link,
        }
        if effective_send_mail:
            self.send_email_invitation(template_vars, invitation_in.email,
                                   sender=sender, lang=customer.settings_json.get("language", "fr"))

        return { "message":"invitation_sent", "template_vars": template_vars}

    def bulk_send_invitation_installer(self, db: Session, user_id: int):
        # get all customer without auth_user_id and account_info.professional category in [1,2,3,4]
        customer_not_invited = crud.customer.get_multi_where_array_v2(
            db=db,
            base_columns=["id", "invitation_id", "import_id", "email"],
            where=[
                {"key": "auth_user_id", "operator": "isNull"},
                {"key": "email", "operator": "isNotNull"},
                {"key": "account_information.professional_category_id", "operator": "in", "value": [1, 2, 3, 4]},
                {"key": "invitation_id", "operator": "isNull"},
                {"key": "import_id", "operator": "isNotNull"}
            ]
        )

        # TODO Go to franck
        # def send_invitations(db, customers, user_id):
        #     senders = settings.SENDERS
        #     customer_distribution = distribute_customer_list(customers, senders)
        #     for sender, sender_customers in zip(senders, customer_distribution):
        #         for customer in sender_customers:
        #             try:
        #                 invitation_in = InvitationCreate(**{
        #                     "customer_id": customer.id,
        #                     "email": customer.email
        #                 })
        #                 self.send_invitation(db=db, invitation_in=invitation_in, user_id=user_id, sender=sender)
        #                 print(f"Invitation sent to customer ID: {customer.id} by sender: {sender}")
        #             except Exception as e:
        #                 print(f"Error sending invitation to customer ID: {customer.id}: {e}")
        #                 raise e
        # time.sleep(300)

        async def send_invitations(db, customers, user_id):
            senders = settings.SENDERS
            total_customers = len(customers)
            total_senders = len(senders)

            # Ensure there are enough senders
            if total_senders == 0:
                raise ValueError("No senders available")

            async def send_invitation_batch(batch):
                tasks = []
                for sender, customer in batch:
                    tasks.append(send_invitation(db, customer, user_id, sender))
                await asyncio.gather(*tasks)

            async def send_invitation(db, customer, user_id, sender):
                try:
                    invitation_in = InvitationCreate(**{
                        "customer_id": customer.id,
                        "email": customer.email
                    })
                    # Call the invitation sending method
                    self.send_invitation(db=db, invitation_in=invitation_in, user_id=user_id, sender=sender)
                    print(f"Invitation sent to customer ID: {customer.id} by sender: {sender}")
                except Exception as e:
                    print(f"Error sending invitation to customer ID: {customer.id}: {e}")
                    raise e

            # Divide customers into batches
            customer_index = 0
            while customer_index < total_customers:
                batch = [
                    (senders[i % total_senders], customers[customer_index + i])
                    for i in range(min(total_senders, total_customers - customer_index))
                ]
                await send_invitation_batch(batch)
                customer_index += len(batch)
                if customer_index < total_customers:
                    print(f"Waiting 5 seconds before processing the next batch...")
                    await asyncio.sleep(settings.STEP_EMAIL)

        # TODO to remove test
        # for i in range(10):
        asyncio.run(send_invitations(db=db, customers=customer_not_invited, user_id=user_id))
        # invitation_thread = threading.Thread(target=send_invitations, args=(db,customer_not_invited,user_id))
        # invitation_thread.daemon = True
        # invitation_thread.start()

        return customer_not_invited

    def accept_invitation(
            self,
            db: Session,
            invitation_accept_in: InvitationAccept,
    ):
        try:
            found_invitation = crud.invitation.get_first_where_array(db, where=[
                {"key": "id", "operator": "==", "value": invitation_accept_in.id}])

            if found_invitation is None:
                raise HTTPException(status_code=400, detail="invitation_not_found")

            customer_obj = crud.customer.get(db=db, id=invitation_accept_in.customer_id)
            update_auth_user_after_invitation(db,invitation_accept_in,customer_obj )
            
            return {"message":"invitation_accepted", "auth_user_id": customer_obj.auth_user_id}
        except:
            db.rollback()
            raise

    def send_email_invitation(self, template_vars, email, sender: dict = None, lang="fr"):
        email_param = {
            "templateVars": template_vars,
            "mailVars": {
                "from": settings.SMPT_USER_MAIL if not sender else sender['email'],
                "to": email,
                "force_from": True if sender else False,
                "user_password": settings.SMPT_USER_PASSWORD if not sender else sender['password'],
            },
            "baseUrls": {"cmsUrl": settings.CMS_URL, "translationUrl": settings.TRANSLATION_URL},
            "sentByApp": "app",
            "sentByProcess": "Form submission",
            "sentToUser": "",
            "type": "MAIL",
        }
        send_email_cms(email_param, lang, "pvgis-installer-invitation")
        
        return email_param


invitation = CRUDInvitation(Invitation)
