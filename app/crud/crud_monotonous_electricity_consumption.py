from typing import Optional, Any
from requests import Session
from app.crud.base import CRUDBase
from app.models.monotonous_electricity_consumption import MonotonousElectricityConsumption
from app.schemas.monotonous_electricity_consumption import MonotonousElectricityConsumptionCreate, MonotonousElectricityConsumptionUpdate

class CRUDMonotonousElectricityConsumption(CRUDBase[MonotonousElectricityConsumption, MonotonousElectricityConsumptionCreate, MonotonousElectricityConsumptionUpdate]):
    pass

monotonous_electricity_consumption = CRUDMonotonousElectricityConsumption(MonotonousElectricityConsumption)
