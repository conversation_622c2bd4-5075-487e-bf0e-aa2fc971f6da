from app.crud.base import CRUDBase
from app.models.stripe_temp_payment_success import StripeTempPaymentSuccess
from app.models.stripe_webhook import StripeWebhook

from app.schemas.stripe_webhook import StripeWebhookCreate, StripeWebhookUpdate


class CRUDStripeTempPaymentSuccess(CRUDBase[StripeTempPaymentSuccess, StripeWebhookCreate, StripeWebhookUpdate]):
    pass


stripe_temp_payment_success = CRUDStripeTempPaymentSuccess(StripeTempPaymentSuccess)
