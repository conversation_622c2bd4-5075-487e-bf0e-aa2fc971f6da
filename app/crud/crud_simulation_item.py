import io
from fastapi import HTTPException
from fastapi.encoders import jsonable_encoder
from app.crud.base import CRUDBase
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from sqlalchemy.sql import case
import pandas as pd
from openpyxl import load_workbook

from app.models.customer import Customer
from app.models.simulation import Simulation
from app.models.simulation_item import SimulationItem
from app.models.subscription import Subscription
from app.schemas.simulation_item import SimulationItemCreate, SimulationItemUpdate


class CRUDSimulationItem(
    CRUDBase[SimulationItem, SimulationItemCreate, SimulationItemUpdate]
):

    def get_simulation_by_id(self, db: Session, id: int):
        try:
            return db.query(Simulation.id).filter(Simulation.id == id).one()
        except Exception as e:
            raise HTTPException(
                status_code=404,
                detail="Simulation not found when creating a simulation item",
            )

    def get_simulation_item_list(self, db: Session, simulation_type: str):
        _filter = []
        if simulation_type == "all" or simulation_type == "":
            _filter = []
        elif simulation_type == "pvgis24":
            _filter.append(
                or_(
                    SimulationItem.simulation_type.is_(None),
                    SimulationItem.simulation_type == simulation_type
                )
            )
        else:
            _filter.append(SimulationItem.simulation_type == simulation_type)

        simulation_data =  (
            db.query(
                Simulation.country,
                Simulation.latitude,
                Simulation.longitude,
                SimulationItem.created_at,
                case(
                    [(SimulationItem.simulation_type.is_(None), "pvgis24")],
                    else_=SimulationItem.simulation_type,
                ).label("simulation_type"),
                SimulationItem.params_json,
                Customer.id,
                Customer.email,
                Customer.last_name,
                Customer.first_name,
            )
            .join(Simulation, SimulationItem.simulation_id == Simulation.id)
            .join(Subscription, Simulation.subscription_id == Subscription.id)
            .join(Customer, Subscription.customer_id == Customer.id)
            .filter(and_(*_filter))
            .all()
        )

        result = []
        for row in simulation_data:
            params_json = row.params_json
            peak_power = params_json.get('pvgisResult', {}).get('peakPower')
            if peak_power is None:
                peak_power = self.calculate_total_peak_power(params_json.get('pvgis24', []))
            result.append({
                "Country": row.country,
                "Latitude": row.latitude,
                "Longitude": row.longitude,
                "Created At": row.created_at,
                "Simulation Type": row.simulation_type,
                "Puissance": peak_power,
                "Customer ID": row.id,
                "Email": row.email,
                "Last Name": row.last_name,
                "First Name": row.first_name
            })

        return result

    def calculate_total_peak_power(self, data):
        if not isinstance(data, list):
            return 0

        total = 0
        for item in data:
            if isinstance(item.get('peakpower'), (int, float)):
                total += item['peakpower']

        return total


    def write_simulation_data_to_excel(self, db: Session, simulation_type: str):
        # Get the data using the method
        data = self.get_simulation_item_list(db, simulation_type)
        output = io.BytesIO()
        # Define the column names
        columns = [
            "Country", "Latitude", "Longitude", "Created At",
            "Simulation Type", "Puissance", "Customer ID", "Email",
            "Last Name", "First Name"
        ]

        # Convert the data into a DataFrame
        df = pd.DataFrame(data, columns=columns)

        # Write the DataFrame to an Excel file
        with pd.ExcelWriter(output, engine="openpyxl") as writer:
            df.to_excel(writer, index=False, sheet_name="Simulation Data")
            workbook = writer.book
            sheet = workbook["Simulation Data"]
            sheet.auto_filter.ref = sheet.dimensions
        output.seek(0)
        return output 


simulation_item = CRUDSimulationItem(SimulationItem)
