import json
from typing import Any
from app.const.client_config import CLIENT_CONFIG
from app.enums.subscription_status_enum import SubscriptionStatusEnum
from fastapi import HTTPException
from fastapi.encoders import jsonable_encoder
from app.services.subscription_service import get_user_and_subscription_user
import jwt
from app.config.db.database import AlchemyEncoder, SessionLocal
from app.crud.base import CRUDBase
from app.models.customer import Customer
from sqlalchemy.orm import Session
from app.schemas.account_information import AccountInformationCreate
from app.schemas.customer import CustomerCreate, CustomerUpdate, Token
from app.const.default_data import DEFAULT_REFERENTIAL_DATA
from app import crud
import requests
from sqlalchemy.exc import SQLAlchemyError
from datetime import datetime, timedelta
import logging
import urllib.parse
from google_auth_oauthlib.flow import Flow
from google.auth.transport import requests as reqs
from google.oauth2 import id_token
from google.auth.exceptions import GoogleAuthError
from fastapi.responses import RedirectResponse
from app.services.notification.notification import send_notif
from app.utils.utils import custom_jsonable_encoder
from sqlalchemy import func
from app.services.city_migration_service import get_timezone_offset

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
from app.config.settings import get_settings

settings = get_settings()


class CRUDUser(CRUDBase[Customer, CustomerCreate, CustomerUpdate]):

    def authenticate(self, db: Session, username: str, password: str) -> Token:
        payload = {
            "username": username,
            "password": password,
            "appKey": settings.APP_KEY,
        }

        response = requests.post(f"{settings.AUTH_API_URL}/user/signin", json=payload)

        if response.status_code == 202:
            auth_user = response.json()

            payload = jwt.decode(
                auth_user["data"]["token"], options={"verify_signature": False}
            )

            return Token(access_token=auth_user["data"]["token"])
        else:
            raise HTTPException(
                status_code=response.status_code, detail=response.json()
            )

    def social_authenticate(self, db: Session, username: str) -> Any:
        payload = {"username": username, "appKey": settings.APP_KEY, "forceUpdate": True}

        response = requests.post(
            f"{settings.AUTH_API_URL}/user/social-networks/signin",
            json=payload,
            headers=settings.x_app_key_header,
        )

        if response.status_code != 202:
            error_message = response.json().get("error", "Unknown error occurred")
           
            return error_message
        else:
            auth_res = response.json()

            payload = jwt.decode(
                auth_res["data"]["token"], options={"verify_signature": False}
            )

            user_id = payload.get("id")

            found_customer, found_subscription_user = get_user_and_subscription_user(db=db, user_id=user_id,
                                                                                     throw_if_not_found=True,
                                                                                     isHttpException=False)

            auth_res["data"]["user_type"] = "customer" if found_customer else "subscription_user"

            if not found_customer and found_subscription_user:
                found_customer = found_subscription_user.subscription.customer

            twenty_four_hours_from_now = datetime.now() + timedelta(hours=24)
            date_cond = found_customer.to_delete_at and found_customer.to_delete_at <= twenty_four_hours_from_now
            disabled_user_cond = found_customer and date_cond and not found_customer.subscription

            if (found_customer and found_customer.deleted_at) or disabled_user_cond:
                raise Exception("pvgis.authentication_error.user_disabled")

            # auth_res["data"]["customer"] = jsonable_encoder(found_customer)
            auth_res["data"]["customer"] = {
                "id": found_customer.id
            }
            auth_res["data"]["user_id"] = user_id
            auth_res["data"]["subscription_users"] = jsonable_encoder(found_subscription_user)

            return auth_res

    def construct_payload(self, received_data):
        return {
            "username": received_data.email,
            "password": received_data.password,
            "app": settings.APP_KEY,
            "firstname": received_data.first_name,
            "lastname": received_data.last_name,
            "pseudo": received_data.pseudo,
            "emailInfo": {
                "templateVars": {
                    "name": received_data.first_name,
                    "surname": received_data.last_name,
                    "username": received_data.email,
                    "verification_url": f"{settings.PVGIS_UI_URL}/login/",
                },
                "templateName": "pvgis-com/registration-message",
                "mailVars": {
                    "from": settings.SMPT_USER_MAIL,
                    "to": received_data.email,
                    "subject": "Email confirmation",
                },
                "sentByApp": settings.APP_KEY,
                "sentByProcess": "registration",
                "sentToUser": received_data.email,
            },
            "language": received_data.language,
        }

    def send_signup_request(self, payload):
        response = requests.post(f"{settings.AUTH_API_URL}/user/signup", json=payload)
        return response

    def handle_response(self, response):
        if response.status_code == 202 or response.status_code == 201:
            return response.json()
        else:
            raise HTTPException(status_code=response.status_code, detail=response.text)

    def create_customer(self, db: Session, received_data, user_id):
        user_in = CustomerCreate(
            first_name=received_data.first_name,
            last_name=received_data.last_name,
            auth_user_id=user_id,
            email=received_data.email,
            pseudo=received_data.pseudo,
            accept_cgu=received_data.accept_cgu,
            cart_reference=received_data.cart_reference,
            country=received_data.country,
            country_id=received_data.country_id,
            timezone_offset=received_data.timezone_offset,
            settings_json={
                "language": received_data.language if received_data.language else "en",
                "simulation_language": received_data.language if received_data.language else "en",
                "base_currency": {
                    "name": "Euro",
                    "code": "EUR",
                    "symbol": "€",
                    "default": "true"
                }
            },
            default_referential_info_json=DEFAULT_REFERENTIAL_DATA,
        )
        
        created_customer = crud.customer.create(
            db=db, obj_in=user_in, user_id=user_id, commit=False, refresh=False
        )
        crud.account_notification_setting.check_all_by_customer_id(
            db=db, customer_id=created_customer.id
        )

        return created_customer

    def create_account_information(
            self, db: Session, created_customer, received_data, user_id
    ):
        account_information_in = AccountInformationCreate(
            customer_id=created_customer.id,
            account_type_id=received_data.account_type_id,
            company_name=received_data.company_name,
        )

        created_account_information = crud.account_information.create(
            db=db,
            obj_in=account_information_in,
            user_id=user_id,
            commit=False,
            refresh=False,
        )
        return created_account_information

    def create_user_in_db(self, db: Session, received_data):
        data = []

        created_customer = self.create_customer(
            db=db,
            received_data=received_data,
            user_id=None,  # We don't have the auth user ID yet
        )
        data.append(json.dumps(created_customer, cls=AlchemyEncoder))

        created_account_information = self.create_account_information(
            db=db,
            created_customer=created_customer,
            received_data=received_data,
            user_id=None,  # We don't have the auth user ID yet
        )
        data.append(json.dumps(created_account_information, cls=AlchemyEncoder))

        return {
            "customer": json.loads(data[0]),
            "account_information": json.loads(data[1]),
        }

    def update_user_with_auth_id(self, db: Session, customer_id: int, auth_user_id: str):
        customer = crud.customer.update(
            db=db,
            db_obj=crud.customer.get(db=db, id=customer_id),
            obj_in={"auth_user_id": auth_user_id},
        )
        db.flush()
        account_information = crud.account_information.get_first_where_array(
            db=db,
            where=[{"key": "customer_id", "value": customer_id, "operator": "=="}],
        )
        if account_information:
            crud.account_information.update(
                db=db,
                db_obj=account_information,
                obj_in={"last_user_to_interact": auth_user_id},
            )

        return customer, account_information

    def main_function(self, db: Session, received_data, verification=True):
        # country = crud.country.get_by_code_alpha_2(
        #     db=db,
        #     code_alpha_2=received_data.country_code
        # )
        # received_data.country = country.name
        try:
            # Start a new transaction
            db.begin(subtransactions=True)

            invited_user = crud.customer.get_first_where_array_v2(
                db=db,
                where=[
                    {"key": "email", "operator": "==", "value": received_data.email},
                    {"key": "auth_user_id", "operator": "isNull"}
                ]
            )
            # print("invited_user eto 1", received_data.email)
            # print("invited_user", invited_user)

            if invited_user:

                # Update invited user with new information
                crud.customer.update(
                    db=db,
                    db_obj=invited_user,
                    obj_in={
                        "first_name": received_data.first_name,
                        "last_name": received_data.last_name,
                        "pseudo": received_data.pseudo,
                        "accept_cgu": received_data.accept_cgu,
                        "cart_reference": received_data.cart_reference,
                        "country": received_data.country,
                        "country_id": received_data.country_id,
                        "timezone_offset": received_data.timezone_offset,
                        "created_at": func.now(),
                        "country": received_data.country,
                        "settings_json": {
                            "language": received_data.language if received_data.language else "en",
                            "simulation_language": received_data.language if received_data.language else "en",
                            "base_currency": {
                                "name": "Euro",
                                "code": "EUR",
                                "symbol": "€",
                                "default": "true"
                            }
                        }
                    },
                    commit=False
                )
                # update company name if exist in request
                customer_account_info = crud.account_information.get_first_where_array(
                    db=db,
                    where=[{"key": "customer_id", "value": invited_user.id, "operator": "=="}]
                )
                if received_data.company_name:
                    crud.account_information.update(
                        db=db,
                        db_obj=customer_account_info,
                        obj_in={
                            "company_name": received_data.company_name
                        },
                        commit=False
                    )

                db_user = {
                    "customer": custom_jsonable_encoder(invited_user),
                    "account_information": custom_jsonable_encoder(customer_account_info)
                }
                # print("db_user eto 2")
            else:
                # print("db_user eto 3")

                db_user = self.create_user_in_db(db=db, received_data=received_data)
                # print("db_user eto 4")

            # print("customer", db_user["customer"]["id"])
            customer = db_user["customer"]
            customer_id = db_user["customer"]["id"]
            # print("customer_id", customer_id)

            payload = self.construct_payload(received_data)
            response = None
            if verification == True:
                print("verification==True")
                response = self.send_signup_request(payload)
                print("response", response)
            else:
                print("verification==False")
                body = {
                    "firstname": payload["firstname"],
                    "lastname": payload["lastname"],
                    "username": payload["username"],
                    "pseudo": payload["pseudo"],
                    "password": payload["password"],
                    "roleId": settings.CUSTOMER_ROLE_ID,
                    "appId": settings.PVGIS_APP_ID,
                    "language": payload["language"]
                }
                response = requests.post(
                    f"{settings.AUTH_API_URL}/user/social-networks/signup",
                    json=body,
                    headers=settings.x_app_key_header,
                )
                print("response", response.json())

            auth_response = self.handle_response(response)
            auth_user_id = None
            if verification == True:
                auth_user_id = auth_response["data"]["userCredential"]["ui"]
            else:
                auth_user_id = auth_response["userCreated"]["id"]

            print("auth_user_id", auth_user_id)

            updated_customer, updated_account_information = self.update_user_with_auth_id(db, customer_id, auth_user_id)
            print("updated_customer", jsonable_encoder(updated_customer))
            print("updated_account_information", updated_account_information)

            db_user["customer"] = jsonable_encoder(updated_customer)
            db_user["account_information"] = jsonable_encoder(updated_account_information)

            # If everything is successful, commit the transaction
            db.commit()

            return db_user

        except Exception as e:
            print("error main_function", e)
            # If any error occurs, roll back the transaction
            db.rollback()
            # raise HTTPException(status_code=500, detail=f"An error occurred: {str(e)}")
            raise e
        finally:
            # Ensure the session is closed
            db.close()

    def google_auth_callback(self, db: Session, code, state: Any):
        if state:
            params = urllib.parse.parse_qs(state)
            lang = params.get("lang", ["en"])[0]  # Default to 'en' if not present
            country_code = params.get("country_code", [None])[0]  # Default to None if not present
        try:
            flow = Flow.from_client_config(
                CLIENT_CONFIG,
                scopes=[
                    "openid",
                    "https://www.googleapis.com/auth/userinfo.email",
                    "https://www.googleapis.com/auth/userinfo.profile",
                ],
                redirect_uri=settings.get_google_auth_url(),
            )
        except ValueError as ve:
            raise HTTPException(
                status_code=500, detail=f"Invalid client configuration: {str(ve)}"
            )
        try:
            flow.fetch_token(code=code)
        except GoogleAuthError as ge:
            raise HTTPException(status_code=500, detail=f"Error fetching token: {str(ge)}")

        credentials = flow.credentials

        try:
            id_info = id_token.verify_oauth2_token(
                credentials.id_token,
                reqs.Request(),
                credentials.client_id,
                clock_skew_in_seconds=10,
            )
        except ValueError as ve:
            raise HTTPException(status_code=500, detail=f"Invalid token: {str(ve)}")

        if "email" not in id_info:
            raise HTTPException("Email not found in user info")

        country = crud.country.get_by_code_alpha_2(
            db=db,
            code_alpha_2=country_code
        )

        print("country", country)

        try:
            # Start transaction
            db.begin(subtransactions=True)

            invited_user = crud.customer.get_first_where_array_v2(
                db=db,
                where=[
                    {"key": "email", "operator": "==", "value": id_info["email"]},
                    {"key": "auth_user_id", "operator": "isNull"}
                ]
            )
            created_customer = None

            if invited_user:
                print("if invited_user", invited_user)
                # Update invited user with new information
                created_customer = crud.customer.update(
                    db=db,
                    db_obj=invited_user,
                    obj_in={
                        "first_name": id_info.get("given_name", ""),
                        "last_name": id_info.get("family_name", ""),
                        "pseudo": id_info.get("given_name", "") + id_info.get("family_name", ""),
                        "accept_cgu": True,
                        "cart_reference": None,
                        "settings_json": {
                            "language": lang if lang else "en",
                            "simulation_language": lang if lang else "en",
                            "base_currency": {
                                "name": "Euro",
                                "code": "EUR",
                                "symbol": "€",
                                "default": "true"
                            }
                        }
                    },
                    commit=False
                )
            else:
                print("else not invited_user")
                # Create customer in local database first
                user_in = CustomerCreate(
                    first_name=id_info.get("given_name", ""),  # Add fallback
                    last_name=id_info.get("family_name", ""),  # Add fallback
                    email=id_info["email"],
                    timezone_offset=get_timezone_offset(country.code_alpha_2) if country else None,
                    country_id=country.id if country else None,
                    country=country.name if country else None,
                    settings_json={
                        "language": lang if lang else "en",
                        "simulation_language": lang if lang else "en",
                        "base_currency": {
                            "name": "Euro",
                            "code": "EUR",
                            "symbol": "€",
                            "default": "true"
                        }
                    },
                    default_referential_info_json=DEFAULT_REFERENTIAL_DATA,
                )
                created_customer = crud.customer.create(
                    db=db,
                    obj_in=user_in,
                    commit=False,
                    refresh=False,
                )
            user = {
                "firstname": created_customer.first_name,
                "lastname": created_customer.last_name,
                "username": created_customer.email,
                "appId": settings.PVGIS_APP_ID,
                "roleId": settings.CUSTOMER_ROLE_ID,
                "language": lang,
                "sentByApp": settings.APP_KEY,
                "sentByProcess": "registration",
            }

            response = requests.post(
                f"{settings.AUTH_API_URL}/user/social-networks/signup",
                json=user,
                headers=settings.x_app_key_header,
            )
            if response.status_code != 201:
                db.rollback()
                error_detail = response.json()

                print("ato ary izy no mandlao", response.json())
                
                # Enable when using google auth
                # db = SessionLocal()
                # return RedirectResponse(
                #     url=f"{settings.PVGIS_UI_URL}/register?errorMessage={str(error_detail['message'])}"
                # )
                auth_user = user
            else:
                res_auth = response.json()
                auth_user = res_auth["userCreated"]
                print("auth_user", auth_user)
                auth_user_id = str(auth_user["id"])

                try:
                    # Update customer with auth_user_id
                    print("========================")
                    print(created_customer)
                    print("auth_user_id", auth_user_id)
                    print({"auth_user_id": auth_user["id"]})
                    print("========================")
                    crud.customer.update(
                        db=db,
                        db_obj=created_customer,
                        obj_in={"auth_user_id": auth_user["id"]},
                        commit=False,
                    )
                except Exception as e:
                    print(f"Failed to update customer: {str(e)}")
                    raise HTTPException(status_code=500, detail="Failed to update customer")

                try:
                    crud.account_notification_setting.check_all_by_customer_id(
                        db=db, customer_id=created_customer.id
                    )
                    print(
                        f"Checked account notification settings for customer_id: {created_customer.id}")  # Print after checking notification settings
                except Exception as e:
                    print(f"Failed to check account notification settings: {str(e)}")
                    raise HTTPException(status_code=500, detail="Failed to check account notification settings")

                try:
                    account_information_in = AccountInformationCreate(
                        customer_id=created_customer.id,
                        account_type_id=2,  # default as particular
                    )

                    crud.account_information.create(
                        db=db,
                        obj_in=account_information_in,
                        user_id=auth_user["id"],
                        commit=False,
                        refresh=False,
                    )
                    print(
                        f"Created account information for customer_id: {created_customer.id}")  # Print after creating account information
                except Exception as e:
                    print(f"Failed to create account information: {str(e)}")
                    raise HTTPException(status_code=500, detail="Failed to create account information")

                users = [
                    {
                        "id": auth_user["id"],  # Convert to string to ensure serialization
                        "roleId": 2,
                        "appKey": "**********",
                        "vars": {
                            "name": created_customer.first_name or "",
                            "surname": created_customer.last_name or "",
                            "username": created_customer.email or "",
                            "lang": lang  # Convert to string to ensure correct formt
                        }
                    }
                ]

                # Commit transaction
                db.commit()
                print("Transaction committed.")  # Print after committing transaction

                try:
                    send_notif(event_code="first-registration-welcome", users=users, mailLanguage=lang)
                except Exception as e:
                    print(f"Notification error: {str(e)}")  # Log but don't fail registration

        except SQLAlchemyError as e:
            db.rollback()
            print(e)
            return RedirectResponse(
                url=f"{settings.PVGIS_UI_URL}/register?errorMessage=Database error occurred"
            )
        except Exception as e:
            print("========================")
            print(e)
            print("========================")
            db.rollback()
            return RedirectResponse(
                url=f"{settings.PVGIS_UI_URL}/register?errorMessage=1{str(e)}"
            )

        try:
            authenticate_user = crud.login.social_authenticate(
                db=db, username=auth_user["username"]
            )

            created_customer = crud.customer.get_first_where_array_v2(
                db=db,
                where=[
                    {
                        "key": "email",
                        "operator": "==",
                        "value": id_info["email"],
                    },
                ],
            )

            if created_customer:
                crud.account_notification_setting.check_all_by_customer_id(
                    db=db, customer_id=created_customer.id
                )
        except Exception as e:
            return RedirectResponse(
                url=f"{settings.PVGIS_UI_URL}/login?errorMessage=2{str(e)}"
            )

        token = jwt.encode(
            {"data": authenticate_user['data']['token']}, settings.SECRET_KEY, algorithm="HS256"
        )

        return RedirectResponse(url=f"{settings.PVGIS_UI_URL}?token={token}")

    def create_user_to_auth_without_mail(
            self,
            auth_payload: Any
    ):

        # example auth payload
        # auth_payload={
        #     "firstname": auth_payload.first_name,
        #     "lastname": auth_payload.last_name,
        #     "username": auth_payload.email,
        #     "password": auth_payload.password,
        #     "appId": settings.PVGIS_APP_ID,
        #     "roleId": settings.CUSTOMER_ROLE_ID
        # }

        # create to auth
        auth_res = requests.post(f"{settings.AUTH_API_URL}/user/full", json=auth_payload, headers={'Notifinfo': '{}'})
        auth_res_json = auth_res.json()
        if auth_res.status_code == 201:
            return auth_res_json['userCreated']['id']
        else:
            print('Auth user creation error')
            print(auth_res_json)
            raise HTTPException(400, detail='Error when creating a user')

    def update_user_profile(
            self,
            auth_user_id,
            auth_payload: Any
    ):
        # create to auth
        auth_res = requests.patch(f"{settings.AUTH_API_URL}/user-profile/by-user-id/{auth_user_id}", json=auth_payload,
                                  headers={'Notifinfo': '{}'})
        auth_res_json = auth_res.json()
        if auth_res.status_code in [200, 201, 202, 203, 204, 205, 206]:
            return {"message": "profile_updated"}
        else:
            print('Auth profile update error')
            print(auth_res_json)
            print(auth_res.status_code)
            raise HTTPException(400, detail='Error when updating a user')

    def update_user_password(self, username, newPassword):
        # create to auth
        auth_res = requests.post(f"{settings.AUTH_API_URL}/user/update-password/no-hash/{username}",
                                 json={
                                     "newPassword": newPassword
                                 }, )
        auth_res_json = auth_res.json()
        if auth_res.status_code in [200, 201, 202, 203, 204, 205, 206]:
            return {"message": "profile_updated"}
        else:
            print('Auth password update error')
            print(username)
            print(auth_res_json)
            raise HTTPException(400, detail='Error when creating a user')


login = CRUDUser(Customer)
