from app.crud.base import CRUDBase
from app.models.professional_category import ProfessionalCategory

from app.schemas.professional_category import ProfessionalCategoryCreate, ProfessionalCategoryUpdate


class CRUDProfessionalCategory(CRUDBase[ProfessionalCategory, ProfessionalCategoryCreate, ProfessionalCategoryUpdate]):
    pass


professional_category = CRUDProfessionalCategory(ProfessionalCategory)
