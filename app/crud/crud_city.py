from app.crud.base import CRUDBase
from app.crud.ip_location_service import get_google_place_ids_by_city_ip
from app.models.city import City
from sqlalchemy.orm import Session
from app.schemas.city import CityCreate, CityUpdate
from sqlalchemy.sql import text

class CRUDCity(CRUDBase[City, CityCreate, CityUpdate]):
    pass

    def get_one_by_ip(self, db: Session, ip:str): 
        google_place_ids = get_google_place_ids_by_city_ip(db, ip)
        return db.query(City).filter(City.google_place_id.in_(google_place_ids)).all()


city = CRUDCity(City)
