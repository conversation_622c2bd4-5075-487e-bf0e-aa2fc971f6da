from typing import Optional, Any
from requests import Session
from app.crud.base import CRUDBase
from app.models.region import Region
from app.schemas.region import RegionCreate, RegionUpdate

class CRUDRegion(CRUDBase[Region, RegionCreate, RegionUpdate]):
    def get_by_short_name(self, db: Session, short_name: str, normalized_name_param: str) -> Optional[Region]:
        return self.get_first_where_array_v2(
            db=db,
            where=[
                {"key": "short_code", "value": short_name, "operator": "=="},
                {"key": "normalized_name", "value": f"%{normalized_name_param}%", "operator": "like"}
            ]
        )

    def get_country_by_region_id(self, db: Session, region_id: int) -> Optional[Region]:
        return self.get_first_where_array_v2(
            db=db,
            where=[{"key": "id", "value": region_id, "operator": "=="}]
        )


region = CRUDRegion(Region)
