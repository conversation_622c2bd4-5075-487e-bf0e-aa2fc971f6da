from app.crud.base import CRUDBase
from app.crud.ip_location_service import get_google_place_ids_by_city_ip
from app.models.customer_email_campaign import CustomerEmailCampaign
from sqlalchemy.orm import Session
from app.schemas.customer_email_campaign import CustomerEmail<PERSON>amp<PERSON>gn<PERSON><PERSON>, CustomerEmailCampaignUpdate
from sqlalchemy.sql import text
import pandas as pd
from openpyxl import load_workbook
from openpyxl.styles import Pattern<PERSON>ill, Font, Alignment
import io

class CRUDCustomerEmailCampaign(CRUDBase[CustomerEmailCampaign, CustomerEmailCampaignCreate, CustomerEmailCampaignUpdate]):
    def get_mail_campaign_report_file(self, db: Session) -> bytes:
        # SQL query as a string
        sql_query = """
        SELECT
            cu.email,
            cu.created_at registration,
            coalesce(co.name, cu.country) country,
            cei1.sent_at email_1,
            cei2.sent_at email_2,
            cei3.sent_at email_3,
            cei4.sent_at email_4,
            cei5.sent_at email_5,
            case when
                    s.disabled_at is not null
                    or s.is_auto_renew = FALSE
                    or s.subscription_status = 'INACTIVE'
                then coalesce(s.disabled_at, s.updated_at)
                else NULL
            END unsubscribed,
            COALESCE (cu.to_delete_at, cu.deleted_at) unregistered,
            case when
                    not(s.disabled_at is not null
                    or s.is_auto_renew = FALSE
                    or s.subscription_status = 'INACTIVE')
                    and s.product_id = 1
                then
                    s.created_at
                else null
            end prime,
            case when
                    not(s.disabled_at is not null
                    or s.is_auto_renew = FALSE
                    or s.subscription_status = 'INACTIVE')
                    and s.product_id in (2,8)
                then
                    s.created_at
                else null
            end premium,
            case when
                    not(s.disabled_at is not null
                    or s.is_auto_renew = FALSE
                    or s.subscription_status = 'INACTIVE')
                    and s.product_id in (3,9)
                then
                    s.created_at
                else null
            end pro,
            case when
                    not(s.disabled_at is not null
                    or s.is_auto_renew = FALSE
                    or s.subscription_status = 'INACTIVE')
                    and s.product_id in (4,10)
                then
                    s.created_at
                else null
            end expert
        from customer cu
        left join country co on co.id = cu.country_id
        left join customer_email_item cei1 on cei1.customer_id  = cu.id and cei1.customer_email_task_id = 1 and cei1.sent_at is not null
        left join customer_email_item cei2 on cei2.customer_id  = cu.id and cei2.customer_email_task_id = 2 and cei2.sent_at is not null
        left join customer_email_item cei3 on cei3.customer_id  = cu.id and cei3.customer_email_task_id = 3 and cei3.sent_at is not null
        left join customer_email_item cei4 on cei4.customer_id  = cu.id and cei4.customer_email_task_id = 4 and cei4.sent_at is not null
        left join customer_email_item cei5 on cei5.customer_id  = cu.id and cei5.customer_email_task_id = 5 and cei5.sent_at is not null
        left join (
            select subs.id, subs.customer_id, subs.product_id, subs.subscription_status, subs.is_auto_renew, subs.disabled_at, subs.updated_at, subs.created_at
            from subscription subs
            join (
                select s.customer_id, max(s.updated_at) updated_at
                from subscription s
                group by s.customer_id
            ) recent_sub_date on subs.customer_id = recent_sub_date.customer_id and subs.updated_at = recent_sub_date.updated_at
        ) s on s.customer_id = cu.id
        """
        
        # Execute query and fetch results
        result = db.execute(text(sql_query))
        rows = result.fetchall()
        
        # Create DataFrame from results
        df = pd.DataFrame(rows, columns=[
            'MAILS', 'REGISTRATION DATE', 'COUNTRY', 'MAIL 1', 'MAIL 2', 'MAIL 3', 
            'MAIL 4', 'MAIL 5', 'UNSUBSCRIBED', 'UNREGISTERED', 'PRIME', 
            'PREMIUM', 'PRO', 'EXPERT'
        ])
        
        # Format dates to DD/MM/YYYY
        date_columns = ['REGISTRATION DATE', 'MAIL 1', 'MAIL 2', 'MAIL 3', 'MAIL 4', 'MAIL 5', 
                       'UNSUBSCRIBED', 'UNREGISTERED', 'PRIME', 'PREMIUM', 'PRO', 'EXPERT']
        for col in date_columns:
            df[col] = pd.to_datetime(df[col]).dt.strftime('%d/%m/%Y')
        
        # Calculate totals
        totals = {
            'MAIL 1': df['MAIL 1'].notna().sum(),
            'MAIL 2': df['MAIL 2'].notna().sum(),
            'MAIL 3': df['MAIL 3'].notna().sum(),
            'MAIL 4': df['MAIL 4'].notna().sum(),
            'MAIL 5': df['MAIL 5'].notna().sum(),
            'UNSUBSCRIBED': df['UNSUBSCRIBED'].notna().sum(),
            'UNREGISTERED': df['UNREGISTERED'].notna().sum(),
            'PRIME': df['PRIME'].notna().sum(),
            'PREMIUM': df['PREMIUM'].notna().sum(),
            'PRO': df['PRO'].notna().sum(),
            'EXPERT': df['EXPERT'].notna().sum()
        }
        
        # Create Excel file in memory
        output_bytes = io.BytesIO()
        
        # Write to Excel with formatting
        with pd.ExcelWriter(output_bytes, engine="openpyxl") as writer:
            # Write the main data starting from row 3
            df.to_excel(writer, sheet_name="PVGIS registered user mailing report", index=False, startrow=2)
            
            # Get the workbook and worksheet
            workbook = writer.book
            worksheet = writer.sheets["PVGIS registered user mailing report"]
            
            # Define styles
            green_fill = PatternFill(start_color='92D050', end_color='92D050', fill_type='solid')
            blue_fill = PatternFill(start_color='00B0F0', end_color='00B0F0', fill_type='solid')
            
            # Write total registered in row 1
            worksheet.cell(row=1, column=1, value="Total registered")
            worksheet.cell(row=2, column=1, value=len(df))
            
            # Write TOTAL with colspan (merge cells)
            worksheet.cell(row=1, column=4, value="TOTAL")
            worksheet.merge_cells(start_row=1, start_column=4, end_row=1, end_column=14)  # Merge cells D1:N1
            worksheet.cell(row=1, column=4).fill = blue_fill
            
            # Center the text in the merged cell
            worksheet.cell(row=1, column=4).alignment = Alignment(horizontal='center', vertical='center')
            
            # Fill the merged cells with blue background
            for col in range(4, 15):  # columns D through N
                worksheet.cell(row=1, column=col).fill = blue_fill
            
            # Write totals in row 2
            for col, total in totals.items():
                col_idx = df.columns.get_loc(col) + 1  # +1 for Excel's 1-based indexing
                worksheet.cell(row=2, column=col_idx, value=total)

            # Format headers (now in row 3)
            header_row = 3
            for col in range(len(df.columns)):
                cell = worksheet.cell(row=header_row, column=col + 1)
                if cell.value in totals.keys():
                    cell.fill = green_fill
            
            # Set column widths
            for col in range(len(df.columns)):
                worksheet.column_dimensions[chr(65 + col)].width = 15

            # Add auto-filter to data range
            worksheet.auto_filter.ref = f"A{header_row}:{chr(64 + len(df.columns))}{len(df) + header_row}"
        
        output_bytes.seek(0)
        return output_bytes

customer_email_campaign = CRUDCustomerEmailCampaign(CustomerEmailCampaign)
