from app.crud.base import CRUDBase
from app.models.ce_sender_email_campaign_country import CeSenderEmailCampaignCountry
from app.models.customer_email_campaign import CustomerEmailCampaign
from app.models.customer_email_item import CustomerEmailItem
from app.models.customer_email_task import CustomerEmailTask
from app.services.customer_email.customer_email_item_factories import factories, pre_sending_processes
from sqlalchemy.orm import joinedload, Session
from sqlalchemy import func
from app.schemas.customer_email_task import CustomerEmailTask<PERSON>reate, CustomerEmailTaskUpdate


class UDddCustomerEmailTask(CRUDBase[CustomerEmailTask, CustomerEmailTaskCreate, CustomerEmailTaskUpdate]):
    
    def load_all(self, db: Session) :
        all_tasks =  (
            db.query(CustomerEmailTask)
            .options(
                joinedload(CustomerEmailTask.customer_email_campaign, innerjoin= False)
                    .joinedload(CustomerEmailCampaign.ce_sender_email_campaign_countries, innerjoin=False)
                    .joinedload(CeSenderEmailCampaignCountry.customer_email_sender, innerjoin=False),
                joinedload(CustomerEmailTask.customer_email_campaign, innerjoin= False)
                    .joinedload(CustomerEmailCampaign.ce_campaign_country_data, innerjoin=False) 
            )
            .all())
        for task in all_tasks:
            task.generate_customer_email_items = factories[task.key]
            task.pre_sending_process = pre_sending_processes[task.key] if task.key in pre_sending_processes else None
        return all_tasks 
    
    def load_by_key (self, db: Session, task_key: str):
        task =  (
            db.query(CustomerEmailTask)
            .options(
                joinedload(CustomerEmailTask.customer_email_campaign, innerjoin= False)
                    .joinedload(CustomerEmailCampaign.ce_sender_email_campaign_countries, innerjoin=False)
                    .joinedload(CeSenderEmailCampaignCountry.customer_email_sender, innerjoin=False),
                joinedload(CustomerEmailTask.customer_email_campaign, innerjoin= False)
                    .joinedload(CustomerEmailCampaign.ce_campaign_country_data, innerjoin=False) 
            )
            .filter(CustomerEmailTask.key == task_key)
            .first())
        if task:
            task.generate_customer_email_items = factories[task.key]
            task.pre_sending_process = pre_sending_processes[task.key] if task.key in pre_sending_processes else None
        return task

    
customer_email_task = UDddCustomerEmailTask(CustomerEmailTask)
