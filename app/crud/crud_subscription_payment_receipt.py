from app.models.subscription import Subscription
from app.models.subscription_payment_receipt import SubscriptionPaymentReceipt
from app.schemas.subscription_payment_receipt import SubscriptionPaymentReceiptCreate, SubscriptionPaymentReceiptUpdate
from app.utils.utils import send_email
from fastapi import HTTPException
from fastapi.encoders import jsonable_encoder
from app.crud.base import CRUDBase
from sqlalchemy.orm import Session
from sqlalchemy import func, text
from app.models.payment_method import PaymentMethod
 
from app import crud
from dateutil.relativedelta import relativedelta
from datetime import datetime
from app.enums.payment_status_enum import PaymentStatusEnum

from app.config.settings import get_settings
settings = get_settings()
class CRUDSubscriptionPaymentReceipt(
    CRUDBase[
        SubscriptionPaymentReceipt,
        SubscriptionPaymentReceiptCreate,
        SubscriptionPaymentReceiptUpdate,
    ]
):
    def fill_legacy_transactions(self, db: Session):
        db.execute("""
                   INSERT INTO subscription_payment_receipt 
                   (reference, stripe_object_id, subscription_stripe_id, subscription_payment_transaction_id) 
                   SELECT spt.stripe_object_id, spt.stripe_object_id, spt.subscription_stripe_id, spt.id 
                   FROM subscription_payment_transaction spt
                   LEFT JOIN subscription_payment_receipt spr ON 
                        spt.id = spr.subscription_payment_transaction_id
                        AND spt.stripe_object_id = spr.stripe_object_id
                   WHERE spr.id IS NULL
                   """)
         


subscription_payment_receipt = CRUDSubscriptionPaymentReceipt(
    SubscriptionPaymentReceipt
)
