import ast
from typing import Any
from fastapi import HTTPException
from sqlalchemy import and_, desc, func
from app import crud, schemas
from app.crud.base import CRUDBase
from sqlalchemy.orm import Session, load_only, joinedload

from app.enums.subscription_status_enum import SubscriptionStatusEnum
from app.models.customer import Customer
from app.models.simulation import Simulation
from app.models.subscription import Subscription
from app.models.subscription_user import SubscriptionUser
from app.schemas.simulation import SimulationCreate, SimulationUpdate
from fastapi.encoders import jsonable_encoder


class CRUDSimulation(CRUDBase[Simulation, SimulationCreate, SimulationUpdate]):

    def get_current_subscription_by_customer_auth_id(
            self, db: Session, auth_user_id: int
    ):
        if not auth_user_id:
            raise HTTPException(status_code=404, detail="missing auth user id")

        _filter = [
            # Subscription.disabled_at.is_(None),
            Subscription.deleted_at.is_(None),
            Subscription.subscription_status == SubscriptionStatusEnum.ACTIVE,
            Subscription.start_date <= func.now(),
            # Subscription.expired_date >= func.now(),
            Customer.auth_user_id == auth_user_id,
            Subscription.product_json != None
        ]

        query = (
            db.query(Subscription)
            .join(Customer, Customer.id == Subscription.customer_id)
            .filter(and_(*_filter))
            .order_by(Subscription.start_date.asc()) 
            .limit(1) 
        )

        # print(str(query.statement.compile(compile_kwargs={"literal_binds": True})))

        return query.scalar()

    def get_current_subscription_by_subscription_user_auth_id(
            self, db: Session, auth_user_id: int
    ):
        if not auth_user_id:
            raise HTTPException(status_code=404, detail="missing auth user id")

        _filter = [
            # Subscription.disabled_at.is_(None),
            Subscription.deleted_at.is_(None),
            Subscription.subscription_status == SubscriptionStatusEnum.ACTIVE,
            Subscription.start_date <= func.now(),
            # Subscription.expired_date >= func.now(),
            Customer.auth_user_id == auth_user_id,
            SubscriptionUser.auth_user_id == auth_user_id,
        ]

        return (
            db.query(Subscription)
            .select_from(Subscription)
            .join(SubscriptionUser)
            .filter(and_(*_filter))
            .scalar()
        )

    def create_simulation_and_deduct_subscription(
            self,
            db: Session,
            subscription: Any,
            simulation_in: schemas.SimulationCreate,
            current_user: Any,
    ):
        if subscription.subscription_status == SubscriptionStatusEnum.PVGIS24 or (subscription.credit_balance and subscription.credit_balance > 0):
            if subscription.id is None or subscription.id <= 0:
                raise HTTPException(status_code=400, detail="Invalid subscription ID")

            simulation_in.subscription_id = subscription.id  # Set the subscription ID

            connected_user_id = current_user.get("connectedUserId", current_user["id"])
            is_admin = ("isAdmin" in current_user) and (current_user["isAdmin"] == True)

            simulation = crud.simulation.create(
                db=db, obj_in=simulation_in, user_id=connected_user_id
            )
            # Deduct subscription credit balance if exist
            if subscription.credit_balance and not is_admin:
                subscription.credit_balance -= 1
            subscription.last_user_to_interact = connected_user_id

            try:
                db.commit()
            except Exception as _e:
                db.rollback()
                raise HTTPException(status_code=500, detail="Database error occurred")

            db.refresh(subscription)
            return simulation
        else:
            raise HTTPException(status_code=400, detail="Insufficient credit balance")

    def find_existing_simulation_by_name(
            self, db: Session, name: str
    ) -> Simulation:
        """
        Check if a simulation exists by exact name or by name without spaces.
        """
        exact_match = db.query(Simulation).filter(
            func.lower(Simulation.name) == func.lower(name)
        ).first()

        name_without_spaces = name.replace(" ", "")
        space_removed_match = db.query(Simulation).filter(
            func.lower(func.replace(Simulation.name, " ", "")) == func.lower(name_without_spaces)
        ).first()
        return exact_match or space_removed_match

    def convert_pvgis24_folder_to_financial(
            self,
            db: Session,
            subscription: Any,
            simulation_in: Any,
            user_id: int,
    ):
        if subscription.credit_balance > 0:
            if subscription.id is None or subscription.id <= 0:
                raise HTTPException(status_code=400, detail="pvgis.error.invalid_subscription")

            # update subscription id into simulation
            data = crud.simulation.get(db=db, id=simulation_in.id)
            simulation = crud.simulation.update(
                db=db,
                db_obj=data,
                obj_in={"subscription_id": subscription.id},
                user_id=user_id,
                commit=False
            )

            # Deduct subscription credit balance
            subscription.credit_balance -= 1
            subscription.last_user_to_interact = user_id

            try:
                db.commit()
            except Exception as _e:
                db.rollback()
                raise HTTPException(status_code=500, detail="pvgis.error.database_error")

            db.refresh(subscription)
            return simulation
        else:
            # raise HTTPException(status_code=400, detail="pvgis.error.insufficient_credit_balance")
            return { "message": "pvgis.error.insufficient_credit_balance" }

    def update_country_id(self, db: Session, simulation_id: int, country_id: int) -> Simulation:
        simulation = self.get(db=db, id=simulation_id)
        if not simulation:
            raise HTTPException(status_code=404, detail="Simulation not found")
        simulation.country_id = country_id
        db.commit()
        db.refresh(simulation)
        return simulation
    
    def get_simulation_count_by_customer_id(self, db: Session, customer_id: int):
        return (db.query(Simulation.id)
            .join(Subscription, Subscription.id == Simulation.subscription_id)
            .filter( 
                Subscription.customer_id == customer_id
            ).count())
         
    
simulation = CRUDSimulation(Simulation)
