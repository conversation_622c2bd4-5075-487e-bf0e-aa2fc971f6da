from typing import Optional, Any
from requests import Session
from app.crud.base import CRUDBase
from app.models.country import Country
from sqlalchemy import update, bindparam
from app.schemas.country import CountryCreate, CountryUpdate


class CRUDCountry(CRUDBase[Country, CountryCreate, CountryUpdate]):
    def get_by_code_alpha_2(self, db: Session, code_alpha_2: str):
        country = self.get_first_where_array_v2(
            db=db,
            where=[{"key": "code_alpha_2", "value": code_alpha_2, "operator": "=="}]
        )
        return country

    def get_by_id(self, db: Session, country_id: int):
        country = self.get_first_where_array_v2(
            db=db,
            where=[{"key": "id", "value": country_id, "operator": "=="}]
        )
        return country

    def bulk_update_continent_country(self, db: Session, country_updates: Any):
        country_update_states = (
            update(Country)
            .where(Country.id == bindparam('b_id'))
            .values(continent_id=bindparam('continent_id'))
        )
        
        # Ensure the data keys match the bindparam names
        updated_data = [
            {"b_id": item["id"], "continent_id": item["continent_id"]}
            for item in country_updates
        ]
        db.execute(country_update_states, updated_data)
        db.commit()

    def get_by_name(self, db: Session, name: str) -> Optional[Country]:
        return db.query(Country).filter(Country.name == name).first()
    
country = CRUDCountry(Country)
