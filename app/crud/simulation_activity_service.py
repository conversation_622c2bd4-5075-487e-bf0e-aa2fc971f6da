import os
import copy
from app.crud.ip_location_service import get_location_from_ip
import requests
from dotenv import load_dotenv, find_dotenv
from requests.auth import HTTP<PERSON>asic<PERSON>uth
from datetime import datetime
import random
import string


load_dotenv(find_dotenv())
logstash_url = os.environ.get("IP_LOCATION_URL")
elastic_index = os.environ.get("ELASTIC_INDEX")
elastic_username = os.environ.get("ELASTIC_USERNAME")
elastic_password = os.environ.get("ELASTIC_PASSWORD")


def save_simulation(request_body):
    request_body = request_body.__dict__
    simulation_data = request_body["additional_data"]
    del request_body["additional_data"]

    # User location
    simulation_data["user_location"] = get_location_from_ip(simulation_data["user_ip"])

    simulation_data["simulation_params"] = request_body
    simulation_data["timestamp"] = datetime.now().isoformat()

    try:

        response = requests.post(
            os.getenv("LOGSTASH_URL") + f"/{elastic_index}/_doc",
            json=simulation_data,
            auth=HTTPBasicAuth(elastic_username, elastic_password),
        )
        if response.status_code in [200, 201]:
            print("sending log successful")
        else:
            print("sending log failed with status code:", response.status_code)
    except Exception as ex:
        print("We have problem with logger but not blocking ")
        print(ex)


def generate_user_id():
    generated_id = datetime.now().strftime("%Y%m%d%H%M%S%f") + generate_random_string(6)
    return {"uid": generated_id}


def generate_random_string(length: int) -> str:
    characters = string.ascii_letters + string.digits
    random_string = "".join(random.choices(characters, k=length))
    return random_string
