from app import crud
from app.crud.base import CRUDBase
from app.models.ip_location import IpLocation
from sqlalchemy.orm import Session
from app.crud.ip_location_service import get_location_from_ip

from app.schemas.ip_location import IpLocationCreate, IpLocationUpdate
from fastapi.encoders import jsonable_encoder


class CRUDIpLocation(CRUDBase[IpLocation, IpLocationCreate, IpLocationUpdate]):
    def get_location_by_ip(self, db: Session, ip: str):
        ip_location_info = jsonable_encoder(crud.ip_location.get_first_where_array_v2(
            db=db,
            where=[{"key":"ip","operator":"==","value":ip}],
            base_columns=["data_json"],
        ))
        if(ip_location_info is None):
            data = get_location_from_ip(ip)
            if data["status"] == "success": 
                crud.ip_location.create(
                    db=db, obj_in={"ip":ip, "data_json":data}
                )
            response = data
        else:
            response = ip_location_info["data_json"]
       
        return response


ip_location = CRUDIpLocation(IpLocation)
