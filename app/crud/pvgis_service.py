from datetime import datetime,timedelta
import io
import os
import json
from pathlib import Path


from fastapi.responses import FileResponse, PlainTextResponse
import requests
import pandas as pd
from pvlib.iotools import read_epw, parse_epw
import warnings
import traceback
from pvlib._deprecation import pvlibDeprecationWarning
from dotenv import load_dotenv, find_dotenv
import base64


from app.crud.simulation_activity_service import save_simulation
from app.enums.output_format_enum import OutputFormatEnum
from app.schemas.dtos.pvgis_daily_dto import PvgisDailyDto
from app.schemas.dtos.pvgis_degreedays_dto import PvgisDegreedaysDto
from app.schemas.dtos.pvgis_extent_dto import PvgisExtentDto
from app.schemas.dtos.pvgis_grid_connected_or_tracking_dto import (
    PvgisGridConnectedOrTrackingDto,
)
from app.schemas.dtos.pvgis_horizon_profile_dto import PvgisHorizonProfileDto
from app.schemas.dtos.pvgis_hourly_dto import PvgisHourlyDto
from app.schemas.dtos.pvgis_monthly_dto import PvgisMonthlyDto
from app.schemas.dtos.pvgis_offgrid_dto import PvgisOffgridDto
from app.schemas.dtos.pvgis_tmy_dto import PvgisTmyDto

load_dotenv(find_dotenv())
import jwt
from app.config.settings import get_settings
settings = get_settings()


 
pvgis_hourly_rad_key = os.environ.get("PVGIS_HOURLY_RAD_ROUTE")
pvgis_daily_rad_key = os.environ.get("PVGIS_DAILY_RAD_ROUTE")
pvgis_monthly_rad_key = os.environ.get("PVGIS_MONTHLY_RAD_ROUTE")
pvgis_tym_key = os.environ.get("PVGIS_TMY_ROUTE")
pvgis_offgrid_key = os.environ.get("PVGIS_OFFGRID_ROUTE")
pvgis_grid_connected_or_tracking_key = os.environ.get(
    "PVGIS_GRID_CONNECTED_OR_TRACKING_ROUTE"
)
pvgis_horizon_profile_key = os.environ.get("PVGIS_HORIZON_PROFILE_ROUTE")
pvgis_degreedays_key = os.environ.get("PVGIS_DEGREEDAYS_ROUTE")
pvgis_extent_key = os.environ.get("PVGIS_EXTENT_ROUTE")


def handle_response(text: str, outputformat: str, browser: int):
    resultBody = text
    if outputformat == OutputFormatEnum.JSON and not browser:
        resultBody = json.load(io.StringIO(text))

    if browser:
        extension = (
            "json"
            if outputformat == OutputFormatEnum.JSON
            else ("csv" if outputformat == OutputFormatEnum.CSV else "txt")
        )
        resultBody = PlainTextResponse(
            resultBody,
            media_type="text/plain",
            headers={
                "Content-Disposition": "attachment; filename=downloaded_file."
                + extension
            },
        )
    return resultBody


def get_pvgis_grid_connected_or_tracking(api_version: str, dto: PvgisGridConnectedOrTrackingDto):
    (has_same_payload, same_payload_res) = handle_same_payload(dto)
    if has_same_payload : 
        return same_payload_res
        
    params = dto.dict()
    pvgis_api_url = settings.get_pvgis_api_url() 
    base_url = pvgis_api_url.replace(r'{api_version}', api_version)
    res = requests.get(
        base_url + "/" + pvgis_grid_connected_or_tracking_key,
        params=params,
        timeout=30,
    )

    if not res.ok:
        try:
            err_msg = res.json()
        except Exception as e:
            traceback.print_exc()
            res.raise_for_status()
        else:
            raise requests.HTTPError(err_msg["message"])

    

    return handle_response(res.text, dto.outputformat, dto.browser)


def get_pvgis_hourly(api_version: str, dto: PvgisHourlyDto):
    
    (has_same_payload, same_payload_res) = handle_same_payload(dto)
    if has_same_payload : 
        return same_payload_res
    
    pvgis_api_url = settings.get_pvgis_api_url()
    base_url = pvgis_api_url.replace(r'{api_version}', api_version)
    params = dto.dict()
    res = requests.get(
        base_url + "/" + pvgis_hourly_rad_key, params=params, timeout=30
    )

    if not res.ok:
        try:
            err_msg = res.json()
        except Exception as e:
            traceback.print_exc()
            res.raise_for_status()
        else:
            raise requests.HTTPError(err_msg["message"])

     

    return handle_response(res.text, dto.outputformat, dto.browser)


def get_pvgis_daily(api_version: str, dto: PvgisDailyDto):
    
    (has_same_payload, same_payload_res) = handle_same_payload(dto)
    if has_same_payload : 
        return same_payload_res
    pvgis_api_url = settings.get_pvgis_api_url()
    base_url = pvgis_api_url.replace(r'{api_version}', api_version)
    params = dto.dict()
    if "isGlobal" in params:
        params["global"] = params["isGlobal"]
        del params["isGlobal"]

    res = requests.get(
        base_url + "/" + pvgis_daily_rad_key, params=params, timeout=30
    )

    if not res.ok:
        try:
            err_msg = res.json()
        except Exception as e:
            traceback.print_exc()
            res.raise_for_status()
        else:
            print("error: ", err_msg)
            raise requests.HTTPError(err_msg["message"])

  

    return handle_response(res.text, dto.outputformat, dto.browser)


def get_pvgis_monthly(api_version: str, dto: PvgisMonthlyDto):
    
    (has_same_payload, same_payload_res) = handle_same_payload(dto)
    if has_same_payload : 
        return same_payload_res
    pvgis_api_url = settings.get_pvgis_api_url()
    base_url = pvgis_api_url.replace(r'{api_version}', api_version)
    params = dto.dict()
    res = requests.get(
        base_url + "/" + pvgis_monthly_rad_key, params=params, timeout=30
    )

    if not res.ok:
        try:
            err_msg = res.json()
        except Exception as e:
            traceback.print_exc()
            res.raise_for_status()
        else:
            raise requests.HTTPError(err_msg["message"])

  
    return handle_response(res.text, dto.outputformat, dto.browser)


def get_pvgis_tmy(api_version: str,dto: PvgisTmyDto):
    
    print("dto eto: ",api_version, dto)
    
    (has_same_payload, same_payload_res) = handle_same_payload(dto)
    if has_same_payload : 
        return same_payload_res
    pvgis_api_url = settings.get_pvgis_api_url()
    base_url = pvgis_api_url.replace(r'{api_version}', api_version)
    params = dto.dict()
    
    print("params eto: ",api_version, params)
    res = requests.get(base_url + "/" + pvgis_tym_key, params=params, timeout=30)

    if not res.ok:
        try:
            err_msg = res.json()
        except Exception as e:
            traceback.print_exc()
            res.raise_for_status()
        else:
            raise requests.HTTPError(err_msg["message"])

     

    return handle_response(res.text, dto.outputformat, dto.browser)


def get_pvgis_offgrid(api_version: str,dto: PvgisOffgridDto):
    (has_same_payload, same_payload_res) = handle_same_payload(dto)
    if has_same_payload : 
        return same_payload_res
    pvgis_api_url = settings.get_pvgis_api_url()
    base_url = pvgis_api_url.replace(r'{api_version}', api_version)
    params = dto.dict()
    res = requests.get(
        base_url + "/" + pvgis_offgrid_key, params=params, timeout=30
    )

    if not res.ok:
        try:
            err_msg = res.json()
        except Exception as e:
            traceback.print_exc()
            res.raise_for_status()
        else:
            raise requests.HTTPError(err_msg["message"])

 

    return handle_response(res.text, dto.outputformat, dto.browser)


def get_horizon_profile(api_version: str,dto: PvgisHorizonProfileDto):
    pvgis_api_url = settings.get_pvgis_api_url()
    base_url = pvgis_api_url.replace(r'{api_version}', api_version)
    params = dto.dict()
    res = requests.get(
        base_url + "/" + pvgis_horizon_profile_key, params=params, timeout=30
    )
    if not res.ok:
        try:
            err_msg = res.json()
        except Exception as e:
            traceback.print_exc()
            res.raise_for_status()
        else:
            raise requests.HTTPError(err_msg["message"])

    return handle_response(res.text, dto.outputformat, dto.browser)


def get_degreedays(api_version: str,dto: PvgisDegreedaysDto):
    pvgis_api_url = settings.get_pvgis_api_url()
    base_url = pvgis_api_url.replace(r'{api_version}', api_version)
    params = dto.dict()
    res = requests.get(
        base_url + "/" + pvgis_degreedays_key, params=params, timeout=30
    )
    if not res.ok:
        try:
            err_msg = res.json()
        except Exception as e:
            traceback.print_exc()
            res.raise_for_status()
        else:
            raise requests.HTTPError(err_msg["message"])
    # Create a DataFrame from the CSV data
    df = pd.read_csv(io.StringIO(res.text))

    # Convert the DataFrame to JSON
    json_data = df.to_dict(orient="records")
    return json_data[0]


def get_extent_db(api_version: str,dto: PvgisExtentDto):
    pvgis_api_url = settings.get_pvgis_api_url()
    base_url = pvgis_api_url.replace(r'{api_version}', api_version)
    params = dto.dict()
    res = requests.get(
        base_url + "/" + pvgis_extent_key, params=params, timeout=30
    )
    if not res.ok:
        try:
            err_msg = res.json()
        except Exception as e:
            traceback.print_exc()
            res.raise_for_status()
        else:
            raise requests.HTTPError(err_msg["message"])
    values_list = res.text.strip().split("\n")

    # Convert the values to integers
    integer_array = [int(value) for value in values_list]
    return integer_array

def generatePaymentReference(paymentTransationId):
    # Get the current year and month
    now = datetime.now()
    year = now.year
    month = now.month
    month_str = f"{month:02}"
    number_str = f"{paymentTransationId:04}"
    # Combine to form the reference
    reference = f"{year}-{month_str}-{number_str}"
    return reference

def create_token(data, expires_in_days=30):
    APP_SECRETE_KEY =settings.APP_SECRET_KEY
    expiration_time = datetime.utcnow() + timedelta(days=expires_in_days)
    payload = {
        **data,
        "exp": expiration_time
    }
    token = jwt.encode(payload, APP_SECRETE_KEY, algorithm="HS256")
    encoded_token = base64.b64encode(token.encode('utf-8')).decode('utf-8')
    return encoded_token

def verify_token(token):
    APP_SECRETE_KEY =settings.APP_SECRET_KEY
    try:
        decoded_bytes = base64.b64decode(token)
        decoded_token = decoded_bytes.decode('utf-8')
        decoded_data = jwt.decode(decoded_token, APP_SECRETE_KEY, algorithms=["HS256"])
        return decoded_data  # Token is valid
    except jwt.ExpiredSignatureError as e:
        print("Token has expired")
        raise e
    except jwt.InvalidTokenError as e:
        print("Invalid token")
        raise e
        
        

def handle_same_payload(dto):
    try:
        dto_dict = dto.__dict__
        if dto_dict.get('additional_data', None) : 
            has_same_payload = dto_dict['additional_data']['has_same_payload']
            save_simulation(dto)
            if has_same_payload :
                return( True, handle_response('{}', dto.outputformat, dto.browser))
    except Exception as e:
        traceback.print_exc()
    return (False, None)