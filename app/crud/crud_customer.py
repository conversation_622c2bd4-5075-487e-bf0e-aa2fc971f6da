import ast
from datetime import datetime
import io
from typing import Optional

import pandas as pd

from app.crud.base import CRUDBase
from app.enums.subscription_status_enum import SubscriptionStatusEnum
from app.events import customer
from app.models.account_information import AccountInformation
from app.models.account_type import AccountType
from app.models.customer import Customer
from app.models.product import Product
from app.models.subscription import Subscription
from sqlalchemy.orm import Session, joinedload
from app.schemas.customer import CustomerCreate, CustomerUpdate
import requests
from app.config.settings import get_settings
from sqlalchemy import func, distinct, or_, String, select
from openpyxl import load_workbook
from app import crud

settings = get_settings()


class CRUDCustomer(CRUDBase[Customer, CustomerCreate, CustomerUpdate]):
    def get_by_user_id(self, db: Session, auth_user_id: int) -> Optional[Customer]:
        return db.query(Customer).filter(Customer.auth_user_id == auth_user_id).first()
    
    def get_auth_id_by_customer_id(self, db: Session, customer_id: int) -> Optional[int]:
        auth_user_id = db.query(Customer.auth_user_id).filter(Customer.id == customer_id).first()
        return auth_user_id[0] if auth_user_id else None

    def hard_delete_customer_data(self, db: Session, customer):
        customer_id = customer.id
        auth_user_id = customer.auth_user_id
        customer_email = customer.email
        
        customer_email_item_query = f"""
            DELETE FROM customer_email_item 
            WHERE customer_email_item.customer_id = :customer_id;
        """

        subscription_user_query = f"""
            DELETE subscription_user
            FROM subscription_user
            JOIN subscription ON subscription_user.subscription_id = subscription.id
            WHERE subscription.customer_id = :customer_id
        """
        customer_contact_platform_information_query = f"""
            DELETE FROM customer_contact_platform_information
            WHERE customer_id = :customer_id
        """
        account_notification_setting_query = f"""
            DELETE FROM account_notification_setting
            WHERE customer_id = :customer_id
        """
        simulation_item_query = f"""
            DELETE simulation_item
            FROM simulation_item
            JOIN simulation ON simulation.id=simulation_item.simulation_id
            JOIN subscription ON simulation.subscription_id=subscription.id
            WHERE subscription.customer_id = :customer_id
        """
        simulation_query = f"""
            DELETE 
            simulation
            FROM 
            simulation 
            JOIN subscription ON simulation.subscription_id=subscription.id
            WHERE subscription.customer_id = :customer_id
        """
        subscription_payment_receipt_query = f"""
        DELETE spr
            FROM subscription_payment_receipt spr
            JOIN subscription_payment_transaction spt ON spt.id=spr.subscription_payment_transaction_id
            JOIN subscription s ON spt.subscription_id=s.id
            WHERE
            s.customer_id = :customer_id
        """
        subscription_payment_transaction_query = f"""
            DELETE subscription_payment_transaction
            FROM subscription_payment_transaction
            JOIN subscription ON subscription_payment_transaction.subscription_id=subscription.id
            WHERE
            subscription.customer_id = :customer_id
        """
        subscription_query = f"""
            DELETE FROM subscription
            WHERE customer_id = :customer_id
        """

        account_information_query = """
            DELETE 
            FROM account_information 
            WHERE account_information.customer_id = :customer_id
        """
        customer_query = """
            DELETE FROM customer WHERE id = :customer_id
        """

        # Execute the raw SQL query with the customer ID parameter
        db.execute(customer_email_item_query, {'customer_id': customer_id})
        db.execute(subscription_user_query, {'customer_id': customer_id})
        db.execute(customer_contact_platform_information_query, {'customer_id': customer_id})
        db.execute(account_notification_setting_query, {'customer_id': customer_id})
        db.execute(simulation_item_query, {'customer_id': customer_id})
        db.execute(simulation_query, {'customer_id': customer_id})
        db.execute(subscription_payment_receipt_query, {'customer_id': customer_id})
        db.execute(subscription_payment_transaction_query, {'customer_id': customer_id})
        db.execute(subscription_query, {'customer_id': customer_id})
        db.execute(account_information_query, {'customer_id': customer_id})
        db.execute(customer_query, {'customer_id': customer_id})
        db.commit()

        # Make the external API request after committing the deletion
        auth_res = requests.delete(f"{settings.AUTH_API_URL}/user/user-and-app/{auth_user_id}")

        user_channel = f"user_{customer_email}_notification"
        notification_res = requests.delete(f"{settings.NOTIFICATION_API}/notifications/user-channel/{user_channel}")
        try:
            print(notification_res.json())
        except:
            pass
        return None

    def get_count_registered(self, db, where):
        subquery = db.query(
            distinct(Subscription.customer_id)).filter(
            Subscription.product_id == '********',
            Subscription.subscription_status == SubscriptionStatusEnum.ACTIVE
        ).subquery()

        total_registered_query = (
            db.query(
                func.count(distinct(Customer.id)).label("total_registered"),
            )
            .join(Subscription, Subscription.customer_id == Customer.id, isouter=True)
            .filter(
                Customer.id.notin_(subquery),
                Customer.auth_user_id != None,
                Customer.deleted_at == None
            )
        )

        wheres = []
        if where is not None and where != []:
            wheres = [
                condition for condition in where if condition["key"] != "subscription.created_at"
            ]

        conditions = self.get_full_condition(db=db, where=wheres)

        if conditions is not None:
            total_registered_query = total_registered_query.filter(conditions)

        total_registered = total_registered_query.scalar()

        return total_registered

    def get_all_registered(self, db, where):
        # Define the subquery with filtering for product_id != ********
        subquery = (
            db.query(
                Subscription.customer_id,
                Subscription.product_json,
                Subscription.product_id,  # Include product_id in the subquery
                Subscription.start_date,
                Subscription.expired_date,
                Product.name,
                func.if_(
                    # Prioritize subscriptions where product_json is not null and within date range
                    (Subscription.product_json.isnot(None))
                    & (Subscription.start_date <= func.now()),
                    # & (Subscription.expired_date >= func.now()),
                    1,  # High priority
                    func.if_(
                        Subscription.product_json.is_(None),  # Fallback to product_json null
                        2,
                        3,  # Lowest priority for others
                    ),
                ).label("priority"),
            )
            .join(Product, Subscription.product_id == Product.id)
            .filter(Subscription.product_id != ********,
                    Subscription.subscription_status == SubscriptionStatusEnum.ACTIVE,
                    )  # Exclude product_id ******** in the subquery
            .subquery()
        )

        subquery_second = (
            db.query(
                Subscription.customer_id,
            )
            .filter(
                Subscription.product_id == ********,
                Subscription.subscription_status == SubscriptionStatusEnum.ACTIVE
            )  # Exclude product_id ******** in the subquery
            .subquery()
        )

        subquery_result_id = [id_['customer_id'] for id_ in db.execute(subquery_second)]

        # Outer query to select customers with the highest priority subscription
        all_registered_query = (
            db.query(
                Customer.id,
                Customer.first_name,
                Customer.last_name,
                Customer.email,
                Customer.created_at,
                func.cast(Customer.title, String),
                Customer.street_address,
                Customer.district_postal_code,
                Customer.city,
                Customer.country,
                Customer.pseudo,
                AccountType.name,
                subquery.c.name,  # Include product_id in the final result
            )
            .outerjoin(
                subquery,
                (subquery.c.customer_id == Customer.id)
                & (subquery.c.priority == 1),  # Filter for highest priority subscription
            )
            .outerjoin(
                AccountInformation,
                AccountInformation.customer_id == Customer.id
            )
            .outerjoin(
                AccountType,
                AccountInformation.account_type_id == AccountType.id
            )
            .filter(
                Customer.auth_user_id != None,
                Customer.deleted_at == None,  # Ensure customers are not deleted
            )
        )

        # Process additional conditions
        wheres = []
        if where is not None and where != []:
            wheres = [
                condition for condition in where if condition["key"] != "subscription.created_at"
            ]

        conditions = self.get_full_condition(db=db, where=wheres)

        if conditions is not None:
            all_registered_query = all_registered_query.filter(conditions)

        # Fetch all records
        all_registered_data = all_registered_query.all()
        data = []
        columns = [column["name"] for column in all_registered_query.column_descriptions]  # Get column names
        for registered in all_registered_data:
            # Create a dictionary from the tuple and column names
            registered_dict = dict(zip(columns, registered))
            if registered_dict["id"] not in subquery_result_id:
                data.append(registered)
        return data

    def get_all_registered_(self, db, where):
        
        # Start the query
        all_registered_query = (
            db.query(
                Customer.id,  # Example column
                Customer.first_name,  # Example column
                Customer.last_name,  # Example column
                Customer.email,  # Example column
                Customer.created_at,  # Example column
                func.cast(Customer.title, String),  # Example column
                Customer.street_address,  # Example column
                Customer.district_postal_code,  # Example column
                Customer.city,  # Example column
                Customer.country,  # Example column
                Customer.pseudo,  # Example column
                Customer.pseudo,  # Example column
                Customer.pseudo,  # Example column
            )
            .outerjoin(Subscription, Subscription.customer_id == Customer.id)
            .filter(
                or_(
                    Subscription.product_id != ********,
                    Subscription.product_id == None,
                ),
                Customer.auth_user_id != None
            )
            .group_by(Customer.id)
        )

        # Process additional conditions
        wheres = []
        if where is not None and where != []:
            wheres = [
                condition for condition in where if condition["key"] != "subscription.created_at"
            ]

        conditions = self.get_full_condition(db=db, where=wheres)

        if conditions is not None:
            all_registered_query = all_registered_query.filter(conditions)
        # Fetch all records
        all_registered_data = all_registered_query.all()

        return all_registered_data

    def get_all_subscriptions_file(self, db, where):
        all_registered, all_subscriptions = self.get_all_subscriptions(db, where)
        

        
        
        output_bytes = io.BytesIO() 

        # Write to Excel with auto-filters
        with pd.ExcelWriter(output_bytes, engine="openpyxl") as writer:
            # Subscription data
            headers = [
                "Product ID", "Status", "Date de début",
                "Date d'expiration", "Customer ID", "Prénom", "Nom", "E-mail", "Type d'Abonnement"
            ]
            if len(all_subscriptions) > 0:
                df = pd.DataFrame(all_subscriptions, columns=headers)
            else:
                df = pd.DataFrame(columns=headers)

            df.to_excel(writer, sheet_name="Subscription", index=False)

            # Registered customers data
            registered_headers = [
                "ID", "First Name", "Last Name", "Email", "Created At",
                "Title", "Street Address", "Postal Code", "District", "Country",
                "Pseudo", "Type", "Subscription Type"
            ]
            if len(all_registered) != 0:
                registered_df = pd.DataFrame(all_registered, columns=registered_headers)
            else:
                registered_df = pd.DataFrame(columns=registered_headers)

            registered_df.to_excel(writer, sheet_name="Registered Customers", index=False)

        # Add auto-filters to the Excel file
        wb = load_workbook(output_bytes)

        for sheet_name in wb.sheetnames:
            ws = wb[sheet_name]
            ws.auto_filter.ref = ws.dimensions

        wb.save(output_bytes)
        output_bytes.seek(0)
        return   output_bytes
        
    def get_all_subscriptions(self, db, where):
        # Start the query
        subscriber_query = (
            db.query(
                Subscription.product_id,
                # Subscription.subscription_status.label("Status"),
                func.cast(Subscription.subscription_status, String).label("Status"),  # Example column
                Subscription.start_date.label("Date de debut"),
                Subscription.expired_date.label("Date d'expiration"),
                Customer.id.label("Customer ID"),
                Customer.first_name.label("Prénom"),
                Customer.last_name.label("Nom"),
                Customer.email.label("E-mail"),
                Product.name.label("Type d'Abonnement"),
            )
            .select_from(Subscription)
            .join(Customer, Customer.id == Subscription.customer_id)
            .join(Product, Product.id == Subscription.product_id)
            .filter(
                or_(
                    Subscription.product_id != ********,
                    Subscription.product_id == None,
                ),
                Subscription.subscription_status == SubscriptionStatusEnum.ACTIVE,
                # Subscription.expired_date >= func.now(),
                Subscription.start_date <= func.now(),
                Product.allow_new_subscribers == True,
                Customer.auth_user_id != None,
            )
            .group_by(
                Subscription.product_id,
                Subscription.subscription_status,
                Subscription.start_date,
                Subscription.expired_date,
                Customer.id,
                Customer.first_name,
                Customer.last_name,
                Customer.email,
                Product.name,
            )
        )

        all_registered = self.get_all_registered(db=db, where=where)

        # Process additional conditions
        wheres = []
        if where is not None and len(where) != 0:
            wheres = [condition for condition in where if condition["key"] != "created_at"]

        conditions = self.get_full_condition(db=db, where=wheres)

        if conditions is not None:
            subscriber_query = subscriber_query.filter(conditions)

        # Fetch all records
        all_subscriptions = subscriber_query.all() 
        return (all_registered, all_subscriptions)

    def format_product_name(self, name: str) -> str:
        name = name.lower()
        if "only" in name: 
            return name.strip()
        else:
            return name.replace('pvgis24', '').strip()

    def get_count_subscription(self, db, where, category="professional"):
        subscriber_query = (
            db.query(
                Subscription.product_id,
                func.count(distinct(Customer.id)).label("count"), 
                Product.billing_period_interval.label("billing_period_interval"),
                Product.monthly_credit.label("monthly_credit"),
            )
            .join(Customer, Customer.id == Subscription.customer_id)
            .join(Product, Product.id == Subscription.product_id)
            .filter(
                or_(
                    Subscription.product_id != ********,
                    Subscription.product_id == None,
                ),
                Subscription.subscription_status == SubscriptionStatusEnum.ACTIVE,
                # Subscription.expired_date >= func.now(),
                Subscription.start_date <= func.now(),
                Product.allow_new_subscribers == True,
                Customer.auth_user_id != None,
            )
        )

        product_query = (
            db.query(
                Product.id,
                Product.name,
                Product.billing_period_interval
            )
            .filter(
                Product.allow_new_subscribers == True,
                or_(Product.additional == False, Product.additional.is_(None)),
                Product.id != ********
            )
        )

        if category == "professional":
            subscriber_query = subscriber_query.filter(
                Product.type == 1,
            )
            product_query = product_query.filter(
                Product.type == 1,
            )
        else : 
            subscriber_query = subscriber_query.filter(
                Product.type == 2 ,
            )
            product_query = product_query.filter(
                Product.type == 2 ,
            )
        subscriber_query = subscriber_query.group_by(Subscription.product_id)
        
        
        
        wheres = []
        if where is not None and where != []:
            wheres = [
                condition for condition in where if condition["key"] != "created_at"
            ]

        conditions = self.get_full_condition(db=db, where=wheres)

        if conditions is not None:
            subscriber_query = subscriber_query.filter(conditions)

        subscription_counts = subscriber_query.all()
        
        product_list = product_query.all()
        
        product_id_to_name = {
            product.id: self.format_product_name(product.name)
            for product in product_list
        }

        breakdown = {name: {} for name in product_id_to_name.values()}

        for row in subscription_counts:
            subscription_name = product_id_to_name.get(row.product_id)
            if subscription_name and row.billing_period_interval.value:
                if category == "professional":
                    breakdown[subscription_name][row.billing_period_interval.value] = row.count
                else: 
                    breakdown[subscription_name][f"site_{int(row.monthly_credit)}"] = row.count
                    
        total_subscription = sum(sum(period_counts.values()) for period_counts in breakdown.values())

        return {
            "total_subscription": total_subscription,
            "details": breakdown,
        }

    def get_count_registered_subscription(self, db, where):
        count_registered = self.get_count_registered(db=db, where=where)
        count_subscription = self.get_count_subscription(db=db, where=where)

        return {
            "total_registered": count_registered,
            **count_subscription
        }
    
    def get_pvgis_status(self, db):
        # ALL
        subscription_all_count_professional = self.get_count_subscription(db=db, where=[], category="professional")
        subscription_all_count_none_professional = self.get_count_subscription(db=db, where=[], category="none_professional")
        all_count_registered = self.get_count_registered(db=db, where=[])
        all_count_unsubscribed = crud.subscription.get_count_unsubscribed_user(db=db, where=[])
        
        # LAST 24 hours  
        where_last_24_hours = [
            {"key": "created_at", "operator": "last_24h"},
            {"key": "subscription.created_at", "operator": "last_24h"},
        ]
        subscription_last_24_hours_professional = self.get_count_subscription(db=db, where=where_last_24_hours, category="professional")
        subscription_last_24_hours_none_professional = self.get_count_subscription(db=db, where=where_last_24_hours, category="none_professional")
        last_24_hours_registered = self.get_count_registered(db=db, where=where_last_24_hours)
        last_24_hours_unsubscribed = crud.subscription.get_count_unsubscribed_user(db=db, where=[
            {"key": "updated_at", "operator": "last_24h"}
        ])
        
        return {
            "registered_user": {
                "all": all_count_registered, 
                "last_24_hours": last_24_hours_registered,
            },
            "subscription" : {
                "professional": {
                    "all": subscription_all_count_professional,
                    "last_24_hours": subscription_last_24_hours_professional,
                }, 
                "none_professional": {
                    "all": subscription_all_count_none_professional,
                    "last_24_hours": subscription_last_24_hours_none_professional,
                }
            },
            "unsubscribed_user": {
                "all": all_count_unsubscribed, 
                "last_24_hours": last_24_hours_unsubscribed,
            } 
        }

    def get_for_records(self, db: Session, customer_id: int) -> Optional[Customer]:
        customer = ( 
            db.query(Customer) 
            .options(joinedload(Customer.account_information, innerjoin=False), joinedload(Customer.country_rel, innerjoin=False))
            .filter(Customer.id == customer_id)
        ).first()
        if customer:
            customer.autonomy_catalogue_json = None
            customer.default_referential_info_json = None
        return customer

customer = CRUDCustomer(Customer)
