from fastapi import HTTPException
from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.features import Features
from app.models.product_features import ProductFeatures
from app.schemas.product_features import ProductFeaturesCreate, ProductFeaturesUpdate


class CRUDProductFeatures(
    CRUDBase[ProductFeatures, ProductFeaturesCreate, ProductFeaturesUpdate]
):
    def get_feature_by_id(self, db: Session, id: int):
        try:
            return db.query(Features.id).filter(Features.id == id).one()
        except Exception as e:
            raise HTTPException(
                status_code=404,
                detail="Feature not found when creating a product feature",
            )


product_features = CRUDProductFeatures(ProductFeatures)
