from typing import Optional, List
from datetime import datetime, date, timedelta

from app.crud.base import CRUDBase
from app.models.bulk_upload import BulkUpload
from app.models.customer import Customer
from app.schemas.bulk_upload import BulkUploadCreate, BulkUploadUpdate
from app.services.city_migration_service import get_lat_lng_iso2_googleplaceid, get_timezone_offset
from sqlalchemy import desc
from sqlalchemy.orm import Session
import re
import requests
from app import crud, schemas
from app.config.settings import get_settings
from app.crud.ip_location_service import get_location_from_ip
from app.services.elastic.elastic_service import get_data_from_elastic

settings = get_settings()


class CRUDBulkUpload(CRUDBase[BulkUpload, BulkUploadCreate, BulkUploadUpdate]):
    def get_by_name(self, db: Session, *, name: str) -> Optional[BulkUpload]:
        return

    def get_by_resource_name(self, db: Session, *, resource_name: str) -> Optional[List[BulkUpload]]:
        query = db.query(BulkUpload).filter(BulkUpload.resource_name == resource_name).order_by(
            desc(BulkUpload.created_at))
        return query.all()

    def create_import(self, db: Session, import_id: str, resource_name: str, file_name: str):
        import_ob = BulkUpload(
            resource_name=resource_name,
            import_id=import_id,
            file_name=file_name
        )
        db.add(import_ob)
        db.commit()
        db.refresh(import_ob)
        return import_ob

    def clean_street_address(self, street_address, postal_code, city, country):
        street_address = str(street_address) if street_address is not None else ""
        postal_code = str(postal_code) if postal_code is not None else ""
        city = str(city) if city is not None else ""
        country = str(country) if country is not None else ""

        escaped_postal_code = re.escape(str(postal_code))
        escaped_city = re.escape(str(city))
        escaped_country = re.escape(str(country))

        pattern = rf"(,?\s*{escaped_postal_code})|(,?\s*{escaped_city})|(,?\s*{escaped_country})"

        cleaned_address = re.sub(pattern, "", street_address)

        cleaned_address = re.sub(r",\s*,", ",", cleaned_address).strip(", ")

        return cleaned_address

    def bulk_upload_installer(self, db: Session, import_id, row, user_id, source_file_name, source_row_id, country_codes_by_name,
                              installer_email_campaign = None):
        cur_country_code = row['Country Code 2'] if 'Country Code 2' in row else None
        cur_country_name = row['Region']
        if cur_country_code is None :
            if cur_country_name in country_codes_by_name:
                cur_country_code = country_codes_by_name[cur_country_name]
            else:
                (lat, lng, country_iso2, google_place_id) = get_lat_lng_iso2_googleplaceid(cur_country_name)
                cur_country_code = country_iso2
        cur_country = crud.country.get_first_where_array_v2(
            db=db,
            where=[{"key": "code_alpha_2", "operator": "==", "value": cur_country_code}]
        )
        settings_language = 'en'
        if installer_email_campaign and cur_country:
            ce_campaign_country_datas = installer_email_campaign.ce_campaign_country_data
            cur_compaign_country_data = next((data.default_language for data in ce_campaign_country_datas if data.country_id == cur_country.id), None)
            settings_language = cur_compaign_country_data if cur_compaign_country_data else settings_language
        
        customer_json = {
            "source_file_name": source_file_name, 
            "source_row_id": source_row_id,
            "email": row["Email"],
            "street_address": row["Address"],
            "district_postal_code": row["Post Code"],
            "city": row["Area 1"],
            "mobile_number": row["Phone"],
            "code_alpha_2": cur_country_code,
            "import_id": import_id,
            "last_user_to_interact": user_id,
            "settings_json": {
                "language": settings_language,
                "simulation_language": "en",
                "base_currency": {
                    "name": "Euro",
                    "code": "EUR",
                    "symbol": "€",
                    "default": "true"
                }
            }
        }
        account_info_json = {
            "account_type_id": 1,
            "professional_category_id": 3,
            "company_name": row["Company"],
            "import_id": import_id,
            "last_user_to_interact": user_id
        }
        customer_contact_platform_json = {
            "value": row["Website"],
            "contact_platform_id": 14,
            "import_id": import_id,
            "last_user_to_interact": user_id
        }

        if customer_json["email"]:
            user_response = requests.get(f"{settings.AUTH_API_URL}/user/username/{customer_json['email']}")
            if user_response.text != "":
                return {"skipped": row["Company"]}

            check_customer = crud.customer.get_first_where_array_v2(
                db=db,
                where=[{"key": "email", "operator": "==", "value": customer_json['email']}]
            )
            if check_customer:
                return {"skipped": row["Company"]}
        else:
            return {"skipped": f"email required, installer_id {row['Id']}"}

        # get country of customer based on the country code 
        if customer_json["code_alpha_2"]:
            country = crud.country.get_first_where_array_v2(
                db=db,
                where=[{"key": "code_alpha_2", "operator": "==", "value": customer_json["code_alpha_2"]}]
            )
            if country:
                customer_json["country"] = country.name
                customer_json["country_id"] = country.id
                customer_json["timezone_offset"] = get_timezone_offset(country.code_alpha_2)
            customer_json.pop("code_alpha_2", None)

        customer_json["street_address"] = self.clean_street_address(
            street_address=customer_json["street_address"],
            postal_code=customer_json["district_postal_code"],
            city=customer_json["city"],
            country=customer_json["country"]
        )

        # create customer with information
        customer_in = schemas.Customer(**customer_json)
        crud.customer.create(db=db, obj_in=customer_in, commit=False, refresh=False)
        db.flush()
        customer = db.query(Customer).filter(Customer.email == customer_in.email).first()
         
        
        account_information_in = schemas.AccountInformation(**account_info_json)
        account_information_in.customer_id = customer.id
        crud.account_information.create(db=db, obj_in=account_information_in, commit=False, refresh=False)

        customer_contact_platform_in = schemas.CustomerContactPlatformInformationBase(**customer_contact_platform_json)
        customer_contact_platform_in.customer_id = customer.id
        crud.customer_contact_platform_information.create(db=db, obj_in=customer_contact_platform_in, commit=False, refresh=False)

        return {"added": row['Company']}

    def revert_bulk_installer(self, db: Session, *, import_id: str):
        statements = [
            f"DELETE FROM invitation WHERE customer_id IN (SELECT id FROM customer WHERE import_id='{import_id}')",
            f"DELETE FROM customer_contact_platform_information WHERE import_id='{import_id}'",
            f"DELETE FROM account_information WHERE import_id='{import_id}'",
            f"DELETE FROM customer WHERE import_id='{import_id}'",
            f"DELETE FROM bulk_upload WHERE import_id='{import_id}'",
        ]

        for statement in statements:
            db.execute(statement)

        db.commit()

    def bulk_update_missing_country_customer(self, db: Session, start_date: str="", end_date: str="", customer_limit: int = 1000):
        # get list of all customer has missing country
        customers = crud.customer.get_multi_where_array_v2(
            db=db,
            where=[{"key": "country", "operator": "isNull"}],
            limit= customer_limit if customer_limit else 1000
        )
        customer_ids = [customer.id for customer in customers]
        
        elastic_data = []
        # get estaticsearch data for customers
        if len(customer_ids) > 0 :
            # Get first and last day of current month
            today = date.today()
            first_day = today.replace(day=1).strftime("%Y-%m-%dT00:00:00")
            if today.month == 12:
                last_day = today.replace(year=today.year + 1, month=1, day=1)
            else:
                last_day = today.replace(month=today.month + 1, day=1)
            last_day = (last_day - timedelta(days=1)).strftime("%Y-%m-%dT23:59:59")
            query = {
                "size": 0, 
                "query": {
                    "bool": {
                        "filter": [
                            {
                            "range": {
                                "timestamp": {
                                    "gte": start_date if start_date else first_day,
                                    "lte": end_date if end_date else last_day
                                }
                            }
                            }
                        ],
                        "must": [
                            {
                                "exists": {
                                    "field": "user_location.countryCode"
                                }
                            },
                            {
                                "exists": {
                                    "field": "pvgis_customer_id"
                                }
                            },
                            {"terms": {"pvgis_customer_id": customer_ids}}
                        ]
                    }
                },
                "aggs": {
                    "unique_customer_ids": {
                        "terms": {
                            "field": "pvgis_customer_id.keyword",
                            "size": 1000 
                        },
                        "aggs": {
                            "latest_doc": {
                            "top_hits": {
                                "size": 1,
                                "sort": [
                                    {
                                        "timestamp": {
                                        "order": "desc"
                                        }
                                    }
                                ],
                                "_source": {
                                    "includes": [
                                        "pvgis_customer_id",
                                        "user_location.countryCode"
                                    ]
                                }
                            }
                            }
                        }
                    }
                }
            }

            elastic_data = get_data_from_elastic(url=f"/{settings.ELASTIC_INDEX}/_search", query=query)
        
        pvgis_customer_map = {}
        # map elastic data for each customer
        if elastic_data and 'aggregations' in elastic_data and elastic_data['aggregations'] and 'unique_customer_ids' in elastic_data['aggregations'] and 'buckets' in elastic_data['aggregations']['unique_customer_ids'] and len(elastic_data['aggregations']['unique_customer_ids']['buckets']):
            buckets = elastic_data['aggregations']['unique_customer_ids']['buckets']
            for hit in buckets:
                if 'latest_doc' in hit and 'hits' in hit['latest_doc'] and 'hits' in hit['latest_doc'] and 'hits' in hit['latest_doc']['hits'] and len(hit['latest_doc']['hits']['hits']) and hit['latest_doc']['hits']['hits'][0]:
                    item = hit['latest_doc']['hits']['hits'][0]
                    pvgis_customer_id = int(item['_source'].get('pvgis_customer_id', -1))
                    pvgis_customer_map[pvgis_customer_id] = item
        
        updated_count = 0
        # update customer if country found
        for customer in customers:
            if customer.id in pvgis_customer_map:
                hit = pvgis_customer_map[customer.id]
                country_code = hit['_source']['user_location']['countryCode']
                where = [
                    {
                        "key": "code_alpha_2",
                        "value": country_code,
                        "operator": "=="
                    }
                ]
                # Find corresponding country in our base
                country = crud.country.get_first_where_array_v2(db=db, where=where,base_columns=["name"])
                if country:
                    updated_count += 1
                    crud.customer.update(db=db, db_obj=customer, obj_in={"country": country.name if country else None})
        
        return {
            "fetched_elastic_data": len(pvgis_customer_map),
            "customer_missing_country": len(customer_ids),
            "rows_updated": updated_count
        }
    
bulk_upload = CRUDBulkUpload(BulkUpload)
