from typing import Optional
from fastapi.encoders import jsonable_encoder
from sqlalchemy import and_
from app.crud.base import CRUDBase
from app.models.product import Product
from sqlalchemy.orm import Session,joinedload,contains_eager
from fastapi import HTTPException
 
from app.models.product_features import ProductFeatures
from app.schemas.product import ProductCreate, ProductUpdate
from app import crud


class CRUDProduct(CRUDBase[Product, ProductCreate, ProductUpdate]):
    pass
    # def create(self, db: Session, *, obj_in: ProductCreate) -> Product:
    #     db_obj = Product(**jsonable_encoder(obj_in))
    #     db.add(db_obj)
    #     db.commit()
    #     db.refresh(db_obj)
    #     return db_obj

    def get_allowed_subscription_product_by_id(self, db: Session, product_id:int):
        product_json = crud.product.get_first_where_array(db=db,
                                                    where=[{"key": "id","operator": "==","value": product_id}, 
                                                           {"key": "allow_new_subscribers","operator": "==","value": True}], 
                                                    relations=["product_features", "product_features.features"])
        if not product_json:
            raise HTTPException(
                status_code=404, detail="product_not_allowed_new_subscribe"
            )
            
        return product_json 
    
    def get_product_details_or_throw(self, db: Session, product_id:int):
        query = (
            db.query(Product).options(
                joinedload(Product.product_features).joinedload(ProductFeatures.features),
            ).filter(
                Product.id == product_id,)
        )
     
         
        
        product_details = query.first()
        if not product_details:
            raise HTTPException(
                status_code=404, detail="product_not_found"
            )
            
        return product_details 
        

product = CRUDProduct(Product)
