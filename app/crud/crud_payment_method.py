from fastapi.encoders import jsonable_encoder
from app.crud.base import CRUDBase
from sqlalchemy.orm import Session

from app.models.payment_method import PaymentMethod
from app.schemas.payment_method import PaymentMethodCreate, PaymentMethodUpdate


class CRUDPaymentMethod(
    CRUDBase[PaymentMethod, PaymentMethodCreate, PaymentMethodUpdate]
):
    pass


payment_method = CRUDPaymentMethod(PaymentMethod)
