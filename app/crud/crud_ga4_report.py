from typing import List
from sqlalchemy.orm import Session
from app import crud
from datetime import datetime, timedelta, timezone
from google.analytics.data_v1beta import BetaAnalyticsDataClient
from google.analytics.data_v1beta.types import (Dimension, Metric, OrderBy, DateRange,
                                               FilterExpression, MetricAggregation, CohortSpec)
from google.analytics.data_v1beta.types import RunReportRequest, RunRealtimeReportRequest
from app.schemas.google_analytics import GoogleAnalytics, GoogleAnalyticsCreate, GoogleAnalyticsUpdate
from sqlalchemy import func
from fastapi.encoders import jsonable_encoder

class GA4Exception(Exception):
    '''base class for GA4 exceptions'''

class GA4Report():
    """class to query GA4 real time report
    More information: https://support.google.com/analytics/answer/9271392?hl=en
    """

    def __init__(self, property_id):
        self.property_id = property_id
        self.client = BetaAnalyticsDataClient()

    def query_report(
        self,
        metrics: List[Metric]=[],
        dimensions: List[str]=[],
        start_date = None,
        end_date = None,
        limit:int=10000,
        quota_usage:bool=False
    ):
        """
        :param dimensions: categorical attributes (age, country, city, etc)
        :type dimensions: [dimension type]
                :param dimensions: categorical attributes (age, country, city, etc)
        :start_date: [date]
        :end_date: [date]
        :param metrics: numeric attributes (views, totalUsers, activeUsers)
        :type metrics: [metric type]

        """
        try:
            dimension_list = [Dimension(name=dim) for dim in dimensions]
            metrics_list = [Metric(name=m) for m in metrics]
            response = None
            if(start_date is not None and end_date is not None): 
                report_request = RunReportRequest(
                    property=f'properties/{self.property_id}',
                    date_ranges=[DateRange(start_date=start_date.strftime('%Y-%m-%d'), end_date=end_date.strftime('%Y-%m-%d'))],
                    dimensions=dimension_list,
                    metrics=metrics_list,
                    limit=limit,
                    return_property_quota=quota_usage
                )
                response = self.client.run_report(report_request)
            else:
                report_request = RunRealtimeReportRequest(
                    property=f'properties/{self.property_id}',
                    dimensions=dimension_list,
                    metrics=metrics_list,
                    limit=limit,
                    return_property_quota=quota_usage
                )
                response = self.client.run_realtime_report(report_request)
     
            output = {}
            if 'property_quota' in response:
                output['quota'] = response.property_quota

            # construct the dataset
            headers = [header.name for header in response.dimension_headers] + [header.name for header in response.metric_headers]
            rows = []
            for row in response.rows:
                rows.append(
                    [dimension_value.value for dimension_value in row.dimension_values] + \
                    [metric_value.value for metric_value in row.metric_values])            
            output['headers'] = headers
            output['rows'] = rows
            return output
        except Exception as e:
            raise GA4Exception(e)
        
    def get_active_users(self, db: Session):
        google_analytics_data = jsonable_encoder(crud.google_analytics.get_first_where_array_v2(
            db=db,
            where=[{"key":"created_at","operator":"date","value":datetime.now().strftime("%Y-%m-%d")}],
            base_columns=["data_json"],
        ))
        
        if(google_analytics_data is None):
            # active user in all time
            ga_reports = self.query_report(
                start_date=datetime(2024, 1, 1),
                end_date=datetime.today(),
                metrics=['activeUsers'],
                dimensions=[],
                limit=10
            )
            data = [
                {header: value for header, value in zip(ga_reports['headers'], row)}
                for row in ga_reports['rows']
            ]
            
            yesterday = datetime.now(timezone.utc) - timedelta(days=1)
            start_date = datetime(yesterday.year, yesterday.month, yesterday.day, 0, 0, 0, tzinfo=timezone.utc)
            end_date = datetime(yesterday.year, yesterday.month, yesterday.day, 23, 59, 59, tzinfo=timezone.utc)

            ga_reports_last_24_hours = self.query_report(
                start_date=start_date,
                end_date=end_date,
                metrics=['activeUsers'],
                dimensions=[],
                limit=10
            )
            
            # active user in last 24 hours
            data[0]["activeUsersLast24h"] = ga_reports_last_24_hours['rows'][0][0] if ga_reports_last_24_hours['rows'] else 0
            
            crud.google_analytics.create(
                db=db, obj_in={"data_json":data}
            )
            response = data[0]
        else:
            response = google_analytics_data["data_json"][0]
       
        return response
    