from typing import Optional, List
from fastapi.encoders import jsonable_encoder
from sqlalchemy import and_, text
from app.crud.base import CRUDBase
from app.models.account_notification_setting import AccountNotificationSetting
from sqlalchemy.orm import Session

from app.schemas.account_notification_setting import AccountNotificationSettingCreate, AccountNotificationSettingUpdate
from app.models.notification_setting import NotificationSetting
from fastapi import HTT<PERSON>Exception
from app import crud

class CRUDAccountNotificationSetting(CRUDBase[AccountNotificationSetting, AccountNotificationSettingCreate, AccountNotificationSettingUpdate]):
    def remove_by_key(self, db: Session, key: str, auth_user_id: int) -> None:
        notification_setting_id, customer_id = self.get_notification_setting_and_customer_ids(db, key, auth_user_id)
        records_to_delete = (
            db.query(AccountNotificationSetting)
            .filter(
                AccountNotificationSetting.notification_setting_id == notification_setting_id,
                AccountNotificationSetting.customer_id == customer_id
            )
            .all()
        )
        # Delete records and track event
        for record in records_to_delete:
            db.delete(record)
        db.commit()
        return None

    def add_by_key(self, db: Session, key: str, auth_user_id: int) -> AccountNotificationSetting:
        notification_setting_id, customer_id = self.get_notification_setting_and_customer_ids(db, key, auth_user_id)

        existing_setting = db.query(AccountNotificationSetting).filter(
            AccountNotificationSetting.notification_setting_id == notification_setting_id,
            AccountNotificationSetting.customer_id == customer_id
        ).first()

        if existing_setting:
            return existing_setting

        new_setting = AccountNotificationSetting(notification_setting_id=notification_setting_id, customer_id=customer_id)
        db.add(new_setting)
        db.commit()
        db.refresh(new_setting)
        return new_setting
    
    def check_all_by_customer_id(self, db: Session, customer_id) -> None:
        try:
            sql = text("""
                INSERT INTO account_notification_setting (notification_setting_id, customer_id)
                SELECT notification_setting.id AS notification_setting_id, :customer_id AS customer_id
                FROM notification_setting
                WHERE notification_setting.id NOT IN (
                    SELECT account_notification_setting.notification_setting_id
                    FROM account_notification_setting
                    WHERE account_notification_setting.customer_id = :customer_id
                )
            """)

            db.execute(sql, {"customer_id": customer_id})
            db.commit()
        except Exception as e:
            print(f"Error inserting account_notification_settings for customer {customer_id}: {e}")
            raise HTTPException(status_code=500, detail="Failed to insert account notification settings")
    
    def get_key_list(self, db: Session, customer_id: int) -> AccountNotificationSetting:
        settings = (
            db.query(NotificationSetting.key)
            .join(AccountNotificationSetting)
            .filter(
                AccountNotificationSetting.customer_id == customer_id
            )
            .all())
        return [item[0] for item in settings]
    
    def get_notification_setting_and_customer_ids(self, db: Session, key: str, auth_user_id: int) -> tuple[int, int]:
        customer = crud.customer.get_by_user_id(db, auth_user_id)
        notification_setting = db.query(NotificationSetting.id).filter(NotificationSetting.key == key).first()

        if not notification_setting or not customer:
            raise HTTPException(status_code=404, detail="Notification setting or customer not found")

        return notification_setting[0], customer.id

account_notification_setting = CRUDAccountNotificationSetting(AccountNotificationSetting)
