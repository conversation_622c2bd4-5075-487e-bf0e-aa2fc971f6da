from app.crud.base import CRUDBase
from app.models.customer_contact_platform_information import CustomerContactPlatformInformation

from app.schemas.customer_contact_platform_information import CustomerContactPlatformInformationCreate, CustomerContactPlatformInformationUpdate


class CRUDCustomerContactPlatformInformation(CRUDBase[CustomerContactPlatformInformation, CustomerContactPlatformInformationCreate, CustomerContactPlatformInformationUpdate]):
    pass


customer_contact_platform_information = CRUDCustomerContactPlatformInformation(CustomerContactPlatformInformation)
