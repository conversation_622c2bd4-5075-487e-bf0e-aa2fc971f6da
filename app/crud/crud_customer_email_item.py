from datetime import timedelta
from app.crud.base import CRUDBase
from app.models.country import Country
from app.models.customer import Customer
from app.models.customer_email_item import CustomerEmailItem
from app.models.customer_email_task import CustomerEmailTask
from app.schemas.customer_email_item import CustomerEmailItemCreate, CustomerEmailItemUpdate
from sqlalchemy import Date, and_, cast, func, not_, outerjoin 
from sqlalchemy.orm import joinedload, Session, aliased
from typing import List

from app.services.customer_email.customer_email_item_factories import generate_random_subscribed_user_query

class UDddCustomerEmailItem(CRUDBase[CustomerEmailItem, CustomerEmailItemCreate, CustomerEmailItemUpdate]):
    def get_customer_email_item_stats(self,db, current_datetime):
        a_day_ago = current_datetime - timedelta(days=1)
        query = (
            db.query(
                CustomerEmailTask.key.label("task_key"),   
                CustomerEmailTask.email_number.label("task_email_number"), 
                func.count(CustomerEmailItem.id).label('email_item_count')   
            )
            .join(CustomerEmailItem, 
                  and_ (
                        CustomerEmailTask.id == CustomerEmailItem.customer_email_task_id, 
                        CustomerEmailItem.sent_at <= current_datetime,
                        CustomerEmailItem.sent_at > a_day_ago,
                    ),  
                  isouter=True)   
            .group_by(CustomerEmailTask.key, CustomerEmailTask.email_number)   
        )
        results = query.all()
        return {
            "stats_query":  str(query.statement.compile(compile_kwargs={"literal_binds": True})),
            "stats_date": current_datetime,
            "stats":
                [
                    {"task_email_number": task_email_number, "task_key": task_key, "email_item_count": email_item_count} 
                    for task_key,task_email_number,email_item_count in results
                ]
        }
        
    def get_daily_report(self, db: Session, current_datetime) -> List[CustomerEmailItem]:
        a_day_ago = current_datetime - timedelta(days=1)
        results = (
            db.query( 
                CustomerEmailItem, 
            ).options(
                joinedload(CustomerEmailItem.customer),
                joinedload(CustomerEmailItem.country, innerjoin=False),
                joinedload(CustomerEmailItem.customer_email_task),
            )
            .filter(
                CustomerEmailItem.sent_at <= current_datetime,
                CustomerEmailItem.sent_at > a_day_ago,
                not_(CustomerEmailItem.sent_at.is_(None))
            )
            .all() 
        )
        return results
    
    def get_customer_data_for_mail(self, db: Session, customer_id, current_datetime):
        testimony_customer = aliased(Customer)
        testimony_customer_subquery = generate_random_subscribed_user_query(db, current_datetime)
        query = (
            db.query(
                Customer,
                Country,
                testimony_customer
            )
            .join(testimony_customer_subquery, testimony_customer_subquery.c.country_id == Customer.country_id, isouter=True)
            .join(testimony_customer,testimony_customer.id == testimony_customer_subquery.c.customer_id ,  isouter=True) 
            .join(Country, Country.id == Customer.country_id)
            .filter(
                Customer.id == customer_id, 
            )
        )
        results = query.all()
        
        data_dicts = [{
            "customer": r[0],
            "country": r[1],
            "random_subscribed_customer": r[2]
        } for r in results] 
        
        return  data_dicts

customer_email_item= UDddCustomerEmailItem(CustomerEmailItem)
