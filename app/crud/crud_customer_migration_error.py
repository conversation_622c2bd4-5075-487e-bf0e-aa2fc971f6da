from app.crud.base import CRUDBase
from app.crud.ip_location_service import get_google_place_ids_by_city_ip
from app.models.customer_migration_error import CustomerMigrationError
from sqlalchemy.orm import Session
from app.schemas.customer_migration_error import CustomerMigrationErrorCreate, CustomerMigrationErrorUpdate
from sqlalchemy.sql import text

class CRUDCustomerMigrationError(CRUDBase[CustomerMigrationError, CustomerMigrationErrorCreate, CustomerMigrationErrorUpdate]):
    pass
 

customer_migration_error = CRUDCustomerMigrationError(CustomerMigrationError)
