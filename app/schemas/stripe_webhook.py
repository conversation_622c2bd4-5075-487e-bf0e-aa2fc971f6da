from app.schemas.customer_contact_platform_information import CustomerContactPlatformInformationCreate
from .base import BaseSchema
from pydantic import BaseModel
from typing import List, Any, Optional
from app.enums.age_range_enum import AgeRangeEnum
from .customer import Customer
from .account_type import AccountType
from .professional_category import ProfessionalCategory
from .school_category import SchoolCategory

class StripeWebhookBase(BaseSchema):
    event_id: Optional[str] = None
    event_type: Optional[str] = None

# Properties to receive on creation
class StripeWebhookCreate(StripeWebhookBase):
    pass

# Properties to receive on update
class StripeWebhookUpdate(StripeWebhookBase):
    pass


# Properties shared by models stored in DB
class StripeWebhookInDBBase(StripeWebhookBase):
    id: Optional[int] = None

    class Config:
        orm_mode = True


# Properties to return to client
class StripeWebhook(StripeWebhookInDBBase):
    pass

# Response sent to client
class ResponseStripeWebhook(BaseModel):
    count: Optional[int]
    data: Optional[List[StripeWebhook]]
