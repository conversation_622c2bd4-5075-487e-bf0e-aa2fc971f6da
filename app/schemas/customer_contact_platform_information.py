from .base import BaseSchema
from pydantic import BaseModel
from typing import List, Optional
from .customer import Customer
from .contact_platform import Contact<PERSON>latform

# Shared properties
class CustomerContactPlatformInformationBase(BaseSchema):
    value: Optional[str] = None
    contact_platform_id: Optional[int] = None
    customer_id : Optional[int] = None

# Properties to receive on creation
class CustomerContactPlatformInformationCreate(CustomerContactPlatformInformationBase):
    value: str
    contact_platform_id: int
    customer_id: int


# Properties to receive on update
class CustomerContactPlatformInformationUpdate(CustomerContactPlatformInformationBase):
    pass


# Properties shared by models stored in DB
class CustomerContactPlatformInformationInDBBase(CustomerContactPlatformInformationBase):
    id: Optional[int] = None
    contact_platform_id: Optional[int] = None
    customer_id : Optional[int] = None

    class Config:
        orm_mode = True


# Properties to return to client
class CustomerContactPlatformInformation(CustomerContactPlatformInformationInDBBase):
    contact_platform: Optional[ContactPlatform] = None
    customer : Optional[Customer] = None

# Response sent to client
class ResponseCustomerContactPlatformInformation(BaseModel):
    count: Optional[int]
    data: Optional[List[CustomerContactPlatformInformation]]
