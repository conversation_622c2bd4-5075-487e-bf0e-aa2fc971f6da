from .base import BaseSchema
from pydantic import BaseModel
from typing import List, Optional


# Shared properties
class ProductFeaturesBase(BaseSchema):
    product_id: int
    features_id: int
    allow: Optional[bool]


# Properties to receive on creation
class ProductFeaturesCreate(ProductFeaturesBase):
    pass


# Properties to receive on update
class ProductFeaturesCreate(ProductFeaturesBase):
    pass


class ProductFeaturesUpdate(ProductFeaturesBase):
    pass


# Properties shared by models stored in DB
class ProductFeaturesInDBBase(ProductFeaturesBase):
    id: Optional[int] = None

    class Config:
        from_attributes = True


# Properties to return to client
class ProductFeatures(ProductFeaturesInDBBase):
    pass


# Response sent to client
class ResponseProductFeatures(BaseModel):
    count: Optional[int]
    data: Optional[List[ProductFeatures]]
