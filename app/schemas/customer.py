from datetime import datetime

from .base import BaseSchema
from pydantic import BaseModel, EmailStr
from typing import Any, List, Literal, Optional, Union
from app.enums.title_enum import TitleEnum
from app.enums.support_enum import SupportEnum


# Shared properties
class CustomerBase(BaseSchema):
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    profile_image_json: Optional[dict] = None
    company_logo_json: Optional[dict] = None
    settings_json: Optional[dict] = None
    default_referential_info_json: Optional[dict] = None
    catalogues_json: Optional[dict] = None
    autonomy_catalogue_json: Optional[dict] = None
    auth_user_id: Optional[int] = None
    created_by: Optional[int] = None
    email: Optional[str] = None
    title: Optional[TitleEnum] = None
    street_address: Optional[str] = None
    district_postal_code: Optional[str] = None
    city: Optional[str] = None
    country: Optional[str] = None
    pseudo: Optional[str] = None
    mobile_number: Optional[str] = None
    accept_cgu: Optional[bool] = None
    cart_reference: Optional[str] = None
    full_phone: Optional[str] = None
    stripe_customer_id: Optional[str] = None
    country_id: Optional[int] = None
    timezone_offset: Optional[str] = None
    source_row_id: Optional[int] = None
    source_file_name: Optional[str] = None
    unsubscribed_at: Optional[datetime] = None


# Properties to receive on creation
class CustomerCreate(CustomerBase):
    first_name: str
    last_name: str


# Properties to receive on update
class CustomerUpdate(CustomerBase):
    pass


# Properties shared by models stored in DB
class CustomerInDBBase(CustomerBase):
    id: Optional[int] = None
    created_by: Optional[int] = None

    class Config:
        orm_mode = True


# Properties to return to client
class Customer(CustomerInDBBase):
    created_by_user: Optional[Any] = None


# Response sent to client
class ResponseCustomer(BaseModel):
    count: Optional[int]
    data: Optional[List[Customer]]


class OAuth2PasswordRequestForm(BaseModel):
    grant_type: Optional[str]
    username: EmailStr
    password: str


class Token(BaseModel):
    access_token: str
    token_type: str = "bearer"


class BaseUserSchema(BaseModel):
    pseudo: Optional[str] = None
    last_name: str
    first_name: str
    email: EmailStr
    password: str
    language: str
    accept_cgu: bool
    country_id: Optional[int]
    country: Optional[str]
    timezone_offset: Optional[str] = None
    country_code: Optional[str] = None


class ProfessionalUserSchema(BaseUserSchema):
    account_type_id: Literal[1]
    company_name: str
    cart_reference: Optional[str]


class SchoolUserSchema(BaseUserSchema):
    account_type_id: Literal[3]
    company_name: str
    cart_reference: Optional[str]


class PersonalUserSchema(BaseUserSchema):
    account_type_id: Literal[2]
    company_name: Optional[str]
    cart_reference: Optional[str]


# Properties to receive from pvgis-UI client
RegistrationData = Union[ProfessionalUserSchema, SchoolUserSchema, PersonalUserSchema]


class CustomerUpdateInfos(CustomerBase):
    company_name: Optional[str]
    siret_number: Optional[str]


class CustomerAccountInfoCreate(CustomerBase):
    account_type_id: Optional[str]
    company_name: Optional[str]
    siret_number: Optional[str]


class LoginData(BaseModel):
    username: str
    password: str
    country_code: Optional[str] = None
    language: Optional[str] = None
    user_id: Optional[str] = None


class ForgotPasswordData(BaseModel):
    email: str
    subject: str
    lang: Optional[str]


class ResetPasswordData(BaseModel):
    newPassword: str
    emailInfo: dict
    language: str
    
class ChangeEmailData(BaseModel):
    customer_id: str
    encoded_email: str
    lang: str
  
class RequestChangeEmailData(BaseModel):
    subject: str
    email: str
    customer_id: str
    lang: str
    firstname: str
    lastname: str
