from typing import Optional, List, Any

from pydantic import BaseModel


# Shared properties
class BulkUploadBase(BaseModel):
    import_id: Optional[str]
    file_name: Optional[str]
    resource_name: Optional[Any]


# Properties to receive on BulkUpload creation
class BulkUploadCreate(BulkUploadBase):
    pass


class BulkUploadUpdate(BulkUploadBase):
    pass


# Properties to return to client
class BulkUpload(BulkUploadBase):
    pass


# Properties stored in DB
class BulkUploadInDB(BulkUploadBase):
    pass


class ResponseBulkUpload(BaseModel):
    count: Optional[int]
    data: Optional[List[BulkUpload]]
