from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime
from .base import BaseSchema


# Shared properties for CustomerEmailTask
class CustomerEmailTaskBase(BaseSchema):
    key: Optional[str] = None
    name: Optional[str] = None
    email_number: Optional[int] = None
    cms_key: Optional[str] = None
    is_active: Optional[bool] = None 

class CustomerEmailTaskCreate(CustomerEmailTaskBase):
    pass

class CustomerEmailTaskUpdate(BaseModel):
    pass

class CustomerEmailTaskInDB(CustomerEmailTaskBase):
    id: int

    class Config:
        orm_mode = True
        
class CustomerEmailTaskPublish(BaseModel):
    id: int