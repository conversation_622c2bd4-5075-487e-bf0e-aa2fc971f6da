from app.schemas.customer_contact_platform_information import CustomerContactPlatformInformationCreate
from .base import BaseSchema
from pydantic import BaseModel
from typing import List, Any, Optional
from app.enums.age_range_enum import AgeRangeEnum
from .customer import Customer
from .account_type import AccountType
from .professional_category import ProfessionalCategory
from .school_category import SchoolCategory

class StripeTempPaymentSuccessBase(BaseSchema):
    
    subscription_stripe_id: Optional[str] = None
    event_ob: Optional[Any] = None

# Properties to receive on creation
class StripeTempPaymentSuccessCreate(StripeTempPaymentSuccessBase):
    pass

# Properties to receive on update
class StripeTempPaymentSuccessUpdate(StripeTempPaymentSuccessBase):
    pass


# Properties shared by models stored in DB
class StripeTempPaymentSuccessInDBBase(StripeTempPaymentSuccessBase):
    id: Optional[int] = None

    class Config:
        orm_mode = True


# Properties to return to client
class StripeTempPaymentSuccess(StripeTempPaymentSuccessInDBBase):
    pass

# Response sent to client
class ResponseStripeTempPaymentSuccess(BaseModel):
    count: Optional[int]
    data: Optional[List[StripeTempPaymentSuccess]]
