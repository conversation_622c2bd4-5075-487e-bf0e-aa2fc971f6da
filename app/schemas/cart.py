from .base import BaseSchema
from pydantic import BaseModel
from typing import Any, List, Optional


# Shared properties
class CartBase(BaseSchema):
    country_id: Optional[int] = None 
    language_iso_2: Optional[str] = None
    ip_address: Optional[str] = None
    cart_reference: Optional[str] = None

# Properties to receive on creation
class CartCreate(CartBase):
    pass
class AddToCart(BaseSchema):
    cart_reference:Optional[str]
    product_id:int 
    customer_id:Optional[int]
    country_id: Optional[int]
    language_iso_2: Optional[str]
    ip_address: Optional[str]
    
class DeleteCart(BaseSchema):
    customer_id:Optional[int]
class CartUpdate(CartBase):
    pass