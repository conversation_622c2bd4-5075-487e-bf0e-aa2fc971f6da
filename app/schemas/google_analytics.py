from app.schemas.customer_contact_platform_information import CustomerContactPlatformInformationCreate
from .base import BaseSchema
from pydantic import BaseModel
from typing import List, Any, Optional
from app.enums.age_range_enum import AgeRangeEnum
from .customer import Customer
from .account_type import AccountType
from .professional_category import ProfessionalCategory
from .school_category import SchoolCategory

class GoogleAnalyticsBase(BaseSchema):
    data_json: Optional[str] = None

# Properties to receive on creation
class GoogleAnalyticsCreate(GoogleAnalyticsBase):
    data_json: Optional[str] = None


# Properties to receive on update
class GoogleAnalyticsUpdate(GoogleAnalyticsBase):
    pass


# Properties shared by models stored in DB
class GoogleAnalyticsInDBBase(GoogleAnalyticsBase):
    id: Optional[int] = None
    data_json: Optional[str] = None

    class Config:
        orm_mode = True


# Properties to return to client
class GoogleAnalytics(GoogleAnalyticsInDBBase):
    data_json: Optional[str] = None


# Response sent to client
class ResponseGoogleAnalytics(BaseModel):
    count: Optional[int]
    data: Optional[List[GoogleAnalytics]]
