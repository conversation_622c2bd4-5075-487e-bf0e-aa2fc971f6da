from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime
from .base import BaseSchema


# Shared properties for SimulationListing
class SimulationListingBase(BaseSchema):
    city_id: Optional[int] = None
    simulation_data: Optional[dict] = None
    simulation_input: Optional[dict] = None
    simulation_image: Optional[dict] = None
    content: Optional[str] = None 

class SimulationListingCreate(SimulationListingBase):
    pass

class SimulationListingUpdate(BaseModel):
    pass

class SimulationListingInDB(SimulationListingBase):
    id: int

    class Config:
        orm_mode = True
        
class SimulationListingPublish(BaseModel):
    id: int