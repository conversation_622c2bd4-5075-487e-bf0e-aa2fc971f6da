from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime
from .base import BaseSchema


# Shared properties for City
class CityBase(BaseSchema):
    name:str
    import_id: Optional[str] = None
    country_id: Optional[int] = None
    status: Optional[bool] = None
    google_place_id: Optional[str] = None
    
    
class CityCreate(CityBase):
    pass

class CityUpdate(BaseModel):
    pass

class CityInDB(CityBase):
    id: int

    class Config:
        orm_mode = True
        
class CityMigrate(BaseModel):
    chunk_size: int = 20