from datetime import datetime

from app.models.subscription_payment_receipt import SubscriptionPaymentReceipt
from .base import BaseSchema
from pydantic import BaseModel
from typing import Any, List, Optional
from .subscription import Subscription
from app.enums.payment_status_enum import PaymentStatusEnum
from app.enums.receipt_status_enum import ReceiptStatusEnum

# Shared properties
class SubscriptionPaymentReceiptBase(BaseSchema):
    subscription_payment_transaction_id: Optional[int] = None
    stripe_reference: Optional[str] = None
    reference: Optional[str] = None
    stripe_object_id: str = None
    subscription_stripe_id : str = None
    amount_paid: float = None
    remaining_to_pay: float = None
    amount_already_paid: float = None
    status: ReceiptStatusEnum = None

# Properties to receive on creation
class SubscriptionPaymentReceiptCreate(SubscriptionPaymentReceiptBase):
    pass


# Properties to receive on update
class SubscriptionPaymentReceiptUpdate(SubscriptionPaymentReceiptBase):
    pass


# Properties shared by models stored in DB
class SubscriptionPaymentReceiptInDBBase(SubscriptionPaymentReceiptBase):
    id: Optional[int] = None
    

    class Config:
        orm_mode = True


