from .base import BaseSchema
from typing import Any, List, Optional

from .country import CountryBase


# Shared properties
class TestimonyBase(BaseSchema):
    country_id: Optional[int] = None
    flag: Optional[str] = None
    name: Optional[str] = None
    languages: Optional[str] = None
    base_language: Optional[str] = None
    is_default: Optional[bool] = False
    testimony: Optional[str] = None


# Properties to receive on creation
class TestimonyCreate(TestimonyBase):
    country_id: int = None
    flag: str
    name: str
    testimony: str


class TestimonyUpdate(TestimonyBase):
    pass


class TestimonyInDB(TestimonyBase):
    id: int
    country_id: int = None

    class Config:
        orm_mode = True


class Testimony(TestimonyInDB):
    country: Optional[CountryBase]
