from __future__ import annotations
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime, timedelta
 
from .base import BaseSchema


# Shared properties for CustomerEmailItemTracking
class CustomerEmailItemTrackingBase(BaseSchema):
    language: Optional[str] = None
    customer_id: Optional[int] = None

class CustomerEmailItemTrackingCreate(CustomerEmailItemTrackingBase):
    pass

class CustomerEmailItemTrackingUpdate(BaseModel):
    pass

class CustomerEmailItemTrackingInDB(CustomerEmailItemTrackingBase):
    id: int

    class Config:
        orm_mode = True