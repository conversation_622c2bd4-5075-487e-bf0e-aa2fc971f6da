from .base import BaseSchema
from pydantic import BaseModel, EmailStr, constr
from typing import Any, List, Optional
from app.enums.title_enum import TitleEnum
from datetime import datetime,date

# Shared properties
class CustomerPaymentInformationBase(BaseSchema):
    name: Optional[str] = None
    customer_id: Optional[int] = None
    payment_method_id: Optional[int] = None
    card_number: Optional[constr(regex=r'^\d{16}$')] = None
    cardholder_name: Optional[str] = None
    card_expiration_date: Optional[date] = None
    card_cryptogram: Optional[constr(regex=r'^\d{3}$')] = None
    payment_card_id:Optional[int]=None
    # is_valid= Optional[bool] = False

# Properties to receive on creation
class CustomerPaymentInformationCreate(CustomerPaymentInformationBase):
    pass


# Properties to receive on update
class CustomerPaymentInformationUpdate(CustomerPaymentInformationBase):
    pass


# Properties shared by models stored in DB
class CustomerPaymentInformationInDBBase(CustomerPaymentInformationBase):
    id: Optional[int] = None
    name: Optional[str]
    customer_id: Optional[int]
    payment_method_id: Optional[int]
    card_number: Optional[int]
    cardholder_name: Optional[str]
    card_expiration_date: Optional[datetime] = None
    card_cryptogram: Optional[str] = None

    class Config:
        orm_mode = True
        


# Properties to return to client
class CustomerPaymentInformation(CustomerPaymentInformationInDBBase):
    name: Optional[str]
    customer_id: Optional[int]
    payment_method_id: Optional[int]
    card_number: Optional[int]
    cardholder_name: Optional[str]
    card_expiration_date: Optional[datetime] = None
    card_cryptogram: Optional[str] = None
    is_valid: Optional[bool] 


# Response sent to client
class ResponseCustomerPaymentInformation(BaseModel):
    count: Optional[int]
    data: Optional[List[CustomerPaymentInformation]]
