from datetime import datetime

from app.models.subscription_payment_transaction import SubscriptionPaymentTransaction
from .base import BaseSchema
from pydantic import BaseModel
from typing import Any, List, Optional
from .subscription import Subscription
from app.enums.payment_status_enum import PaymentStatusEnum

# Shared properties
class SubscriptionPaymentTransactionBase(BaseSchema): 
    paid: Optional[bool] = None
    currency: Optional[str] = None
    reference: Optional[str] = None
    payment_date: Optional[datetime] = None
    payment_method_id: Optional[int] = None
    subscription_id: Optional[int] = None
    subscription_stripe_id: Optional[str] = None
    next_payment_date: Optional[datetime] = None
    payment_transaction_json: Optional[dict] = None
    cart_id: Optional[int]=None
    is_new_subscription_payment: Optional[bool]=None
    payment_gateway_id:Optional[int]=None
    payment_status:PaymentStatusEnum
    amount_paid: Optional[float]=None
    stripe_object_id: Optional[str]=None
    customer_json: Optional[dict] = None


# Properties to receive on creation
class SubscriptionPaymentTransactionCreate(SubscriptionPaymentTransactionBase):
    pass


# Properties to receive on update
class SubscriptionPaymentTransactionUpdate(SubscriptionPaymentTransactionBase):
    pass


# Properties shared by models stored in DB
class SubscriptionPaymentTransactionInDBBase(SubscriptionPaymentTransactionBase):
    id: Optional[int] = None
    created_by: Optional[int] = None
    subscription_id: Optional[int] = None

    class Config:
        orm_mode = True


# Properties to return to client
class SubscriptionPaymentTransaction(SubscriptionPaymentTransactionInDBBase):
    created_by_user: Optional[Any] = None
    subscription: Optional[Subscription] = None


# Response sent to client
class ResponseSubscriptionPaymentTransaction(BaseModel):
    count: Optional[int]
    data: Optional[List[SubscriptionPaymentTransaction]]
