from decimal import Decimal
from app.enums.location_type_enum import LocationTypeEnum, ProfileEnum
from app.enums.simulation_type_enum import TypeEnum
from .base import BaseSchema
from pydantic import BaseModel, Field
from typing import Any, List, Optional
from .customer import Customer


# Shared properties
class SimulationBase(BaseSchema):
    name: Optional[str] = None
    type: Optional[TypeEnum] = None
    location_type: Optional[LocationTypeEnum] = None
    latitude: Optional[Decimal] = Field(None, ge=-90, le=90)
    longitude: Optional[Decimal] = Field(None, ge=-180, le=180)
    street_number: Optional[str] = None
    street: Optional[str] = None
    city: Optional[str] = None
    country: Optional[str] = None
    country_id: Optional[int] = None
    subscription_id: Optional[int] = None
    profile: ProfileEnum = "residential"
    simulation_info: Optional[dict] = None
    customer_name: Optional[str] = None
    simulation_carbon: Optional[dict] = None
    region_id: Optional[int] = None
    essai_count: int = 1  

# Properties to receive on creation
class SimulationCreate(SimulationBase):
    pass


# Properties to receive on update
class SimulationUpdate(SimulationBase):
    pass


# Properties shared by models stored in DB
class SimulationInDBBase(SimulationBase):

    class Config:
        orm_mode = True


# Properties to return to client
class Simulation(SimulationInDBBase):
    created_by_user: Optional[Customer]


# Response sent to client
class ResponseSimulation(BaseModel):
    count: Optional[int]
    data: Optional[List[Simulation]]
