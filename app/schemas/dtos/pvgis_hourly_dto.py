from pydantic import BaseModel
from typing import Any
from typing import Optional

from app.schemas.dtos.base.base_simulation_dto import BaseSimulationDto

# from src.app.enums.output_format_enum import OutputFormatEnum


class PvgisHourlyDto(BaseSimulationDto):
     
    raddatabase: Optional[str] = None
    startyear: Optional[int] = None
    endyear: Optional[int] = None
    pvcalculation: Optional[int] = None
    peakpower: Optional[float] = None
    pvtechchoice: Optional[str] = None
    mountingplace: Optional[str] = None
    loss: Optional[float] = None
    trackingtype: Optional[int] = None
    angle: Optional[float] = None
    aspect: Optional[float] = None
    optimalinclination: Optional[int] = None
    optimalangles: Optional[int] = None
    components: Optional[int] = None
    
    hendyear: Optional[int] = None
    hstartyear: Optional[int] = None
    hourlyangle: Optional[float] = None
    hourlyaspect: Optional[float] = None
    js: Optional[int] = None
    select_database_hourly: Optional[str] = None
    
