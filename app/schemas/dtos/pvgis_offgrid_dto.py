from pydantic import BaseModel
from typing import Optional
from typing import Any

from app.schemas.dtos.base.base_simulation_dto import BaseSimulationDto

# from src.app.enums.output_format_enum import OutputFormatEnum


class PvgisOffgridDto(BaseSimulationDto):
    
    raddatabase: Optional[str] = None
    peakpower: float
    angle: Optional[float] = None
    aspect: Optional[float] = None
    batterysize: float
    cutoff: float
    consumptionday: float
    hourconsumption: Optional[str] = None
    
