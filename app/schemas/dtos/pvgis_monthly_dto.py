from pydantic import BaseModel
from typing import Optional
from typing import Any

from app.schemas.dtos.base.base_simulation_dto import BaseSimulationDto

# from src.app.enums.output_format_enum import OutputFormatEnum


class PvgisMonthlyDto(BaseSimulationDto):
     
    raddatabase: Optional[str] = None
    startyear: Optional[int] = None
    endyear: Optional[int] = None
    horirrad: Optional[int] = None
    optrad: Optional[int] = None
    selectrad: Optional[int] = None
    angle: Optional[int] = None
    mr_dni: Optional[int] = None
    d2g: Optional[int] = None
    avtemp: Optional[int] = None
    
