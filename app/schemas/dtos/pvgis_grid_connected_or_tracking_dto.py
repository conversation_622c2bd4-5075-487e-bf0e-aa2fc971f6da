from pydantic import BaseModel
from typing import Optional
from typing import Any

from app.schemas.dtos.base.base_simulation_dto import BaseSimulationDto

# from src.app.enums.output_format_enum import OutputFormatEnum


class PvgisGridConnectedOrTrackingDto(BaseSimulationDto):
    raddatabase: Optional[str] = None
    peakpower: Optional[float] = None
    pvtechchoice: Optional[str] = None
    mountingplace: Optional[str] = None
    loss: Optional[float] = None
    fixed: Optional[int] = None
    angle: Optional[float] = None
    aspect: Optional[float] = None
    optimalinclination: Optional[int] = None
    optimalangles: Optional[int] = None
    inclined_axis: Optional[int] = None
    inclined_optimum: Optional[int] = None
    inclinedaxisangle: Optional[float] = None
    vertical_axis: Optional[int] = None
    vertical_optimum: Optional[int] = None
    verticalaxisangle: Optional[int] = None
    twoaxis: Optional[int] = None
    pvprice: Optional[int] = None
    systemcost: Optional[float] = None
    interest: Optional[float] = None
    lifetime: Optional[int] = None
   