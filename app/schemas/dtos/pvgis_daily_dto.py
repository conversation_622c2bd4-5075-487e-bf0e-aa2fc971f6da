from pydantic import BaseModel
from typing import Any
from typing import Optional

from app.schemas.dtos.base.base_simulation_dto import BaseSimulationDto

# from src.app.enums.output_format_enum import OutputFormatEnum


class PvgisDailyDto(BaseSimulationDto):
    raddatabase: Optional[str] = None
    month: int
    angle: Optional[float] = None
    aspect: Optional[float] = None
    isGlobal: Optional[int] = None
    glob_2axis: Optional[int] = None
    clearsky: Optional[int] = None
    clearsky_2axis: Optional[int] = None
    showtemperatures: Optional[int] = None
    localtime: Optional[int] = None
    
