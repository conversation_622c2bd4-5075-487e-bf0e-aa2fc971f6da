from pydantic import BaseModel, EmailStr
from typing import List, Optional
from datetime import datetime
 
from .base import BaseSchema


# Shared properties for Invitation
class InvitationBase(BaseSchema):
    customer_id: Optional[int]
    user_sender_id: Optional[int] = None
    invitation_link: Optional[str] = None
    email: str
    invitation_sent_at: Optional[datetime] = None
    invitation_accepted_at: Optional[datetime] = None

class InvitationCreate(InvitationBase):
    pass

class InvitationUpdate(BaseModel):
    pass

class InvitationAccept(BaseModel):
    id: Optional[int]
    customer_id: int
    email: Optional[str]
    first_name: str
    last_name: str
    password:  Optional[str]
    company_name: Optional[str]
    country: Optional[str]
    accept_cgu: Optional[int]
    professional_category_id: Optional[str]

class InvitationInDB(InvitationBase):
    id: int

    class Config:
        orm_mode = True