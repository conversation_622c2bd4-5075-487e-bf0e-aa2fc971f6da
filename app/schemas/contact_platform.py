from .base import BaseSchema
from pydantic import BaseModel
from typing import List, Optional
from app.enums.contact_type_enum import ContactT<PERSON><PERSON><PERSON>


# Shared properties
class ContactPlatformBase(BaseSchema):
    key: Optional[str] = None
    label: Optional[str] = None
    type: Optional[ContactTypeEnum] = None

# Properties to receive on creation
class ContactPlatformCreate(ContactPlatformBase):
    key: str
    label: str
    type: ContactTypeEnum


# Properties to receive on update
class ContactPlatformUpdate(ContactPlatformBase):
    pass


# Properties shared by models stored in DB
class ContactPlatformInDBBase(ContactPlatformBase):
    id: Optional[int] = None

    class Config:
        orm_mode = True


# Properties to return to client
class ContactPlatform(ContactPlatformInDBBase):
    pass

# Response sent to client
class ResponseContactPlatform(BaseModel):
    count: Optional[int]
    data: Optional[List[ContactPlatform]]
