from .base import BaseSchema
from pydantic import BaseModel
from typing import List, Optional


# Shared properties
class ProfessionalCategoryBase(BaseSchema):
    name: Optional[str] = None
    is_other: Optional[bool] = None

# Properties to receive on creation
class ProfessionalCategoryCreate(ProfessionalCategoryBase):
    name: str


# Properties to receive on update
class ProfessionalCategoryUpdate(ProfessionalCategoryBase):
    pass


# Properties shared by models stored in DB
class ProfessionalCategoryInDBBase(ProfessionalCategoryBase):
    id: Optional[int] = None

    class Config:
        orm_mode = True


# Properties to return to client
class ProfessionalCategory(ProfessionalCategoryInDBBase):
    pass

# Response sent to client
class ResponseProfessionalCategory(BaseModel):
    count: Optional[int]
    data: Optional[List[ProfessionalCategory]]
