 
from app.enums.billing_period_interval_enum import BillingPeriodIntervalEnum
from app.schemas.product_features import ProductFeaturesInDBBase
from .base import BaseSchema
from pydantic import BaseModel
from typing import List, Optional


# Shared properties
class ProductBase(BaseSchema):
    name: Optional[str] = None
    user_count: Optional[int] = 0
    monthly_credit: Optional[float] = 0
    monthly_price: Optional[float] = 0
    is_recommended: Optional[bool] = False
    show: Optional[bool] = False
    allow_new_subscribers: Optional[bool] = False
    description_translation_key: Optional[str] = None
    description_note_translation_key: Optional[str]
    subscription_max_count: Optional[int] = None
    ui_order: Optional[int] = None
    type: Optional[int] = None
    additional: Optional[bool]=False
    discount_stripe_id: Optional[str]=None
    billing_period_interval: Optional[BillingPeriodIntervalEnum]

# Properties to receive on creation
class ProductCreate(ProductBase):
    id: Optional[int] = None
    

 
class ProductUpdate(ProductBase):
    pass


# Properties shared by models stored in DB
class ProductInDBBase(ProductBase):
    id: Optional[int] = None
    product_features: Optional[ProductFeaturesInDBBase] = None

    class Config:
        from_attributes = True


# Properties to return to client
class Product(ProductInDBBase):
    pass


# Response sent to client
class ResponseProduct(BaseModel):
    count: Optional[int]
    data: Optional[List[Product]]
