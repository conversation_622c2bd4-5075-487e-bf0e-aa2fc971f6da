 
from .base import BaseSchema
from datetime import datetime
from pydantic import BaseModel
from typing import Any, List, Optional
from .product import Product
from .customer import Customer
from app.enums.subscription_status_enum import SubscriptionStatusEnum


# Shared properties
class SubscriptionBase(BaseSchema):
    product_id: Optional[int] = None
    product_json: Optional[dict] = None
    credit_balance: Optional[int] = None
    created_by: Optional[int] = None
    disabled_at: Optional[datetime] = None
    start_date: Optional[datetime] = None
    expired_date: Optional[datetime] = None
    renewal_frequency: Optional[int] = None
    subscription_status: Optional[SubscriptionStatusEnum] = None
    is_auto_renew: Optional[bool] = True
    customer_id: Optional[int] = None
    coupon_end:Optional[datetime] = None
    is_additional_credit: Optional[bool] = None
    subscription_stripe_id: Optional[str] = None
    
     
    
# Properties to receive on creation
class SubscriptionCreate(SubscriptionBase):
    pass
class SubscriptionCreateAfterPayment(BaseSchema):
    customer_id: Optional[int] = None
    payment_json:Optional[dict] = None
    auto_renew:Optional[int]=None


# Properties to receive on update
class SubscriptionUpdate(SubscriptionBase):
    pass


# Properties shared by models stored in DB
class SubscriptionInDBBase(SubscriptionBase):
    id: Optional[int] = None
    created_by: Optional[int]
    product_id: Optional[int]
    customer_id: Optional[int]

    class Config:
        orm_mode = True


# Properties to return to client
class Subscription(SubscriptionInDBBase):
    created_by_user: Optional[Any]
    product: Optional[Product]
    customer: Optional[Customer] = None 

# Response sent to client
class ResponseSubscription(BaseModel):
    count: Optional[int]
    data: Optional[List[Subscription]]
