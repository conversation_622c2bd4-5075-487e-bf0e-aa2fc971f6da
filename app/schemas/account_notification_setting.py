from .base import BaseSchema
from pydantic import BaseModel
from typing import List, Optional
from app.enums.age_range_enum import AgeRangeEnum
from .customer import Customer


# Shared properties
class AccountNotificationSettingBase(BaseSchema):
    customer_id: Optional[int] = None
    notification_setting_id: Optional[int] = None

# Properties to receive on creation
class AccountNotificationSettingCreate(AccountNotificationSettingBase):
    pass

class AccountNotificationSettingToggle(BaseModel):
    key: str
    checked: bool

# Properties to receive on update
class AccountNotificationSettingUpdate(AccountNotificationSettingBase):
    pass


# Properties shared by models stored in DB
class AccountNotificationSettingInDBBase(AccountNotificationSettingBase):
    id: Optional[int] = None
    customer_id: Optional[int] = None
    notification_setting_id: Optional[int] = None

    class Config:
        orm_mode = True


# Properties to return to client
class AccountNotificationSetting(AccountNotificationSettingInDBBase):
    customer_id: Optional[int] = None
    notification_setting_id: Optional[int] = None


# Response sent to client
class ResponseAccountNotificationSetting(BaseModel):
    count: Optional[int]
    data: Optional[List[AccountNotificationSetting]]
