from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime, timedelta
from .base import BaseSchema


# Shared properties for CeSenderEmailCampaignCountry
class CeSenderEmailCampaignCountryBase(BaseSchema):
    customer_email_campaign_id: int
    customer_email_sender_id: int
    country_id: int

class CeSenderEmailCampaignCountryCreate(CeSenderEmailCampaignCountryBase):
    pass

class CeSenderEmailCampaignCountryUpdate(BaseModel):
    pass

class CeSenderEmailCampaignCountryInDB(CeSenderEmailCampaignCountryBase):
    id: int

    class Config:
        orm_mode = True
        
class CeSenderEmailCampaignCountryPublish(BaseModel):
    id: int
    
 
        