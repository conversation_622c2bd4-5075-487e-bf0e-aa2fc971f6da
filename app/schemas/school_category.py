from .base import BaseSchema
from pydantic import BaseModel
from typing import List, Optional


# Shared properties
class SchoolCategoryBase(BaseSchema):
    name: Optional[str] = None
    is_other: Optional[bool] = None

# Properties to receive on creation
class SchoolCategoryCreate(SchoolCategoryBase):
    name: str


# Properties to receive on update
class SchoolCategoryUpdate(SchoolCategoryBase):
    pass


# Properties shared by models stored in DB
class SchoolCategoryInDBBase(SchoolCategoryBase):
    id: Optional[int] = None

    class Config:
        orm_mode = True


# Properties to return to client
class SchoolCategory(SchoolCategoryInDBBase):
    pass

# Response sent to client
class ResponseSchoolCategory(BaseModel):
    count: Optional[int]
    data: Optional[List[SchoolCategory]]
