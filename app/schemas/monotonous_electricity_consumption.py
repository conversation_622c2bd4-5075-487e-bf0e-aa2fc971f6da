from pydantic import BaseModel
from typing import Optional
from .base import BaseSchema  


class MonotonousElectricityConsumptionBase(BaseSchema):
    country_id: Optional[int] = None
    residential_consumption_json: Optional[dict] = None
    content: Optional[str] = None
    style: Optional[str] = None  
    region_id: Optional[int] = None 
    use_avg_residential_consumption: Optional[bool] = None



class MonotonousElectricityConsumptionCreate(MonotonousElectricityConsumptionBase):
    pass

# Schema for updating an existing MonotonousElectricityConsumption entry
class MonotonousElectricityConsumptionUpdate(BaseModel):
    residential_consumption_json: Optional[dict] = None
    content: Optional[str] = None
    style: Optional[str] = None  
    region_id: Optional[int] = None  



