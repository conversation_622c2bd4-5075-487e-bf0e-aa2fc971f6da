from .base import BaseSchema
from pydantic import BaseModel
from typing import Any, List, Optional
from .subscription import Subscription


# Shared properties
class PaymentMethodBase(BaseSchema):
    name: Optional[str] = None


# Properties to receive on creation
class PaymentMethodCreate(PaymentMethodBase):
    pass


# Properties to receive on update
class PaymentMethodUpdate(PaymentMethodBase):
    pass


# Properties shared by models stored in DB
class PaymentMethodInDBBase(PaymentMethodBase):
    id: Optional[int] = None

    class Config:
        orm_mode = True


# Properties to return to client
class PaymentMethod(PaymentMethodInDBBase):
    created_by_user: Optional[Any] = None
    subscription: Optional[Subscription] = None


# Response sent to client
class ResponsePaymentMethod(BaseModel):
    count: Optional[int]
    data: Optional[List[PaymentMethod]]
