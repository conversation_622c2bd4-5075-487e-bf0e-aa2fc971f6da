from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime, timedelta
from .base import BaseSchema


# Shared properties for CustomerEmailSender
class CustomerEmailSenderBase(BaseSchema):
    email: str
    name: str

class CustomerEmailSenderCreate(CustomerEmailSenderBase):
    pass

class CustomerEmailSenderUpdate(BaseModel):
    pass

class CustomerEmailSenderInDB(CustomerEmailSenderBase):
    id: int

    class Config:
        orm_mode = True
        
class CustomerEmailSenderPublish(BaseModel):
    id: int
    
 
        