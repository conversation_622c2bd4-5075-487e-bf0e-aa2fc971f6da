from .base import BaseSchema
from pydantic import BaseModel
from typing import Any, List, Optional
from .subscription import Subscription


# Shared properties
class PaymentGatewayBase(BaseSchema):
    name: Optional[str] = None


# Properties to receive on creation
class PaymentGatewayCreate(PaymentGatewayBase):
    pass


# Properties to receive on update
class PaymentGatewayUpdate(PaymentGatewayBase):
    pass


# Properties shared by models stored in DB
class PaymentGatewayInDBBase(PaymentGatewayBase):
    id: Optional[int] = None

    class Config:
        orm_mode = True


# Properties to return to client
class PaymentGateway(PaymentGatewayInDBBase):
    created_by_user: Optional[Any] = None
    subscription: Optional[Subscription] = None


# Response sent to client
class ResponsePaymentGateway(BaseModel):
    count: Optional[int]
    data: Optional[List[PaymentGateway]]
