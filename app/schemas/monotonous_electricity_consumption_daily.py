from pydantic import BaseModel
from typing import Optional
from .base import BaseSchema  


class MonotonousElectricityConsumptionDailyBase(BaseSchema):
    country_id: Optional[int] = None
    residential_consumption_json: Optional[dict] = None
    residential_consumption_sum: Optional[dict] = None
    residential_consumption_percentage_json: Optional[dict] = None 
    content: Optional[str] = None
    style: Optional[str] = None  
    region_id: Optional[int] = None 
    use_avg_residential_consumption: Optional[bool] = None



class MonotonousElectricityConsumptionDailyCreate(MonotonousElectricityConsumptionDailyBase):
    pass

# Schema for updating an existing MonotonousElectricityConsumptionDaily entry
class MonotonousElectricityConsumptionDailyUpdate(BaseModel):
    residential_consumption_json: Optional[dict] = None
    content: Optional[str] = None
    style: Optional[str] = None  
    region_id: Optional[int] = None  



