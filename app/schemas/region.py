from pydantic import BaseModel
from typing import Optional
from .base import BaseSchema

class RegionBase(BaseSchema):
    name: str
    short_code: str
    normalized_name: Optional[str] = None
    country_id: Optional[int] = None

class RegionCreate(RegionBase):
    pass

class RegionUpdate(RegionBase):
    name: Optional[str]
    short_code: Optional[str]
    normalized_name: Optional[str]

class RegionInDB(RegionBase):
    id: int

    class Config:
        orm_mode = True
