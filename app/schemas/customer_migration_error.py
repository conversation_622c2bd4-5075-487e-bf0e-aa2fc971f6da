from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime, timedelta 
from .base import BaseSchema


# Shared properties for CustomerMigrationError
class CustomerMigrationErrorBase(BaseSchema):
    company: Optional[str]
    email: Optional[str]
    import_id: Optional[str]
    source_row_id: Optional[int]
    source_file_name: Optional[int]
    error: Optional[str]
    

class CustomerMigrationErrorCreate(CustomerMigrationErrorBase):
    pass

class CustomerMigrationErrorUpdate(BaseModel):
    pass

class CustomerMigrationErrorInDB(CustomerMigrationErrorBase):
    id: int

    class Config:
        orm_mode = True
        
class CustomerMigrationErrorPublish(BaseModel):
    id: int
     
        