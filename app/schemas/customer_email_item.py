from __future__ import annotations
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime, timedelta
 
from .base import BaseSchema


# Shared properties for CustomerEmailItem
class CustomerEmailItemBase(BaseSchema):
    sent_at: Optional[datetime] = None
    language: Optional[str] = None
    customer_id: Optional[int] = None
    country_id: Optional[int] = None 
    customer_email_task_id: Optional[int] = None

class CustomerEmailItemCreate(CustomerEmailItemBase):
    pass

class CustomerEmailItemUpdate(BaseModel):
    pass

class CustomerEmailItemInDB(CustomerEmailItemBase):
    id: int

    class Config:
        orm_mode = True
        
class CustomerEmailItemPublish(BaseModel):
    id: int
    
class CustomerEmailItemGenerateParams:
    chunk_size_per_task: int
    current_datetime: datetime
    account_type_id: int
    special_offer_duration: timedelta
    chunk_size: int 
    
    def __init__(self,  
                current_datetime: datetime =None, 
                account_type_id: int=None, 
                special_offer_duration: timedelta  =None,
                chunk_size_per_task: int = None,
                chunk_size: int = None ):
        self.current_datetime = current_datetime
        self.account_type_id = account_type_id
        self.special_offer_duration = special_offer_duration
        self.chunk_size_per_task = chunk_size_per_task
        self.chunk_size = chunk_size
        
        
        
        if not self.special_offer_duration:
            self.set_special_offer_duration()
        
    def setup_chunk_size(self, task_count):
        if self.chunk_size:
            chunk_size_per_task = self.chunk_size // task_count
            if (
                not self.chunk_size_per_task
                or (self.chunk_size_per_task and self.chunk_size_per_task > chunk_size_per_task)
            ):
                 self.chunk_size_per_task = chunk_size_per_task 
            
             
     


        