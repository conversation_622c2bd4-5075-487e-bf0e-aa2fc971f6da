from decimal import Decimal
from app.enums.location_type_enum import LocationTypeEnum
from app.enums.simulation_type_enum import SimulationTypeEnum
from .base import BaseSchema
from pydantic import BaseModel, Field
from typing import Any, List, Optional
from .customer import Customer


# Shared properties
class SimulationItemBase(BaseSchema):
    name: Optional[str] = None
    simulation_type: Optional[str] = None
    params_json: Optional[dict] = None
    simulation_id: Optional[int] = None
    created_by: Optional[int] = None


# Properties to receive on creation
class SimulationItemCreate(SimulationItemBase):
    pass


# Properties to receive on update
class SimulationItemUpdate(SimulationItemBase):
    pass


# Properties shared by models stored in DB
class SimulationItemInDBBase(SimulationItemBase):
    id: Optional[int] = None
    created_by: Optional[int]

    class Config:
        orm_mode = True


# Properties to return to client
class SimulationItem(SimulationItemInDBBase):
    created_by_user: Optional[Customer]


# Response sent to client
class ResponseSimulationItem(BaseModel):
    count: Optional[int]
    data: Optional[List[SimulationItem]]
