from .base import BaseSchema
from pydantic import BaseModel,EmailStr
from typing import List, Optional
from .subscription import Subscription

# Shared properties
class SubscriptionUserBase(BaseSchema):
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    is_active: Optional[bool] = None
    auth_user_id: Optional[int] = None
    email: Optional[str] = None
    pseudo: Optional[str] = None
    subscription_id: Optional[int] = None


# Properties to receive on creation
class SubscriptionUserCreate(SubscriptionUserBase):
    pass


# Properties to receive on update
class SubscriptionUserUpdate(SubscriptionUserBase):
    pass

# Properties to copy user
# class SubscriptionUserCopy():
#     customer_auth_user_id: Optional[int] = None
#     new_subscription_id: Optional[int] = None
#     old_subscription_id: Optional[int] = None
    
# Properties shared by models stored in DB
class SubscriptionUserInDBBase(SubscriptionUserBase):
    id: Optional[int] = None
    subscription_id: Optional[int]

    class Config:
        orm_mode = True


# Properties to return to client
class SubscriptionUser(SubscriptionUserInDBBase):
    subscription: Optional[Subscription]


# Response sent to client
class ResponseSubscriptionUser(BaseModel):
    count: Optional[int]
    data: Optional[List[SubscriptionUser]]

class SubscriptionUserCreateOrUpdate(BaseModel):
    # id: Optional[int] = None
    email: EmailStr
    password: str
class SubscriptionUserMultiple(BaseModel):
    # id: Optional[int] = None
    data: List[SubscriptionUserCreateOrUpdate]
    
class SubscriptionUserResetPassword(BaseModel):
    # id: Optional[int] = None
    password: str