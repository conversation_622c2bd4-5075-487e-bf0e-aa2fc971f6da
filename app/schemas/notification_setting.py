from .base import BaseSchema
from pydantic import BaseModel
from typing import List, Optional
from app.enums.age_range_enum import AgeRangeEnum
from .customer import Customer

from app.enums.type_notification_setting_enum import TypeNotificationSettingEnum

# Shared properties
class NotificationSettingBase(BaseSchema):
    key: Optional[str] = None
    name: Optional[str] = None
    type: TypeNotificationSettingEnum = None

# Properties to receive on creation
class NotificationSettingCreate(NotificationSettingBase):
    key: str
    name: str
    type: TypeNotificationSettingEnum


# Properties to receive on update
class NotificationSettingUpdate(NotificationSettingBase):
    pass


# Properties shared by models stored in DB
class NotificationSettingInDBBase(NotificationSettingBase):
    id: Optional[int] = None
    key: Optional[str] = None
    name: Optional[str] = None
    type: TypeNotificationSettingEnum

    class Config:
        orm_mode = True


# Properties to return to client
class NotificationSetting(NotificationSettingInDBBase):
    key: Optional[str] = None
    name: Optional[str] = None
    type: TypeNotificationSettingEnum


# Response sent to client
class ResponseNotificationSetting(BaseModel):
    count: Optional[int]
    data: Optional[List[NotificationSetting]]
