from app.schemas.product_features import ProductFeaturesInDBBase
from .base import BaseSchema
from pydantic import BaseModel
from typing import List, Optional


# Shared properties
class FeaturesBase(BaseSchema):
   
    name: Optional[str] = None
    description: Optional[str] = None
    key: Optional[str] = None
    value: Optional[str] = None
    ui_order: Optional[int] = None
    display_for_free: Optional[bool] = None
    allow_for_free: Optional[bool] = None
    hide: Optional[bool] = None

# Properties to receive on creation
class FeaturesCreate(FeaturesBase):
    id: Optional[int] = None
    pass


# Properties to receive on update
 

class FeaturesUpdate(FeaturesBase):
    pass


# Properties shared by models stored in DB
class FeaturesInDBBase(FeaturesBase):
    id: Optional[int] = None
    product_features: Optional[ProductFeaturesInDBBase] = None

    class Config:
        from_attributes = True


# Properties to return to client
class Features(FeaturesInDBBase):
    pass


# Response sent to client
class ResponseFeatures(BaseModel):
    count: Optional[int]
    data: Optional[List[Features]]
