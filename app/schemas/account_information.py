from app.schemas.customer_contact_platform_information import CustomerContactPlatformInformationCreate
from .base import BaseSchema
from pydantic import BaseModel
from typing import List, Any, Optional
from app.enums.age_range_enum import AgeRangeEnum
from .customer import Customer
from .account_type import AccountType
from .professional_category import ProfessionalCategory
from .school_category import SchoolCategory
from ..enums.support_enum import SupportEnum


# Shared properties
class AccountInformationBase(BaseSchema):
    customer_id: Optional[int] = None
    account_type_id: Optional[int] = None
    professional_category_id: Optional[int] = None
    school_category_id: Optional[int] = None
    profession: Optional[str] = None
    age_range: Optional[AgeRangeEnum] = None
    siret_number: Optional[str] = None
    company_name: Optional[str] = None
    support: Optional[SupportEnum] = None
    support_verified: Optional[bool] = False
    other_category: Optional[str] = None


# Properties to receive on creation
class AccountInformationCreate(AccountInformationBase):
    pass


class FullAccountInformationCreate(AccountInformationCreate):
    pseudo: Optional[str] = None
    username: Optional[str] = None
    lastname: Optional[str] = None
    firstname: Optional[str] = None
    profile_image_json: Optional[dict] = None
    company_logo_json: Optional[dict] = None
    rue: Optional[str] = None
    postal_code: Optional[str] = None
    city: Optional[str] = None
    timezone_offset: Optional[str] = None
    country_id: Optional[int] = None
    country: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[str] = None
    social_medias: Optional[List[Any]] = None
    other_category: Optional[str] = None


# Properties to receive on update
class AccountInformationUpdate(AccountInformationBase):
    pass


# Properties shared by models stored in DB
class AccountInformationInDBBase(AccountInformationBase):
    id: Optional[int] = None
    customer_id: Optional[int] = None
    account_type_id: Optional[int] = None
    professional_category_id: Optional[int] = None
    school_category_id: Optional[int] = None

    class Config:
        orm_mode = True


# Properties to return to client
class AccountInformation(AccountInformationInDBBase):
    customer: Optional[Customer] = None
    account_type: Optional[AccountType] = None
    professional_category: Optional[ProfessionalCategory] = None
    school_category: Optional[SchoolCategory] = None


# Response sent to client
class ResponseAccountInformation(BaseModel):
    count: Optional[int]
    data: Optional[List[AccountInformation]]
