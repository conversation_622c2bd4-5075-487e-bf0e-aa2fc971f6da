from app.enums.billing_period_interval_enum import BillingPeriodIntervalEnum
from app.schemas.product_features import ProductFeaturesInDBBase
from .base import BaseSchema
from pydantic import BaseModel
from typing import List, Optional


# Shared properties
class ProductAltPricingBase(BaseSchema):
    billing_period_interval: BillingPeriodIntervalEnum
    product_id: int
    stripe_price_id: Optional[str]
    discount_stripe_id: Optional[str]
    credit: Optional[float]
    price: Optional[float]


# Properties to receive on creation
class ProductAltPricingCreate(ProductAltPricingBase):
    pass


# Properties to receive on update
class ProductAltPricingUpdate(ProductAltPricingBase):
    pass


class ProductAltPricingInDB(ProductAltPricingBase):
    id: int

    class Config:
        orm_mode = True
