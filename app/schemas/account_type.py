from .base import BaseSchema
from pydantic import BaseModel
from typing import List, Optional


# Shared properties
class AccountTypeBase(BaseSchema):
    name: Optional[str] = None

# Properties to receive on creation
class AccountTypeCreate(AccountTypeBase):
    name: str


# Properties to receive on update
class AccountTypeUpdate(AccountTypeBase):
    pass


# Properties shared by models stored in DB
class AccountTypeInDBBase(AccountTypeBase):
    id: Optional[int] = None

    class Config:
        orm_mode = True


# Properties to return to client
class AccountType(AccountTypeInDBBase):
    pass

# Response sent to client
class ResponseAccountType(BaseModel):
    count: Optional[int]
    data: Optional[List[AccountType]]
