from pydantic import BaseModel
from typing import Any, List, Optional
from datetime import datetime
from .base import BaseSchema


# Shared properties for CeCampaignCountryData
class CeCampaignCountryDataBase(BaseSchema):
    country_id:int
    customer_email_campaign_id:int
    default_customer_json:Optional[str] = None
    default_language:Optional[Any] = None
    
    
class CeCampaignCountryDataCreate(CeCampaignCountryDataBase):
    pass

class CeCampaignCountryDataUpdate(BaseModel):
    pass

class CeCampaignCountryDataInDB(CeCampaignCountryDataBase):
    id: int

    class Config:
        orm_mode = True
         