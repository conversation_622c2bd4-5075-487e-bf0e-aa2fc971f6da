from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime, timedelta 
from .base import BaseSchema


# Shared properties for CustomerEmailCampaign
class CustomerEmailCampaignBase(BaseSchema):
    id: Optional[int] = None
    name: Optional[str] = None
    key:  Optional[str] = None
    is_active: Optional[bool] = None
    

class CustomerEmailCampaignCreate(CustomerEmailCampaignBase):
    pass

class CustomerEmailCampaignUpdate(BaseModel):
    pass

class CustomerEmailCampaignInDB(CustomerEmailCampaignBase):
    id: int

    class Config:
        orm_mode = True
        
class CustomerEmailCampaignPublish(BaseModel):
    id: int
     
        