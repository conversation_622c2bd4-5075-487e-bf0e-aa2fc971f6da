from enum import Enum


class SimulationTypeEnum(str, Enum):
    GRID_CONNECTED = "grid_connected"
    TRACKING = "tracking"
    DAILY = "daily"
    HOURLY = "hourly"
    MONTHLY = "monthly"
    OFFGRID = "offgrid"
    TMY = "tmy"


class TypeEnum(str, Enum):
    TOTAL_RESALE = "total_resale"
    SELF_CONSUMPTION_RESALE_OF_SURPLUS = "self_consumption_plus_resale_of_surplus"
    SIMPLE_SELF_CONSUMPTION = "simple_self_consumption"
    SELF_CONSUMPTION_PLUS_BATTERIES = "self_consumption_plus_batteries"
    AUTONOMY_PLUS_PUBLIC_GRID = "autonomy_plus_public_grid"
    BATTERY_AUTONOMY = "battery_autonomy"
    ISOLATED_PARK = "isolated_park"
