import os
from functools import lru_cache
import random
from numpy import full
from pydantic import BaseSettings
from pathlib import Path
from dotenv import load_dotenv
from urllib.parse import quote_plus
from typing import Any, List

from app.enums.billing_period_interval_enum import BillingPeriodIntervalEnum

env_path = Path(".") / ".env"
load_dotenv(dotenv_path=env_path)


class Settings(BaseSettings):
    # App
    APP_NAME: str = os.environ.get("APP_NAME", "FastAPI")
    DEBUG: bool = bool(os.environ.get("DEBUG", False))

    DOCS_URL: str = os.environ.get("DOCS_URL")
    REDOC_URL: str = os.environ.get("REDOC_URL")

    # MySql Database Config
    MYSQL_HOST: str = os.environ.get("MYSQL_HOST", "localhost")
    MYSQL_USER: str = os.environ.get("MYSQL_USER", "root")
    MYSQL_PASSWORD: str = os.environ.get("MYSQL_PASSWORD", "secret")
    MYSQL_PORT: int = int(os.environ.get("MYSQL_PORT", 3306))
    MYSQL_DATABASE: str = os.environ.get("MYSQL_DATABASE", "fastapi")
    DATABASE_URI: str = (
        f"mysql+pymysql://{MYSQL_USER}:{MYSQL_PASSWORD}@{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DATABASE}"
    )

    # Mongo UserActivity Database Config
    MONGO_HOST: str = os.getenv("MONGO_HOST")
    MONGO_PORT: int = os.getenv("MONGO_PORT")
    MONGO_USER: str = os.getenv("MONGO_USER")
    MONGO_PASSWORD: str = os.getenv("MONGO_PASSWORD")
    MONGO_USER_ACTIVITY_DB: str = os.getenv("MONGO_USER_ACTIVITY_DB")
    MONGO_USER_ACTIVITY_COLLECTION: str = os.getenv("MONGO_USER_ACTIVITY_COLLECTION")
    MONGO_AUTH_SOURCE: str = os.getenv("MONGO_AUTH_SOURCE")
    MONGO_DB_URI = f"mongodb://{MONGO_USER}:{MONGO_PASSWORD}@{MONGO_HOST}:{MONGO_PORT}/?authSource={MONGO_AUTH_SOURCE}"

    # App Secret Key
    SECRET_KEY: str = os.environ.get(
        "SECRET_KEY", "8deadce9449770680910741063cd0a3fe0acb62a8978661f421bbcbb66dc41f1"
    )
    APP_SECRET_KEY: str = os.environ.get(
        "APP_SECRET_KEY", ""
    )
    INVITATION_SECRET_KEY: str = os.environ.get(
        "INVITATION_SECRET_KEY", ""
    )
    STRIPE_SECRETE_KEY = os.environ.get(
        "STRIPE_SECRETE_KEY", "8deadce9449770680910741063cd0a3fe0acb62a8978661f421bbcbb66dc41f1"
    )
    STRIPE_ENDPOINT_SECRET = os.environ.get(
        "STRIPE_ENDPOINT_SECRET", ""
    )
    STRIPE_COUPON_FIFTY_ID = os.environ.get(
        "STRIPE_COUPON_FIFTY_ID", ""
    )
    ORIGI_PVGIS_API_BASE_URLS= os.getenv("ORIGI_PVGIS_API_BASE_URLS", "") 
    ORIGI_PVGIS_API_PATH = os.getenv("ORIGI_PVGIS_API_PATH", "")
    AUTH_API_URL = os.getenv("AUTH_API_URL", "")
    NOTIFICATION_API = os.getenv("NOTIFICATION_API", "")
    APP_KEY = os.getenv("APP_KEY", "")
    PVGIS_UI_URL = os.getenv("PVGIS_UI_URL", "")
    PVGIS_UI_APP_KEY = os.getenv("PVGIS_UI_APP_KEY", "")
    PVGIS_ADMIN_APP_KEY = os.getenv("PVGIS_ADMIN_APP_KEY", "")
    PVGIS_API_BASE_URL = os.getenv("PVGIS_API_BASE_URL", "")
    SETTINGS_API_URL = os.getenv("SETTINGS_API_URL", "")
    SUPPORT_EMAIL = os.getenv("SUPPORT_EMAIL", "")
    WEBHOOK_ADMIN_EMAIL = os.getenv("WEBHOOK_ADMIN_EMAIL", "")
    SMPT_USER_MAIL = os.getenv("SMPT_USER_MAIL", "")
    BILLING_EMAIL = os.getenv("BILLING_EMAIL", "")
    BILLING_EMAIL_PASSWORD = os.getenv("BILLING_EMAIL_PASSWORD", "")
    SMPT_USER_PASSWORD = os.getenv("SMPT_USER_PASSWORD", "")
    MAIL_API = os.getenv("MAIL_API", "")

    BACKEND_URL = os.getenv("BACKEND_URL", "")
    # Google Auth Credentials
    GOOGLE_CLIENT_ID: str = os.environ.get("GOOGLE_CLIENT_ID", "")
    GOOGLE_CLIENT_SECRET: str = os.environ.get("GOOGLE_CLIENT_SECRET", "")
    GOOGLE_REDIRECT_URI: str = os.environ.get("GOOGLE_REDIRECT_URI", "")
    GOOGLE_LOGIN_REDIRECT_URI: str = os.environ.get("GOOGLE_LOGIN_REDIRECT_URI", "")

    # Facebook Auth Credentials
    FACEBOOK_CLIENT_ID: str = os.environ.get("FACEBOOK_CLIENT_ID", "")
    FACEBOOK_CLIENT_SECRET: str = os.environ.get("FACEBOOK_CLIENT_SECRET", "")
    FACEBOOK_REDIRECT_URI: str = os.environ.get("FACEBOOK_REDIRECT_URI", "")
    FACEBOOK_LOGIN_REDIRECT_URI: str = os.environ.get("FACEBOOK_LOGIN_REDIRECT_URI", "")

    # Google Analytics
    GOOGLE_ANALYTICS_CREDENTIALS_URI: str = os.environ.get("GOOGLE_ANALYTICS_CREDENTIALS_URI", "")
    GOOGLE_ANALYTICS_PROPERTY_ID: str = os.environ.get("GOOGLE_ANALYTICS_PROPERTY_ID", "")
    GOOGLE_ANALYTICS_MEASUREMENT_ID = os.environ.get("GOOGLE_ANALYTICS_MEASUREMENT_ID")
    GOOGLE_ANALYTICS_API_SECRET = os.environ.get("GOOGLE_ANALYTICS_API_SECRET")

    

    GOOGLE_GEOCODE_URL =  os.environ.get("GOOGLE_GEOCODE_URL", "")
    GOOGLE_MAPS_API_KEY =  os.environ.get("GOOGLE_MAPS_API_KEY", "")
    FS_BASE_URL = os.environ.get("FS_BASE_URL", "")
    CITY_FILE_PATH= os.environ.get("CITY_FILE_PATH", "")
    
    CUSTOMER_EMAIL_SENDER_PASSWORD= os.environ.get("CUSTOMER_EMAIL_SENDER_PASSWORD", "")
    CMS_URL= os.environ.get("CMS_URL", "")
    TRANSLATION_URL= os.environ.get("TRANSLATION_URL", "")
    LOGSTASH_URL= os.environ.get("LOGSTASH_URL", "")
    ELASTIC_PASSWORD= os.environ.get("ELASTIC_PASSWORD", "")
    ELASTIC_USERNAME= os.environ.get("ELASTIC_USERNAME", "")
    ELASTIC_INDEX= os.environ.get("ELASTIC_INDEX", "")
    DEFAULT_CAMPAIGN_MAIL_CHUNK_SIZE = os.environ.get("DEFAULT_CAMPAIGN_MAIL_CHUNK_SIZE", "")
    DEFAULT_CAMPAIGN_MAIL_CHUNK_SIZE_PER_SENDER = os.environ.get("DEFAULT_CAMPAIGN_MAIL_CHUNK_SIZE_PER_SENDER", "")
    STRIPE_DASHBOARD_BASE_URL= os.environ.get("STRIPE_DASHBOARD_BASE_URL", "")
    PRINT_URL= os.environ.get("PRINT_URL", "https://print.pvgis.com/pvgis") 
    INSTALLERS_FILE_NAME= os.environ.get("INSTALLERS_FILE_NAME", "installers.xlsx")
    CUSTOMER_EMAIL_REPORT_RECIPIENT= os.environ.get("CUSTOMER_EMAIL_REPORT_RECIPIENT", "<EMAIL>")
    STEP_EMAIL = 5 * 60  # five minute per sender

    SENDERS = [
        {'email': '<EMAIL>', 'password': 'M6qerFR7Bt9cJjmb'},
        {'email': '<EMAIL>', 'password': '4U:f]2t%,{_U"}fy'},
        {'email': '<EMAIL>', 'password': '4Jzd461zUj7F3QrG'},
    ]

    BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))


    def get_pvgis_api_url(self):
        full_url = random.choice(self.ORIGI_PVGIS_API_BASE_URLS.split(','))+self.ORIGI_PVGIS_API_PATH 
        return full_url


    def get_google_auth_url(self):
        return f"{self.PVGIS_API_BASE_URL}{self.GOOGLE_REDIRECT_URI}"

    def get_facebook_auth_url(self):
        return f"{self.PVGIS_API_BASE_URL}{self.FACEBOOK_REDIRECT_URI}"

    def get_google_login_auth_url(self):
        return f"{self.PVGIS_API_BASE_URL}{self.GOOGLE_LOGIN_REDIRECT_URI}"

    def get_facebook_login_auth_url(self):
        return f"{self.PVGIS_API_BASE_URL}{self.FACEBOOK_LOGIN_REDIRECT_URI}"

    # APPID = 1
    # ROLEID = 2
    PVGIS_APP_ID = 1
    CUSTOMER_ROLE_ID = 2

    X_APP_KEY = os.environ.get("X_APP_KEY", "")

    x_app_key_header = {"x-app-key": os.environ.get("X_APP_KEY", "")}

    IS_PROD: bool = os.environ.get("IS_PROD", "False") == "True"
    
    BREVO_API_KEY = os.environ.get("BREVO_API_KEY", "")
    BREVO_LIST_ID = os.environ.get("BREVO_LIST_ID", 8)
    BREVO_API_URL = os.environ.get("BREVO_API_URL", "")

    DEFAULT_ACCOUNT_TYPE: List[Any] = [
        {"name": "Professionnel"},
        {"name": "Particulier"},
        {"name": "École"},
    ]

    DEFAULT_PROFESSIONAL_CATEGORY: List[Any] = [
        {
            "name": "pvgis.professional_category.artisan_installer",
            "is_other": False,
        },
        {
            "name": "pvgis.professional_category.local_installer",
            "is_other": False,
        },
        {
            "name": "pvgis.professional_category.regional_installer",
            "is_other": False,
        },
        {
            "name": "pvgis.professional_category.national_installer",
            "is_other": False,
        },
        {"name": "pvgis.professional_category.engineering_office", "is_other": False},
        {"name": "pvgis.professional_category.electrician", "is_other": False},
        {"name": "pvgis.professional_category.engineer", "is_other": False},
        {"name": "pvgis.professional_category.architect", "is_other": False},
        {"name": "pvgis.professional_category.real_estate_developer", "is_other": False},
        {"name": "pvgis.professional_category.individual_home_builder", "is_other": False},
        {"name": "pvgis.professional_category.electric_car_dealership", "is_other": False},
        {"name": "pvgis.professional_category.national_electricity_company", "is_other": False},
        {"name": "pvgis.professional_category.private_electricity_company", "is_other": False},
        {"name": "pvgis.professional_category.others", "is_other": True},
    ]

    DEFAULT_SCHOOL_CATEGORY: List[Any] = [
        {"name": "pvgis.account_info.school", "is_other": False, "default_order": 1},
        {"name": "pvgis.school_category.university", "is_other": False, "default_order": 2},
        {"name": "pvgis.school_category.higher_education", "is_other": False, "default_order": 3},
        {"name": "pvgis.school_category.training_center", "is_other": False, "default_order": 4},
        {"name": "pvgis.school_category.national_technical_institute", "is_other": False, "default_order": 5},
        {"name": "pvgis.school_category.association", "is_other": False, "default_order": 6},
        {"name": "pvgis.school_category.others", "is_other": True, "default_order": 7},
    ]

    DEFAULT_PRODUCT: List[Any] = [
        {
            "billing_period_interval": BillingPeriodIntervalEnum.month,
            "name": "PVGIS24 PRIME",
            "user_count": 1,
            "monthly_credit": 10,
            "monthly_price": 9,
            "show": True,
            "allow_new_subscribers": True,
            "is_recommended": False,
            "description_translation_key": "PVGIS24 PRIME",
            "ui_order": 4,
            "discount_stripe_id": STRIPE_COUPON_FIFTY_ID
        },
        {
            "billing_period_interval": BillingPeriodIntervalEnum.month,
            "name": "PVGIS24 PREMIUM",
            "user_count": 1,
            "monthly_credit": 25,
            "monthly_price": 9,
            "show": True,
            "allow_new_subscribers": True,
            "is_recommended": False,
            "description_translation_key": "PVGIS24 PREMIUM",
            "ui_order": 5,
            "discount_stripe_id": STRIPE_COUPON_FIFTY_ID
        },
        {
            "billing_period_interval": BillingPeriodIntervalEnum.month,
            "name": "PVGIS24 PRO",
            "user_count": 2,
            "monthly_credit": 50,
            "monthly_price": 19,
            "show": True,
            "allow_new_subscribers": True,
            "is_recommended": True,
            "description_translation_key": "PVGIS24 PRO",
            "ui_order": 6,
            "discount_stripe_id": STRIPE_COUPON_FIFTY_ID
        },
        {
            "billing_period_interval": BillingPeriodIntervalEnum.month,
            "name": "PVGIS24 EXPERT",
            "user_count": 3,
            "monthly_credit": 100,
            "monthly_price": 29,
            "show": True,
            "allow_new_subscribers": True,
            "is_recommended": False,
            "description_translation_key": "PVGIS24 EXPERT",
            "ui_order": 7,
            "discount_stripe_id": STRIPE_COUPON_FIFTY_ID

        },
        {
            "billing_period_interval": BillingPeriodIntervalEnum.month,
            "name": "PVGIS24 TEST SIMULATOR",
            "user_count": 1,
            "monthly_credit": 1,
            "monthly_price": 0,
            "show": False,
            "allow_new_subscribers": True,
            "is_recommended": False,
            "description_translation_key": None,
            "ui_order": 2,
            "subscription_max_count": 1
        },
        {
            "billing_period_interval": BillingPeriodIntervalEnum.month,
            "name": "PVGIS24 CALCULATOR À 3,90€ / MOIS",
            "user_count": 1,
            "monthly_credit": 1,
            "monthly_price": 3.90,
            "show": False,
            "allow_new_subscribers": True,
            "is_recommended": False,
            "description_translation_key": None,
            "ui_order": 1,
            "discount_stripe_id": STRIPE_COUPON_FIFTY_ID
        },
        {
            "billing_period_interval": BillingPeriodIntervalEnum.once,
            "name": "PVGIS24 ADDITIONAL CREDIT",
            "user_count": 1,
            "monthly_credit": 10,
            "monthly_price": 10,
            "show": False,
            "allow_new_subscribers": True,
            "is_recommended": False,
            "description_translation_key": None,
            "ui_order": 10,
            "additional": True
        },
    ]
    
    DEFAULT_YEARLY_PRODUCT: List[Any] = [
        
        {
            "id": 8,
            "billing_period_interval": BillingPeriodIntervalEnum.year,
            "name": "PVGIS24 PREMIUM",
            "user_count": 1,
            "monthly_credit": 120,
            "monthly_price": 99,
            "show": True,
            "allow_new_subscribers": True,
            "is_recommended": False,
            "description_translation_key": "pvgis.product_premium_bref_desc",
            "description_note_translation_key": "pvgis.product_pricing.premium",
            "ui_order": 5,
        },
        {
            "id": 9,
            "billing_period_interval": BillingPeriodIntervalEnum.year,
            "name": "PVGIS24 PRO",
            "user_count": 2,
            "monthly_credit": 300,
            "monthly_price": 199,
            "show": True,
            "allow_new_subscribers": True,
            "is_recommended": True,
            "description_translation_key": "pvgis.product_pro_bref_desc",
            "description_note_translation_key": "pvgis.product_pricing.pro",
            "ui_order": 6,
            "discount_stripe_id": STRIPE_COUPON_FIFTY_ID
        },
        {
            "id": 10,
             "billing_period_interval": BillingPeriodIntervalEnum.year,
            "name": "PVGIS24 EXPERT",
            "user_count": 3,
            "monthly_credit": 600,
            "monthly_price": 299,
            "show": True,
            "allow_new_subscribers": True,
            "is_recommended": False,
            "description_translation_key": "pvgis.product_expert_bref_desc",
            "description_note_translation_key": "pvgis.product_pricing.expert",
            "ui_order": 7,

        },
    ]

    DEFAULT_FEATURES: List[Any] = [
        
        {
            "id": 1,
            "name": "Nombre dutilisateur",
            "description": "nombre dutilisateur",
            "key": "subscription_user_count",
            "value": None,
            "created_at": "2024-09-19 12:06:38",
            "updated_at": None,
            "deleted_at": None,
            "last_user_to_interact": None,
            "display_for_free": True,
            "ui_order": 1
        },
        {
            "id":2,
            "name": "PVGIS 5.3 acces direct",
            "description": "PVGIS 5.3 acces direct",
            "key":"direct_access_5_3",
            "display_for_free": True,
            "ui_order": 2
        },
        {
            "id":3,
            "name": "PVGIS24 acces 4 sections de toit",
            "description": "PVGIS24 acces 4 sections de toit",
            "key":"access_4_sections",
            "ui_order": 3,
            "display_for_free": True,
            "allow_for_free": True
        },
        {
            "id":4,
            "name": "Simulations financieres revente",
            "description": "Simulations financieres revente",
            "key":"simulation_rev",
            "ui_order": 4,
            "display_for_free": True,
            "allow_for_free": False
        },
        {
            "id":5,
            "name": "Simulations financieres autoconso",
            "description": "Simulations financieres autoconso",
            "key":"simulation_autoconso",
            "ui_order": 5,
            "display_for_free": True,
            "allow_for_free": False
        },
        {
            "id":6,
            "name": "Simulations financieres Autonomie",
            "description": "Simulations financieres Autonomie",
            "key":"simulation_autonomy",
            "ui_order": 6,
             "display_for_free": True,
             "allow_for_free": False
        },
        {
            "id":7,
            "name": "Gestion des dossiers",
            "description": "Gestion des dossiers",
            "key": "project_management",
            "value": None,
            "created_at": "2024-08-06 12:56:28",
            "updated_at": None,
            "deleted_at": None,
            "last_user_to_interact": None,
            "ui_order": 7,
            "display_for_free": True,
            "allow_for_free": True
        },
        {
            "id":8,
            "name": "Sauvegarde des dossiers",
            "description": "Sauvegarde des dossiers",
            "key": "saving_projects",
            "value": None,
            "created_at": "2024-08-06 12:56:30",
            "updated_at": None,
            "deleted_at": None,
            "last_user_to_interact": None,
            "ui_order": 8,
            "display_for_free": True,
            "allow_for_free": True
        },
        {
            "id":9,
            "name": "Sauvegarde des simulations",
            "description": "Sauvegarde des simulations",
            "key": "saving_simulations",
            "value": None,
            "created_at": "2024-08-06 12:56:30",
            "updated_at": None,
            "deleted_at": None,
            "last_user_to_interact": None,
            "ui_order": 9,
            "display_for_free": False,
            "allow_for_free": False
        },
        {
            "id":10,
            "name": "Support technique en ligne",
            "description": "Support technique en ligne",
            "key": "online_technical_support",
            "value": None,
            "created_at": "2024-08-06 12:56:30",
            "updated_at": None,
            "deleted_at": None,
            "last_user_to_interact": None,
            "ui_order": 10,
            "display_for_free": True,
            "allow_for_free": False
        },
        {
            "id":11,
            "name": "Utilisation commerciale autorisée",
            "description": "Utilisation commerciale autorisée",
            "key": "commercial_use_permitted",
            "value": None,
            "created_at": "2024-08-06 12:56:31",
            "updated_at": None,
            "deleted_at": None,
            "last_user_to_interact": None,
            "ui_order": 11,
            "display_for_free": True
        },
         
    ]
    DEFAULT_PRODUCT_FEATURES: List[Any] = [
        # PREMIUM monthly
        {
            "product_id": 2,
            "features_id": 1,
             
        },
        {
            "product_id": 2,
            "features_id": 2,
        },
        {
            "product_id": 2,
            "features_id": 3,
        },
        {
            "product_id": 2,
            "features_id": 4,
        },
        {
            "product_id": 2,
            "features_id": 5,
            "allow": False
        },
        {
            "product_id": 2,
            "features_id": 6,
            "allow": False
        },
        {
            "product_id": 2,
            "features_id": 7,
        },
        {
            "product_id": 2,
            "features_id": 8,
        },
        {
            "product_id": 2,
            "features_id": 9,
        },
        {
            "product_id": 2,
            "features_id": 10,
        },
        {
            "product_id": 2,
            "features_id": 11,
        },
       
        
        
        # PRO monthly
        {
            "product_id": 3,
            "features_id": 1,
             
        },
        {
            "product_id": 3,
            "features_id": 2,
        },
        {
            "product_id": 3,
            "features_id": 3,
        },
        
        {
            "product_id": 3,
            "features_id": 4,
        },
        {
            "product_id": 3,
            "features_id": 5,
        },
         {
            "product_id": 3,
            "features_id": 6,
            "allow": False
        },
      
        {
            "product_id": 3,
            "features_id": 7,
        },
        {
            "product_id": 3,
            "features_id": 8,
        },
        {
            "product_id": 3,
            "features_id": 9,
        },
        {
            "product_id": 3,
            "features_id": 10,
        },
        {
            "product_id": 3,
            "features_id": 11,
        },
        
        # EXPERT monthly
        {
            "product_id": 4,
            "features_id": 1,
             
        },
        {
            "product_id": 4,
            "features_id": 2,
        },
        {
            "product_id": 4,
            "features_id": 3,
        },
        
        {
            "product_id": 4,
            "features_id": 4,
        },
        {
            "product_id": 4,
            "features_id": 5,
        },
        {
            "product_id": 4,
            "features_id": 6,
        },
      
        {
            "product_id": 4,
            "features_id": 7,
        },
        {
            "product_id": 4,
            "features_id": 8,
        },
        {
            "product_id": 4,
            "features_id": 9,
        },
        {
            "product_id": 4,
            "features_id": 10,
        },
        {
            "product_id": 4,
            "features_id": 11,
        },
    ]
    DEFAULT_YEARLY_PRODUCT_FEATURES: List[Any] = [
 
        # PREMIUM yearly
        {
            "product_id": 8,
            "features_id": 1,
             
        },
        {
            "product_id": 8,
            "features_id": 2,
        },
        {
            "product_id": 8,
            "features_id": 3,
        },
        {
            "product_id": 8,
            "features_id": 4,
        },
        {
            "product_id": 8,
            "features_id": 5,
            "allow": False
        },
        {
            "product_id": 8,
            "features_id": 6,
            "allow": False
        },
        {
            "product_id": 8,
            "features_id": 7,
        },
        {
            "product_id": 8,
            "features_id": 8,
        },
        {
            "product_id": 8,
            "features_id": 9,
        },
         {
            "product_id": 8,
            "features_id": 10,
        },
        {
            "product_id": 8,
            "features_id": 11,
        },
       
        
        
        # PRO yearly
        {
            "product_id": 9,
            "features_id": 1,
             
        },
        {
            "product_id": 9,
            "features_id": 2,
        },
        {
            "product_id": 9,
            "features_id": 3,
        },
        
        {
            "product_id": 9,
            "features_id": 4,
        },
      {
            "product_id": 9,
            "features_id": 5,
        },
        {
            "product_id": 9,
            "features_id": 6,
            "allow": False
        },
        {
            "product_id": 9,
            "features_id": 7,
        },
        {
            "product_id": 9,
            "features_id": 8,
        },
        {
            "product_id": 9,
            "features_id": 9,
        },
        {
            "product_id": 9,
            "features_id": 10,
        },
        {
            "product_id": 9,
            "features_id": 11,
        },
        
        # EXPERT yearly
        {
            "product_id": 10,
            "features_id": 1,
             
        },
        {
            "product_id": 10,
            "features_id": 2,
        },
        {
            "product_id": 10,
            "features_id": 3,
        },
        
        {
            "product_id": 10,
            "features_id": 4,
        },
      {
            "product_id": 10,
            "features_id": 5,
        },
        {
            "product_id": 10,
            "features_id": 6,
        },
        {
            "product_id": 10,
            "features_id": 7,
        },
        {
            "product_id": 10,
            "features_id": 8,
        },
        {
            "product_id": 10,
            "features_id": 9,
        },
        {
            "product_id": 10,
            "features_id": 10,
        },
        {
            "product_id": 10,
            "features_id": 11,
        },
 
    ]

    DEFAULT_NOTIFICATION_SETTING: List[Any] = [
        {
            "key": "email_receive_pvgis_actuality",
            "name": "Je souhaite recevoir par Email les actualités de PVGIS.COM",
            "type": "email"
        },
        {
            "key": "email_receive_pvgis_member_actuality",
            "name": "Je souhaite recevoir par Email l'actualité des membres de PVGIS.COM",
            "type": "email"
        },
        {
            "key": "email_online_learning_pvgis",
            "name": "Études en ligne PVGIS.COM",
            "type": "email"
        },
        {
            "key": "email_partner_offer_pvgis",
            "name": "Les offres sélectionnées par PVGIS.COM auprès de ses partenaires",
            "type": "email"
        },
        {
            "key": "messenger_receive_pvgis_actuality",
            "name": "Je souhaite recevoir par Messagerie l’actualité de PVGIS.COM",
            "type": "messenger"
        },
        {
            "key": "messenger_receive_pvgis_member_actuality",
            "name": "Je souhaite recevoir par Messagerie l’actualité des membres de PVGIS.COM",
            "type": "messenger"
        },
        {
            "key": "messenger_partner_offer_pvgis",
            "name": "Les offres sélectionnées par PVGIS.COM auprès de ses partenaires",
            "type": "messenger"
        },
        {
            "key": "data_share_partner",
            "name": "J’accèpte que PVGIS.COM partage mes données avec ses partenaires",
            "type": "data"
        }
    ]

    DEFAULT_CONTACT_PLATFORM: List[Any] = [
        {
            "key": "fb",
            "label": "facebook",
            "type": "social",
        },
        {
            "key": "linkedin",
            "label": "linkedin",
            "type": "social",
        },
        {
            "key": "x-tweet",
            "label": "x",
            "type": "social",
        },
        {
            "key": "instagram",
            "label": "instagram",
            "type": "social",
        },
        {
            "key": "pinterest",
            "label": "pinterest",
            "type": "social",
        },
        {
            "key": "whatsapp",
            "label": "whatsapp",
            "type": "messenger",
        },
        {
            "key": "wechat",
            "label": "wechat",
            "type": "messenger",
        },
        {
            "key": "messenger",
            "label": "messenger",
            "type": "messenger",
        },
        {
            "key": "telegram",
            "label": "telegram",
            "type": "messenger",
        },
        {
            "key": "discord",
            "label": "discord",
            "type": "messenger",
        },
        {
            "key": "signal",
            "label": "signal",
            "type": "messenger",
        },
        {
            "key": "viber",
            "label": "viber",
            "type": "messenger",
        },
        {
            "key": "line",
            "label": "line",
            "type": "messenger",
        },
        {
            "key": "website",
            "label": "website",
            "type": "network",
        },
    ]

    DEFAULT_PAYMENT_GATEWAY: List[Any] = [
        {"name": "STRIPE"},
    ]
    
     
@lru_cache()
def get_settings() -> Settings:
    return Settings()
