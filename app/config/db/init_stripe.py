import json
import csv
import traceback 
from app.enums.billing_period_interval_enum import BillingPeriodIntervalEnum
import stripe
from app.config.settings import get_settings
from app.config.db import base  # noqa: F401
from sqlalchemy.orm import Session

from app import crud, schemas
settings = get_settings()
stripe.api_key = settings.STRIPE_SECRETE_KEY
# make sure all SQL Alchemy models are imported (app.db.base) before initializing DB
# otherwise, SQL Alchemy might fail to initialize relationships properly
# for more details: https://github.com/tiangolo/full-stack-fastapi-postgresql/issues/28

products = stripe.Product.list(limit=100, active=True) # expand=['data.prices'] doesnt work
 
 

def product_exists(product_name, db_price, billing_period_interval):
    try:
        # Search for the product using Stripe's API
        
        for product in products['data']: 
            if product['name'].lower() == product_name.lower():
                prices = stripe.Price.list(product=product['id'] ,limit=100)
                for price in prices:  # Loop through all associated prices
                    if (
                        price['unit_amount'] == (db_price*100)
                        and price['recurring']
                        and price['recurring']['interval'] == billing_period_interval.value
                        and price['active']   
                    ):
                        return (product,price) 
                return (product, None)
        return (None, None)
    except Exception as e:
        traceback.print_exc()  # Print the traceback to the console
        print(f"Error while searching for product: {str(e)}")
        return (None, None)

def init_stripe(db: Session) -> None:
    products_db=crud.product.get_multi(db=db)
    for row in products_db:
        
        try:
            (existing_product, existing_price) = product_exists(row.name, row.monthly_price, row.billing_period_interval)
            # print('-------------------------------')
            # print(f"Product: {row.name} - {row.billing_period_interval.value}")
            # print(f"Existing product: {existing_product.id if existing_product else 'None'}")
            # print(f"Existing price: {existing_price.id if existing_price else 'None'}")
            if not existing_price and  row.billing_period_interval != BillingPeriodIntervalEnum.once:
               
                # Create the product in Stripe
                if not existing_product :
                    existing_product = stripe.Product.create(
                        name=row.name,
                    )
                    print(f"Created product {existing_product.id}")
                    
                billing_interval_val = (
                        row.billing_period_interval.value 
                        if (row.billing_period_interval )
                        else BillingPeriodIntervalEnum.month.value
                    )
               
                # Create the price for the product
                price = stripe.Price.create(
                    unit_amount=int(float(row.monthly_price) * 100),  # Stripe expects amounts in cents
                    currency="EUR",
                    product=existing_product.id,
                    recurring={
                        'interval': billing_interval_val
                    },
                )
                crud.product.update(db=db,db_obj=row,obj_in={"stripe_price_id":price.id})

                print(f"Created price for product '{row.name}' {existing_product.id} - {billing_interval_val} with price {price.id}")
            elif row.stripe_price_id == None:
                crud.product.update(db=db,db_obj=row,obj_in={"stripe_price_id":existing_price.id})
                print(f"Product '{row.name}' has already a pricing with ID: {row.stripe_price_id}, but no stripe_price_id in db. Updated.")
            else:
              
                print(f"Product '{row.name}' has already a pricing with ID: {row.stripe_price_id}")

        except Exception as e:
            traceback.print_exc()
            print(f"Failed to create product: {row.name}. Error: {str(e)}")
    
    
     

