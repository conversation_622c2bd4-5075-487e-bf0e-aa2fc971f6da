import json

from app.config.settings import get_settings
from app.config.db import base  # noqa: F401
from sqlalchemy.orm import Session
from fastapi.encoders import jsonable_encoder

from app import crud, schemas
from app.models.product import Product
from app.models.product_features import ProductFeatures
settings = get_settings()

# make sure all SQL Alchemy models are imported (app.db.base) before initializing DB
# otherwise, SQL Alchemy might fail to initialize relationships properly
# for more details: https://github.com/tiangolo/full-stack-fastapi-postgresql/issues/28


def init_db(db: Session) -> None:
    # Tables should be created with Alembic migrations
    # But if you don't want to use migrations, create
    # the tables un-commenting the next line
    # Base.metadata.create_all(bind=engine)
    account_types = crud.account_type.get_multi(db=db)
    if len(account_types) == 0:
        for account_type in settings.DEFAULT_ACCOUNT_TYPE:
            account_type_in = schemas.AccountTypeCreate(**account_type)
            crud.account_type.create(db=db, obj_in=account_type_in)
    
    professional_category = crud.professional_category.get_multi(db=db)
    if len(professional_category) == 0:
        for professional_cat in settings.DEFAULT_PROFESSIONAL_CATEGORY:
            professional_cat_in = schemas.ProfessionalCategoryCreate(**professional_cat)
            crud.professional_category.create(db=db, obj_in=professional_cat_in)
            
    school_category = crud.school_category.get_multi(db=db)
    if len(school_category) == 0:
        for school_cat in settings.DEFAULT_SCHOOL_CATEGORY:
            school_cat_in = schemas.SchoolCategoryCreate(**school_cat)
            crud.school_category.create(db=db, obj_in=school_cat_in)
    
    products = crud.product.get_multi(db=db)
    if len(products) == 0:
        for product in settings.DEFAULT_PRODUCT:
            product_in = schemas.ProductCreate(**product)
            crud.product.create(db=db, obj_in=product_in)
    
    yearlyProducts = db.query(Product).filter(
        Product.id.in_([8,9,10])
    ).all()
    if len(yearlyProducts) == 0:
        for product in settings.DEFAULT_YEARLY_PRODUCT:
            product_in = schemas.ProductCreate(**product)
            crud.product.create(db=db, obj_in=product_in)
    
            
    features = crud.features.get_multi(db=db)
    if len(features) == 0:
        for feature in settings.DEFAULT_FEATURES:
            feature_in = schemas.FeaturesCreate(**feature)
            crud.features.create(db=db, obj_in=feature_in)
            
    
            
    product_features = crud.product_features.get_multi(db=db)
    if len(product_features) == 0:
        for product_feature in settings.DEFAULT_PRODUCT_FEATURES:
            product_feature_in = schemas.ProductFeaturesCreate(**product_feature)
            crud.product_features.create(db=db, obj_in=product_feature_in)
            
    yearly_product_features = db.query(ProductFeatures).filter(
        ProductFeatures.product_id.in_([8,9,10])
    ).all()
    if len(yearly_product_features) == 0:
        for product_feature in settings.DEFAULT_YEARLY_PRODUCT_FEATURES:
            product_feature_in = schemas.ProductFeaturesCreate(**product_feature)
            crud.product_features.create(db=db, obj_in=product_feature_in)

    notification_setting = crud.notification_setting.get_multi(db=db)
    if len(notification_setting) == 0:
        for notif_setting in settings.DEFAULT_NOTIFICATION_SETTING:
            notif_setting_in = schemas.NotificationSettingCreate(**notif_setting)
            crud.notification_setting.create(db=db, obj_in=notif_setting_in)

    contact_platform = crud.contact_platform.get_multi(db=db)
    if len(contact_platform) == 0:
        for contact in settings.DEFAULT_CONTACT_PLATFORM:
            contact_in = schemas.ContactPlatformCreate(**contact)
            crud.contact_platform.create(db=db, obj_in=contact_in)
            
    payment_gateway = crud.payment_gateway.get_multi(db=db)
    if len(payment_gateway) == 0:
        for gateway in settings.DEFAULT_PAYMENT_GATEWAY:
            gateway_in = schemas.PaymentGatewayCreate(**gateway)
            crud.payment_gateway.create(db=db, obj_in=gateway_in)
    

