from datetime import datetime
from app.config.settings import get_settings
from sqlalchemy.orm import sessionmaker
from typing import Generator
from sqlalchemy import create_engine, Text, Column, Integer, DateTime, func, ForeignKey
from sqlalchemy.ext.declarative import as_declarative, declared_attr, DeclarativeMeta
import json

settings = get_settings()

engine = create_engine(settings.DATABASE_URI,
                       pool_pre_ping=True,
                       pool_recycle=3600,
                       pool_size=100,
                       max_overflow=10)

SessionLocal = sessionmaker(bind=engine, autocommit=False, autoflush=False)

@as_declarative()
class Base:
    id = Column(Integer, primary_key=True, autoincrement=True)

    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    deleted_at = Column(DateTime)
    last_user_to_interact = Column(Integer, index=True)
    import_id = Column(Text)
    
    __name__: str

    # Generate __tablename__ automatically
    @declared_attr
    def __tablename__(cls) -> str:
        return cls.__name__.lower()


def get_session() -> Generator:
    session = SessionLocal()
    try:
        yield session
    finally:
        session.close()

class AlchemyEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj.__class__, DeclarativeMeta):
            # Convert SQLAlchemy model to dictionary
            fields = {}
            for field in [
                x for x in dir(obj) if not x.startswith("_") and x != "metadata"
            ]:
                data = obj.__getattribute__(field)
                try:
                    json.dumps(data)
                    fields[field] = data
                except TypeError:
                    fields[field] = None
            return fields
        return json.JSONEncoder.default(self, obj)
