from sqlalchemy import event
from app import crud
from app.models.customer import Customer

class CustomerEventListener:
    pass
    # def on_after_insert(mapper, connection, target):
    #     print(target.id)
    #     crud.account_notification_setting.check_all_by_customer_id(db=db, customer_id=target.id)

    # def on_after_update(mapper, connection, target):
    #     print(target.id)
    #     crud.account_notification_setting.check_all_by_customer_id(db=db, customer_id=target.id)

# event.listen(Customer, "after_insert", CustomerEventListener.on_after_insert)
# event.listen(Customer, "after_update", CustomerEventListener.on_after_update)