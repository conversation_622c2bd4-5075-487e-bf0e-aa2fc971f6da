# TRACK USER ACTIVITY BY API CALL (UNUSED FOR NOW)

import logging
from datetime import datetime
from fastapi import Request
from app.api.api_v2.deps import get_optional_user
from app.config.db.mongo_database import user_activity_collection

logger = logging.getLogger(__name__)

async def track_user_activity(request: Request):
    try:
        url = request.url
        method = request.method
        header_authorization = request.headers.get("Authorization")

        if method == "GET" or not header_authorization:
            return

        try:
            try:
                body = await request.json()
            except:
                body = await request.body()
        except:
            body = None

        current_user = get_optional_user(None, header_authorization)
        if not current_user:
            return
            
        token = header_authorization.split(" ")[1]
        is_admin = ("isAdmin" in current_user) and (current_user["isAdmin"] == True)

        # Create a document for user activity
        user_activity_doc = {
            "user_id": current_user["id"],
            "connected_user_id": current_user["connectedUserId"],
            "user_is_admin": is_admin,
            "data": body,
            "action": str(url),
            "method": method,
            "last_user_to_interact": current_user["id"],
            "token": token,
            "current_user_details": current_user,
            "timestamp": datetime.now(),
        }

        # Insert the document into MongoDB collection
        user_activity_collection.insert_one(user_activity_doc)

    except Exception as ex:
        logger.error(" Error in user activity tracking but it's not blocking: %s", str(ex)) 