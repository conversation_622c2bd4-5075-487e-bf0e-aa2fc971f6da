import logging
from datetime import datetime
from typing import Optional, Dict, Any
from app.utils.utils import serialize_dict_to_json
from sqlalchemy import event
from sqlalchemy.orm import Session
from sqlalchemy.orm.attributes import get_history
from app.config.db.mongo_database import user_activity_collection
from app.config.db.database import SessionLocal
from app.config.db import base
import inspect
from sqlalchemy.orm.properties import ColumnProperty

logger = logging.getLogger(__name__)

# Thread-local storage for current user
class UserContext:
    def __init__(self):
        self._user = None
    
    def set_user(self, user: Dict[str, Any]):
        self._user = user
    
    def get_user(self) -> Optional[Dict[str, Any]]:
        return self._user

user_context = UserContext()


def get_user_info(current_user: Optional[Dict[str, Any]]) -> Dict[str, Any]:
    """Get user information for tracking"""
    if not current_user:
        return {
            "id": None,
            "connectedUserId": None,
            "isAdmin": False
        }
    return current_user


def get_changes(target: Any) -> Dict[str, Dict[str, Any]]:
    """Get changes (old and new values) from the target object"""
    changes = {}
    for attr in target.__mapper__.attrs:
        key = attr.key

        history = get_history(target, key, passive=True)
        if history.has_changes():
            old_value = history.deleted[0] if history.deleted else None
            new_value = history.added[0] if history.added else None
            changes[key] = {
                "old": old_value,
                "new": new_value
            }
    return changes


def get_record_data(target: Any) -> Dict[str, Any]:
    """Get the current state of the target object (excluding relationships)"""
    data = {}
    for attr in target.__mapper__.attrs:
        # Only include real columns (exclude relationships)
        if isinstance(attr, ColumnProperty):
            key = attr.key
            data[key] = getattr(target, key, None)
    return data


def create_tracking_doc(
    operation_type: str,
    table_name: str,
    target: Any,
    current_user: Optional[Dict[str, Any]]
) -> Dict[str, Any]:
    """Create tracking document with all necessary information"""
    user_info = get_user_info(current_user)
    record_data = serialize_dict_to_json(get_record_data(target))

    tracking_doc = {
        # Operation details
        "operation_type": operation_type,
        "table_name": table_name,
        "record_id": getattr(target, 'id', None),

        # User information
        "account_user_id": user_info["id"],
        "connected_user_id": user_info.get("connectedUserId", user_info["id"]),
        "user_is_admin": user_info.get("isAdmin", False),
        "current_user_details": user_info,

        # Record data
        "record_data": str(record_data),
        "changes": None,

        # Timestamp
        "timestamp": datetime.now(),
    }

    if operation_type == "UPDATE":
        change_diff = serialize_dict_to_json(get_changes(target))
        tracking_doc["changes"] = str(change_diff)

    return tracking_doc


def track_changes(mapper, connection, target, operation_type: str):
    """SQLAlchemy event listener for tracking changes"""
    try:
        current_user = user_context.get_user()
        table_name = target.__tablename__
        
        # Update last_user_to_interact in the actual database record
        if current_user and hasattr(target, 'last_user_to_interact'):
            connected_user_id = current_user.get("connectedUserId", current_user["id"])
            target.last_user_to_interact = connected_user_id
        
        tracking_doc = create_tracking_doc(
            operation_type=operation_type,
            table_name=table_name,
            target=target,
            current_user=current_user
        )
        
        user_activity_collection.insert_one(tracking_doc)

    except Exception as ex:
        logger.error("Error in database change tracking: %s", str(ex))


def setup_tracking():
    """Setup tracking for all models"""
    tracking_events = {
        'INSERT': 'after_insert',
        'UPDATE': 'after_update',
        'DELETE': 'after_delete'
    }
    try:
        # Get all models from base.py
        for name, obj in inspect.getmembers(base):
            # Check if it's a SQLAlchemy model (class that inherits from Base)
            if inspect.isclass(obj) and hasattr(obj, '__tablename__'):
                try:
                    # Register event listeners for each model with operation type
                    for operation, event_name in tracking_events.items():
                        event.listen(
                            obj,
                            event_name, 
                            lambda mapper, connection, target, op=operation: track_changes(mapper, connection, target, op)
                        )
                    # logger.info(f"Registered tracking for model: {name}")
                except Exception as e:
                    logger.error(f"Error registering events for model {name}: {str(e)}")
    except Exception as e:
        logger.error(f"Error in setup_tracking: {str(e)}")


def set_current_user(user: Dict[str, Any]):
    """Set the current user in the context"""
    user_context.set_user(user)
