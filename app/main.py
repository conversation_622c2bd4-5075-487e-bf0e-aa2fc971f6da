import logging
import os
from datetime import datetime
from app.api.api_v2.deps import get_optional_user
from dotenv import find_dotenv, load_dotenv
from fastapi import FastAPI, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from app.api.api_v2.api import api_router
from app import models, events # DO NOT REMOVE THIS LINE
from app.utils.custom_json_response import CustomJsonResponse
import stripe 
from app.config.settings import get_settings
from app.middleware.db_tracking import setup_tracking, set_current_user

settings = get_settings()
 
logger = logging.getLogger(__name__)
load_dotenv(find_dotenv())
ALLOWED_URL = os.environ.get("ALLOWED_URL", "").split(",")
logger.info(msg="Creating application") 
app = FastAPI(
    docs_url=settings.DOCS_URL,       # URL for Swagger UI
    redoc_url=settings.REDOC_URL,
    default_response_class=CustomJsonResponse)

app.add_middleware(
    CORSMiddleware,
    allow_origins=ALLOWED_URL,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Setup database tracking
setup_tracking()

# Middleware to set current user for tracking
@app.middleware("http")
async def set_user_context(request: Request, call_next):
    header_authorization = request.headers.get("Authorization")
    if header_authorization:
        current_user = get_optional_user(None, header_authorization)
        if current_user:
            set_current_user(current_user)
    response = await call_next(request)
    return response

stripe.api_key = os.getenv('STRIPE_SECRETE_KEY')
app.include_router(api_router, prefix="")

# if __name__ == "__main__":
#     uvicorn.run(app, host="0.0.0.0", port=3000, reload=True)
