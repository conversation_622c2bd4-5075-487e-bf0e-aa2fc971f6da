  
DROP VIEW IF EXISTS subscriptions_view;
CREATE VIEW subscriptions_view AS
  SELECT 
      s.*,
      c.id org_customer_id,
      c.first_name,
      c.last_name,
      COALESCE(co.name, c.country) country, 
      co.code_alpha_2 as country_iso_2
    FROM 
      pvgis_api.subscription s 
      LEFT JOIN pvgis_api.customer c ON c.id = s.customer_id 
      LEFT JOIN pvgis_api.country co on co.id = c.country_id or c.country = co.name
    WHERE 
      (
        (s.subscription_status = 'ACTIVE' 
        AND s.start_date <= NOW() 
        AND c.deleted_at IS NULL)
        or s.subscription_status = 'INACTIVE' 
      );