
DROP VIEW IF EXISTS last_24h_subscriptions_view;
CREATE VIEW last_24h_subscriptions_view AS
SELECT 
      s.*,
      c.country as country
    FROM 
      pvgis_api.subscription s 
      LEFT JOIN pvgis_api.customer c ON c.id = s.customer_id 
    WHERE 
      (
        (
          (
            s.subscription_status = 'ACTIVE' 
            AND s.start_date <= NOW() 
            AND c.deleted_at IS NULL
          )
          OR (
            s.subscription_status = 'INACTIVE'
          )
        ) 
        AND s.created_at BETWEEN (NOW() - INTERVAL 24 HOUR) 
        AND NOW()
      );