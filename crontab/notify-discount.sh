#!/bin/bash

LOG_FILE="/var/log/crontab/pvgis-notify-discount-cron.log"

get_timestamp() {
    date +"[%Y-%m-%d %H:%M:%S]"
}

echo "$(get_timestamp) - STARTING CRON: notify_discount" >> "$LOG_FILE"
response_code=$(curl -X POST -H "Content-Type: application/json" -d '{}' -o /dev/null -w "%{http_code}"  "https://api.pvgis.com/subscriptions/cron_job/notify_discount_customer?x_app_key=dWxs!LCJw!c2V1ZG8iOiJ0aXRpIiwiaWF0Ij%2BoxNzIz%2BTEwMTQ")
echo "$(get_timestamp) - Response code : $response_code" >> "$LOG_FILE"

# 5 0 * * * /home/<USER>/pvgis.com/notify-discount.sh
