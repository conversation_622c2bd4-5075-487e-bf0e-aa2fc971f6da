#!/bin/bash

LOG_FILE="/var/log/crontab/integrate-cities.log"

get_timestamp() {
    date +"[%Y-%m-%d %H:%M:%S]"
}

echo "$(get_timestamp) - STARTING CRON: integrate-cities" >> "$LOG_FILE"
response_code=$(curl -X POST -H "Content-Type: application/json" -d '{ "chunk_size": 200 }' -o /dev/null -w "%{http_code}"  "https://api.pvgis.com/city/migrate-cities?x_app_key=dWxs!LCJw!c2V1ZG8iOiJ0aXRpIiwiaWF0Ij%2BoxNzIz%2BTEwMTQ")
echo "$(get_timestamp) - Response code : $response_code" >> "$LOG_FILE"

# 0 * * * * /home/<USER>/pvgis.com/integrate-cities.sh