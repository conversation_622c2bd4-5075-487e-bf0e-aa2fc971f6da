#!/bin/bash

LOG_FILE="/var/log/crontab/pvgis-send-status.log"

get_timestamp() {
    date +"[%Y-%m-%d %H:%M:%S]"
}

echo "$(get_timestamp) - STARTING CRON: send-status-pvgis" >> "$LOG_FILE"
response_code=$(curl -X GET -H "Content-Type: application/json" -d '{}' -o /dev/null -w "%{http_code}"  "https://api.pvgis.com/customers/cron_job/send_status_pvgis?email_to=<EMAIL>,<EMAIL>,<EMAIL>&x_app_key=dWxs!LCJw!c2V1ZG8iOiJ0aXRpIiwiaWF0Ij%2BoxNzIz%2BTEwMTQ")
echo "$(get_timestamp) - Response code : $response_code" >> "$LOG_FILE"

# 5 0 * * * /home/<USER>/pvgis.com/send-status-pvgis.sh
