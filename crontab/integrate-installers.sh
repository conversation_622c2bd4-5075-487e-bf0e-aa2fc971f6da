#!/bin/bash

LOG_FILE="/var/log/crontab/integrate-installers.log"

get_timestamp() {
    date +"[%Y-%m-%d %H:%M:%S]"
}

echo "$(get_timestamp) - STARTING CRON: integrate-installers" >> "$LOG_FILE"
response_code=$(curl -X POST -H "Content-Type: application/json" -d '{ "chunk_size": 200 }' -o /dev/null -w "%{http_code}"  "https://api.pvgis.com/bulk_upload/customer/installer/batch?chunk_size=200&x_app_key=dWxs!LCJw!c2V1ZG8iOiJ0aXRpIiwiaWF0Ij%2BoxNzIz%2BTEwMTQ")
echo "$(get_timestamp) - Response code : $response_code" >> "$LOG_FILE"

# 0 * * * * /home/<USER>/pvgis.com/integrate-installers.sh