#!/bin/bash

LOG_FILE="/var/log/crontab/populate-missing-subscription-id-spt.log"

get_timestamp() {
    date +"[%Y-%m-%d %H:%M:%S]"
}

echo "$(get_timestamp) - STARTING CRON: populate-missing-subscription-id-spt" >> "$LOG_FILE"
response_code=$(curl -X GET -H "Content-Type: application/json" -d '{}' -o /dev/null -w "%{http_code}"  "https://api.pvgis.com/subscription_payment_transactions/cron/populate_missing_subscription_id?x_app_key=dWxs!LCJw!c2V1ZG8iOiJ0aXRpIiwiaWF0Ij%2BoxNzIz%2BTEwMTQ&limit=100")
echo "$(get_timestamp) - Response code : $response_code" >> "$LOG_FILE"

# 0 * * * * /home/<USER>/pvgis.com/populate-missing-subscription-id-spt.sh
