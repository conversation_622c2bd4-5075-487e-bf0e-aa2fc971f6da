#!/bin/bash

LOG_FILE="/var/log/crontab/send-marketing-customer-email-pvgis.log"

get_timestamp() {
    date +"[%Y-%m-%d %H:%M:%S]"
}

echo "$(get_timestamp) - STARTING CRON: send-marketing-customer-email-pvgis" >> "$LOG_FILE"
response_code=$(curl -X GET -H "Content-Type: application/json" -d '{}' -o /dev/null -w "%{http_code}"  "https://api.pvgis.com/customer-emails/send-emails?x_app_key=dWxs!LCJw!c2V1ZG8iOiJ0aXRpIiwiaWF0Ij%2BoxNzIz%2BTEwMTQ&campaign_key=marketing")
echo "$(get_timestamp) - Response code : $response_code" >> "$LOG_FILE"

# 0 * * * * /home/<USER>/pvgis.com/send-marketing-customer-email-pvgis.sh
