#!/bin/bash
# campaign_id = 8 => Pro Mix
# account_type = 1 =>  Account Type Pro

LOG_FILE="brevo-cron.log"

get_timestamp() {
    date +"[%Y-%m-%d %H:%M:%S]"
}

echo "$(get_timestamp) - STARTING CRON: brevo_import" >> "$LOG_FILE"

response_code=$(curl -s -o /dev/null -w "%{http_code}" -X POST \
  -H "accept: application/json" \
  -d '' \
  "https://api.pvgis.com/brevo/import-data-user-email-to-send-mail?x_app_key=dWxs%21LCJw%21c2V1ZG8iOiJ0aXRpIiwiaWF0Ij%2BoxNzIz%2BTEwMTQ&campaign_id=8&account_type=1")

echo "$(get_timestamp) - Response code : $response_code" >> "$LOG_FILE"

# crontab -e
# * * * * * /home/<USER>/work/pvgis-api-client/crontab/brevo_cron.sh
# chmod +x /home/<USER>/work/pvgis-api-client/crontab/brevo_cron.sh