#!/bin/bash

LOG_FILE="/var/log/crontab/send-report-customer-email-pvgis.log"

get_timestamp() {
    date +"[%Y-%m-%d %H:%M:%S]"
}

echo "$(get_timestamp) - STARTING CRON: send-report-customer-email-pvgis" >> "$LOG_FILE"
response_code=$(curl -X POST -H "Content-Type: application/json" -d '{}' -o /dev/null -w "%{http_code}"  "https://api.pvgis.com/customer-emails/send-report-email?x_app_key=dWxs!LCJw!c2V1ZG8iOiJ0aXRpIiwiaWF0Ij%2BoxNzIz%2BTEwMTQ")
echo "$(get_timestamp) - Response code : $response_code" >> "$LOG_FILE"

# 0 0 * * * /home/<USER>/pvgis.com/send-report-customer-email-pvgis.sh
