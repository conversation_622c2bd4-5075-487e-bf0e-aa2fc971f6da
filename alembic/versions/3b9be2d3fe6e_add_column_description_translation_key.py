"""add column description_translation_key

Revision ID: 3b9be2d3fe6e
Revises: 41832041b69b
Create Date: 2024-08-07 09:35:45.886455

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '3b9be2d3fe6e'
down_revision: Union[str, None] = '41832041b69b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('product', sa.Column('description_translation_key', sa.String(length=255), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('product', 'description_translation_key')
    # ### end Alembic commands ###
