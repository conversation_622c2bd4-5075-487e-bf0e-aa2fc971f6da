"""update_features

Revision ID: 7b6e01d9a02c
Revises: f3430ccd0522
Create Date: 2025-02-28 08:25:35.423687

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa 

from app import crud
from app.models.features import Features
from app.models.product import Product
from app.models.product_features import ProductFeatures

# revision identifiers, used by Alembic.
revision: str = '7b6e01d9a02c'
down_revision: Union[str, None] = 'f3430ccd0522'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None
 


def upgrade() -> None:
    
    op.execute('DELETE FROM product_features')
    op.execute('DELETE FROM features')
    op.execute('UPDATE product set monthly_price = 9 where id = 2')
    op.execute('UPDATE product set monthly_price = 19 where id = 3')
    op.execute('UPDATE product set monthly_price = 29 where id = 4')

def downgrade() -> None:
    pass
