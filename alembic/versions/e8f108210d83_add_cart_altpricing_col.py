"""add cart altpricing col

Revision ID: e8f108210d83
Revises: 88b41716344e
Create Date: 2025-02-17 11:28:37.480004

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'e8f108210d83'
down_revision: Union[str, None] = '88b41716344e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('cart', sa.Column('product_alt_pricing_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'cart', 'product_alt_pricing', ['product_alt_pricing_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'cart', type_='foreignkey')
    op.drop_column('cart', 'product_alt_pricing_id')
    # ### end Alembic commands ###
