"""update subscription table

Revision ID: 464545c6fde9
Revises: af8d313f10b8
Create Date: 2024-08-01 10:01:10.420584

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '464545c6fde9'
down_revision: Union[str, None] = 'af8d313f10b8'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('subscription', sa.Column('subscription_status', sa.Enum('ACTIVE', 'PENDING', 'INACTIVE', name='subscriptionstatusenum'), nullable=True))
    op.add_column('subscription', sa.Column('customer_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'subscription', 'customer', ['customer_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'subscription', type_='foreignkey')
    op.drop_column('subscription', 'customer_id')
    op.drop_column('subscription', 'subscription_status')
    # ### end Alembic commands ###
