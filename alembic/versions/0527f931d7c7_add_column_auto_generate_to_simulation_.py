"""Add column auto generate to simulation table

Revision ID: 0527f931d7c7
Revises: a7295d0ea578
Create Date: 2024-07-31 08:18:14.738290

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0527f931d7c7'
down_revision: Union[str, None] = 'a7295d0ea578'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('simulation', sa.Column('profile', sa.Enum('RESIDENTIAL', 'COMMERCIAL', name='profileenum'), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('simulation', 'profile')
    # ### end Alembic commands ###
