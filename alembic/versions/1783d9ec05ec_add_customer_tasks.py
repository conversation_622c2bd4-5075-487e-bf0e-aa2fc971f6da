"""add_customer_tasks

Revision ID: 1783d9ec05ec
Revises: 8305b0c7da16
Create Date: 2025-01-16 05:26:28.282803

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '1783d9ec05ec'
down_revision: Union[str, None] = '8305b0c7da16'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Insert rows into CustomerEmailTask table
    tasks = [
        {
            "key": "welcome_and_discovery",
            "name": "Welcome and discovery",
            "email_number": 1,
            "cms_key": "pvgis-welcome-discovery",
            "is_active": True,
        },
        {
            "key": "user_testimonial",
            "name": "User testimonial",
            "email_number": 2,
            "cms_key": "pvgis-customer-experience",
            "is_active": True,
        },
        {
            "key": "highlighting_features",
            "name": "Highlighting features",
            "email_number": 3,
            "cms_key": "pvgis-advantages",
            "is_active": True,
        },
        {
            "key": "special_offer",
            "name": "Special offer",
            "email_number": 4,
            "cms_key": "pvgis-special-offer",
            "is_active": True,
        },
        {
            "key": "last_chance",
            "name": "Last chance",
            "email_number": 5,
            "cms_key": "pvgis-subscription-last-chance",
            "is_active": True,
        },
    ]

    # Use op.bulk_insert for batch insertion
    op.bulk_insert(
        sa.table(
            "customer_email_task",
            sa.column("key", sa.String),
            sa.column("name", sa.String),
            sa.column("email_number", sa.Integer),
            sa.column("cms_key", sa.String),
            sa.column("is_active", sa.Boolean),
        ),
        tasks,
    )
    pass


def downgrade() -> None:
    op.execute(
        """
        DELETE FROM CustomerEmailTask
        WHERE key IN (
            'welcome_and_discovery',
            'user_testimonial',
            'highlighting_features',
            'special_offer',
            'last_chance'
        )
        """
    )
