"""Add customer_email tables

Revision ID: 8305b0c7da16
Revises: 0bb563880457
Create Date: 2025-01-16 05:10:15.658219

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '8305b0c7da16'
down_revision: Union[str, None] = '0bb563880457'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('customer_email_task',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.Column('last_user_to_interact', sa.Integer(), nullable=True),
    sa.Column('import_id', sa.Text(), nullable=True),
    sa.Column('key', sa.String(length=255), nullable=True),
    sa.Column('name', sa.String(length=255), nullable=True),
    sa.Column('email_number', sa.Integer(), nullable=True),
    sa.Column('cms_key', sa.String(length=255), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_customer_email_task_last_user_to_interact'), 'customer_email_task', ['last_user_to_interact'], unique=False)
    op.create_table('customer_email_item',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.Column('last_user_to_interact', sa.Integer(), nullable=True),
    sa.Column('import_id', sa.Text(), nullable=True),
    sa.Column('sent_at', sa.DateTime(), nullable=True),
    sa.Column('language', sa.String(length=5), nullable=True),
    sa.Column('customer_id', sa.Integer(), nullable=True),
    sa.Column('country_id', sa.Integer(), nullable=True),
    sa.Column('customer_email_task_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['country_id'], ['country.id'], ),
    sa.ForeignKeyConstraint(['customer_email_task_id'], ['customer_email_task.id'], ),
    sa.ForeignKeyConstraint(['customer_id'], ['customer.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_customer_email_item_last_user_to_interact'), 'customer_email_item', ['last_user_to_interact'], unique=False)
     
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_customer_email_item_last_user_to_interact'), table_name='customer_email_item')
    op.drop_table('customer_email_item')
    op.drop_index(op.f('ix_customer_email_task_last_user_to_interact'), table_name='customer_email_task')
    op.drop_table('customer_email_task')
    # ### end Alembic commands ###
