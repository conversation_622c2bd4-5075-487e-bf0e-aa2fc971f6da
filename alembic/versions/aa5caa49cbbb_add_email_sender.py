"""add email sender

Revision ID: aa5caa49cbbb
Revises: 7c5080056133
Create Date: 2025-03-05 05:03:24.481705

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql
from sqlalchemy.orm import sessionmaker

from app.models.customer_email_sender import CustomerEmailSender
from app.services.country_migration_service import integrate_senders


# revision identifiers, used by Alembic.
revision: str = 'aa5caa49cbbb'
down_revision: Union[str, None] = '7c5080056133'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None
existing_senders = [
        {"email": "<EMAIL>", "name": "<PERSON>"} , 
        {"email": "<EMAIL>", "name": "<PERSON>"} , 
        {"email": "<EMAIL>", "name": "<PERSON>"} , 
        {"email": "<EMAIL>", "name": "<PERSON><PERSON>"} , 
        {"email": "<EMAIL>", "name": "<PERSON><PERSON>"} , 
        {"email": "<EMAIL>", "name": "Hinata"} , 
        {"email": "<EMAIL>", "name": "Ishika"} , 
]

def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('customer_email_sender',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.Column('last_user_to_interact', sa.Integer(), nullable=True),
    sa.Column('import_id', sa.Text(), nullable=True),
    sa.Column('email', sa.String(length=255), nullable=True),
    sa.Column('name', sa.String(length=255), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_customer_email_sender_last_user_to_interact'), 'customer_email_sender', ['last_user_to_interact'], unique=False)
    op.create_table('sender_email_task_country',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.Column('last_user_to_interact', sa.Integer(), nullable=True),
    sa.Column('import_id', sa.Text(), nullable=True),
    sa.Column('customer_email_task_id', sa.Integer(), nullable=True),
    sa.Column('customer_email_sender_id', sa.Integer(), nullable=True),
    sa.Column('country_id', sa.Integer(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_sender_email_task_country_last_user_to_interact'), 'sender_email_task_country', ['last_user_to_interact'], unique=False)
    
    op.create_foreign_key(None, 'sender_email_task_country', 'customer_email_sender', ['customer_email_sender_id'], ['id'])
    op.create_foreign_key(None, 'sender_email_task_country', 'country', ['country_id'], ['id'])
    op.create_foreign_key(None, 'sender_email_task_country', 'customer_email_task', ['customer_email_task_id'], ['id'])
    op.create_unique_constraint('uq_sender_et_c', 'sender_email_task_country', ['customer_email_task_id', 'customer_email_sender_id', 'country_id'])
    op.create_unique_constraint(None, 'customer_email_sender', ['email'])
    
    # session = None 
    # try: 
    #     bind = op.get_bind()
    #     session = sessionmaker(bind=bind)() 
    #     session.add_all([CustomerEmailSender(**es) for es in existing_senders])
    #     integrate_senders(session, ['installer_invitation'])
    #     session.commit()
    # except Exception as e:
    #     if session:
    #         session.rollback()
    #     raise e
    # finally:
    #     if session:
    #         session.close()
    
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_country_normalized_name'), table_name='country')
    op.alter_column('country', 'normalized_name',
               existing_type=sa.String(length=255),
               type_=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=500),
               existing_nullable=True)
    op.drop_index(op.f('ix_city_normalized_name'), table_name='city')
    op.alter_column('city', 'normalized_name',
               existing_type=sa.String(length=255),
               type_=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=500),
               existing_nullable=True)
    op.drop_index(op.f('ix_sender_email_task_country_last_user_to_interact'), table_name='sender_email_task_country')
    op.drop_table('sender_email_task_country')
    op.drop_index(op.f('ix_customer_email_sender_last_user_to_interact'), table_name='customer_email_sender')
    op.drop_table('customer_email_sender')
    # ### end Alembic commands ###
