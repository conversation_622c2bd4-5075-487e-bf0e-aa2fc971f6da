"""Remove energy_purpose column

Revision ID: 73684de2d6e7
Revises: 8389b0dce379
Create Date: 2024-09-11 12:06:37.290413

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '73684de2d6e7'
down_revision: Union[str, None] = '8389b0dce379'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint(None, 'customer', ['pseudo'])
    op.drop_column('simulation_item', 'energy_purpose')
    op.alter_column('subscription_payment_transaction', 'amount_paid',
               existing_type=mysql.DOUBLE(asdecimal=True),
               type_=sa.Float(precision=53),
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('subscription_payment_transaction', 'amount_paid',
               existing_type=sa.Float(precision=53),
               type_=mysql.DOUBLE(asdecimal=True),
               existing_nullable=True)
    op.add_column('simulation_item', sa.Column('energy_purpose', mysql.VARCHAR(length=255), nullable=True))
    op.drop_constraint(None, 'customer', type_='unique')
    # ### end Alembic commands ###
