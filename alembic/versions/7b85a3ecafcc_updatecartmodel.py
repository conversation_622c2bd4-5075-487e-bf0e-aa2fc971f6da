"""updateCartModel

Revision ID: 7b85a3ecafcc
Revises: 48ff38f7ae8d
Create Date: 2024-08-01 07:12:19.214279

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '7b85a3ecafcc'
down_revision: Union[str, None] = '48ff38f7ae8d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('cart', sa.Column('product_id', sa.Integer(), nullable=True))
    op.add_column('cart', sa.Column('quantity', sa.Integer(), nullable=True))
    op.add_column('cart', sa.Column('converted_at', sa.DateTime(), nullable=True))
    op.add_column('cart', sa.Column('product_json', sa.J<PERSON>(), nullable=True))
    op.create_foreign_key(None, 'cart', 'product', ['product_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'cart', type_='foreignkey')
    op.drop_column('cart', 'product_json')
    op.drop_column('cart', 'converted_at')
    op.drop_column('cart', 'quantity')
    op.drop_column('cart', 'product_id')
    # ### end Alembic commands ###
