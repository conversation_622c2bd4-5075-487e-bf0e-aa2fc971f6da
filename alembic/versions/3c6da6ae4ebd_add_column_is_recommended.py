"""Add column is_recommended

Revision ID: 3c6da6ae4ebd
Revises: b4311c9f9a4e
Create Date: 2024-08-06 12:47:14.060031

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '3c6da6ae4ebd'
down_revision: Union[str, None] = 'b4311c9f9a4e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('product', sa.Column('is_recommended', sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('product', 'is_recommended')
    # ### end Alembic commands ###
