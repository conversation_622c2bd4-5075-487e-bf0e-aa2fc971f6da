"""AddColumnStripeCustomerId

Revision ID: fa72f360b08b
Revises: b8dfe2e79f1a
Create Date: 2024-08-23 04:53:10.918049

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'fa72f360b08b'
down_revision: Union[str, None] = 'b8dfe2e79f1a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('customer', sa.Column('stripe_customer_id', sa.String(length=255), nullable=True))
    op.create_index(op.f('ix_customer_stripe_customer_id'), 'customer', ['stripe_customer_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_customer_stripe_customer_id'), table_name='customer')
    op.drop_column('customer', 'stripe_customer_id')
    # ### end Alembic commands ###
