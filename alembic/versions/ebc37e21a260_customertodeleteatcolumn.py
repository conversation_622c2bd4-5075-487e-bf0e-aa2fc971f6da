"""CustomerToDeleteAtColumn

Revision ID: ebc37e21a260
Revises: 73684de2d6e7
Create Date: 2024-09-13 07:19:31.428638

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'ebc37e21a260'
down_revision: Union[str, None] = '73684de2d6e7'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('customer', sa.Column('to_delete_at', sa.DateTime(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('customer', 'to_delete_at')
    # ### end Alembic commands ###