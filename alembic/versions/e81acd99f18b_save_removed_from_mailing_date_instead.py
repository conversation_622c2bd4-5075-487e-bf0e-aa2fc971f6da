"""save_removed_from_mailing_date_instead

Revision ID: e81acd99f18b
Revises: 99771bff9bec
Create Date: 2025-02-25 06:54:18.460878

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'e81acd99f18b'
down_revision: Union[str, None] = '99771bff9bec'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    
    op.add_column('customer', sa.Column('removed_from_mailing_list_at', sa.DateTime(), nullable=True))
    op.execute(sa.text("""
                       UPDATE customer set removed_from_mailing_list_at=NOW() WHERE remove_from_mailing_list = TRUE
                       """))
    op.drop_column('customer', 'remove_from_mailing_list')
    
    


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
  
    op.add_column('customer', sa.Column('remove_from_mailing_list', mysql.TINYINT(display_width=1), autoincrement=False, nullable=True))
    op.drop_column('customer', 'removed_from_mailing_list_at')
   
    # ### end Alembic commands ###
