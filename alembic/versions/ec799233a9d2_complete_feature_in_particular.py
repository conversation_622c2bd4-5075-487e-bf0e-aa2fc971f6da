"""complete feature in particular

Revision ID: ec799233a9d2
Revises: d4268a56d9e2
Create Date: 2025-05-30 05:47:06.711486

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ec799233a9d2'
down_revision: Union[str, None] = 'd4268a56d9e2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    product_features = sa.table(
        'product_features',
        sa.column('product_id', sa.Integer()),
        sa.column('features_id', sa.Integer()),
        sa.column('allow', sa.Integer())
    )

    op.bulk_insert(
        product_features,
        [
            {'product_id': 11, 'features_id': 1, 'allow': 1},
            {'product_id': 12, 'features_id': 1, 'allow': 1},
            {'product_id': 13, 'features_id': 1, 'allow': 1},
            {'product_id': 20, 'features_id': 1, 'allow': 1},
            {'product_id': 21, 'features_id': 1, 'allow': 1},
            {'product_id': 22, 'features_id': 1, 'allow': 1},
            {'product_id': 11, 'features_id': 13, 'allow': 0},
            {'product_id': 12, 'features_id': 13, 'allow': 0},
            {'product_id': 13, 'features_id': 13, 'allow': 0},
            {'product_id': 20, 'features_id': 13, 'allow': 0},
            {'product_id': 21, 'features_id': 13, 'allow': 0},
            {'product_id': 22, 'features_id': 13, 'allow': 0},
            {'product_id': 11, 'features_id': 9, 'allow': 0},
            {'product_id': 12, 'features_id': 9, 'allow': 0},
            {'product_id': 13, 'features_id': 9, 'allow': 0},
            {'product_id': 20, 'features_id': 9, 'allow': 0},
            {'product_id': 21, 'features_id': 9, 'allow': 0},
            {'product_id': 22, 'features_id': 9, 'allow': 0},
            {'product_id': 11, 'features_id': 10, 'allow': 0},
            {'product_id': 12, 'features_id': 10, 'allow': 0},
            {'product_id': 13, 'features_id': 10, 'allow': 0},
            {'product_id': 20, 'features_id': 10, 'allow': 0},
            {'product_id': 21, 'features_id': 10, 'allow': 0},
            {'product_id': 22, 'features_id': 10, 'allow': 0},
        ]
    )


def downgrade() -> None:
    conn = op.get_bind()
    conn.execute(sa.text("""
        DELETE FROM product_features
        WHERE (product_id, features_id, allow) IN (
            (11, 1, 0), (12, 1, 0), (13, 1, 0), (20, 1, 0), (21, 1, 0), (22, 1, 0),
            (11, 13, 0), (12, 13, 0), (13, 13, 0), (20, 13, 0), (21, 13, 0), (22, 13, 0),
            (11, 9, 0), (12, 9, 0), (13, 9, 0), (20, 9, 0), (21, 9, 0), (22, 9, 0),
            (11, 10, 0), (12, 10, 0), (13, 10, 0), (20, 10, 0), (21, 10, 0), (22, 10, 0)
        )
    """))

