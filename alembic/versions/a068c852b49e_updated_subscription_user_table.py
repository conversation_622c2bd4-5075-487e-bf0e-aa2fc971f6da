"""updated subscription user table

Revision ID: a068c852b49e
Revises: 464545c6fde9
Create Date: 2024-08-02 06:05:48.821426

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'a068c852b49e'
down_revision: Union[str, None] = '464545c6fde9'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('subscription_user', sa.Column('first_name', sa.String(length=255), nullable=True))
    op.add_column('subscription_user', sa.Column('last_name', sa.String(length=255), nullable=True))
    op.add_column('subscription_user', sa.Column('is_active', sa.<PERSON>(), nullable=True))
    op.add_column('subscription_user', sa.Column('auth_user_id', sa.Integer(), nullable=True))
    op.add_column('subscription_user', sa.Column('email', sa.String(length=255), nullable=True))
    op.add_column('subscription_user', sa.Column('pseudo', sa.String(length=255), nullable=True))
    op.create_index(op.f('ix_subscription_user_auth_user_id'), 'subscription_user', ['auth_user_id'], unique=False)
    op.drop_constraint('subscription_user_ibfk_2', 'subscription_user', type_='foreignkey')
    op.drop_column('subscription_user', 'customer_id')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('subscription_user', sa.Column('customer_id', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True))
    op.create_foreign_key('subscription_user_ibfk_2', 'subscription_user', 'customer', ['customer_id'], ['id'])
    op.drop_index(op.f('ix_subscription_user_auth_user_id'), table_name='subscription_user')
    op.drop_column('subscription_user', 'pseudo')
    op.drop_column('subscription_user', 'email')
    op.drop_column('subscription_user', 'auth_user_id')
    op.drop_column('subscription_user', 'is_active')
    op.drop_column('subscription_user', 'last_name')
    op.drop_column('subscription_user', 'first_name')
    # ### end Alembic commands ###
