"""Add column timezone_name to Country model

Revision ID: 907b1a24f774
Revises: 60ac155b3c55
Create Date: 2025-08-21 07:27:41.284407

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '907b1a24f774'
down_revision: Union[str, None] = '60ac155b3c55'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('country', sa.Column('timezone_name', sa.String(length=50), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('country', 'timezone_name')
    # ### end Alembic commands ###
