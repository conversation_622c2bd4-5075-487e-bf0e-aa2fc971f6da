"""add_column_simulation_listing_input

Revision ID: 4f212f833206
Revises: a947fb5af95b
Create Date: 2024-12-13 07:13:36.922793

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '4f212f833206'
down_revision: Union[str, None] = '1a5d874a6a9a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('simulation_listing', sa.Column('simulation_input', sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('simulation_listing', 'simulation_input')
    # ### end Alembic commands ###
