"""remove product 17

Revision ID: 727cc33b9257
Revises: db3b36f7fdca
Create Date: 2025-05-26 07:22:35.961920

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '727cc33b9257'
down_revision: Union[str, None] = 'db3b36f7fdca'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    product_features = sa.table(
        'product_features',
        sa.column('product_id', sa.Integer()),
        sa.column('features_id', sa.Integer()),
        sa.column('allow', sa.Integer())
    )
    op.bulk_insert(
        product_features,
        [
            {'product_id': 20, 'features_id': 2, 'allow': 1},
            {'product_id': 20, 'features_id': 3, 'allow': 1},
            {'product_id': 20, 'features_id': 4, 'allow': 1},
            {'product_id': 20, 'features_id': 5, 'allow': 0},
            {'product_id': 20, 'features_id': 6, 'allow': 0},
            {'product_id': 20, 'features_id': 7, 'allow': 1},
            {'product_id': 20, 'features_id': 11, 'allow': 0},
            {'product_id': 21, 'features_id': 2, 'allow': 1},
            {'product_id': 21, 'features_id': 3, 'allow': 1},
            {'product_id': 21, 'features_id': 4, 'allow': 1},
            {'product_id': 21, 'features_id': 5, 'allow': 1},
            {'product_id': 21, 'features_id': 6, 'allow': 0},
            {'product_id': 21, 'features_id': 7, 'allow': 1},
            {'product_id': 21, 'features_id': 11, 'allow': 0},
            {'product_id': 22, 'features_id': 2, 'allow': 1},
            {'product_id': 22, 'features_id': 3, 'allow': 1},
            {'product_id': 22, 'features_id': 4, 'allow': 1},
            {'product_id': 22, 'features_id': 5, 'allow': 1},
            {'product_id': 22, 'features_id': 6, 'allow': 1},
            {'product_id': 22, 'features_id': 7, 'allow': 1},
            {'product_id': 22, 'features_id': 11, 'allow': 0},
        ]
    )


def downgrade() -> None:
    pass
