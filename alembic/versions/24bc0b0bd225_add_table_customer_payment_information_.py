"""add table customer_payment_information, notification_setting, account_notification_setting

Revision ID: 24bc0b0bd225
Revises: e30f1a1cb66f
Create Date: 2024-08-05 08:16:26.113886

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '24bc0b0bd225'
down_revision: Union[str, None] = 'e30f1a1cb66f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('notification_setting',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('key', sa.String(length=255), nullable=True),
    sa.Column('name', sa.String(length=255), nullable=True),
    sa.Column('type', sa.Enum('EMAIL', 'MESSENGER', 'DATA', name='typenotificationsettingenum'), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.Column('last_user_to_interact', sa.Integer(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_notification_setting_key'), 'notification_setting', ['key'], unique=True)
    op.create_index(op.f('ix_notification_setting_last_user_to_interact'), 'notification_setting', ['last_user_to_interact'], unique=False)
    op.create_table('account_notification_setting',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('notification_setting_id', sa.Integer(), nullable=True),
    sa.Column('customer_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.Column('last_user_to_interact', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['customer_id'], ['customer.id'], ),
    sa.ForeignKeyConstraint(['notification_setting_id'], ['notification_setting.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_account_notification_setting_last_user_to_interact'), 'account_notification_setting', ['last_user_to_interact'], unique=False)
    op.create_table('customer_payment_information',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(length=255), nullable=True),
    sa.Column('customer_id', sa.Integer(), nullable=True),
    sa.Column('payment_method_id', sa.Integer(), nullable=True),
    sa.Column('card_number', sa.Integer(), nullable=True),
    sa.Column('cardholder_name', sa.String(length=255), nullable=True),
    sa.Column('card_expiration_date', sa.DateTime(), nullable=True),
    sa.Column('card_cryptogram', sa.String(length=10), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.Column('last_user_to_interact', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['customer_id'], ['customer.id'], ),
    sa.ForeignKeyConstraint(['payment_method_id'], ['payment_method.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_customer_payment_information_last_user_to_interact'), 'customer_payment_information', ['last_user_to_interact'], unique=False)
    op.add_column('subscription_payment_transaction', sa.Column('customer_payment_information_id', sa.Integer(), nullable=True))
    op.add_column('subscription_payment_transaction', sa.Column('payment_status', sa.Enum('FAILURE', 'PENDING', 'SUCCESS', 'UNKNOWN', name='paymentstatusenum'), nullable=True))
    op.add_column('subscription_payment_transaction', sa.Column('cart_id', sa.Integer(), nullable=True))
    op.add_column('subscription_payment_transaction', sa.Column('is_new_subscription_payment', sa.Boolean(), nullable=True))
    op.create_foreign_key(None, 'subscription_payment_transaction', 'customer_payment_information', ['customer_payment_information_id'], ['id'])
    op.create_foreign_key(None, 'subscription_payment_transaction', 'cart', ['cart_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'subscription_payment_transaction', type_='foreignkey')
    op.drop_constraint(None, 'subscription_payment_transaction', type_='foreignkey')
    op.drop_column('subscription_payment_transaction', 'is_new_subscription_payment')
    op.drop_column('subscription_payment_transaction', 'cart_id')
    op.drop_column('subscription_payment_transaction', 'payment_status')
    op.drop_column('subscription_payment_transaction', 'customer_payment_information_id')
    op.drop_index(op.f('ix_customer_payment_information_last_user_to_interact'), table_name='customer_payment_information')
    op.drop_table('customer_payment_information')
    op.drop_index(op.f('ix_account_notification_setting_last_user_to_interact'), table_name='account_notification_setting')
    op.drop_table('account_notification_setting')
    op.drop_index(op.f('ix_notification_setting_last_user_to_interact'), table_name='notification_setting')
    op.drop_index(op.f('ix_notification_setting_key'), table_name='notification_setting')
    op.drop_table('notification_setting')
    # ### end Alembic commands ###
