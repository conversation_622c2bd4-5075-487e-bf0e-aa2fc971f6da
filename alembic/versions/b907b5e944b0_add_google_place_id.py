"""add google place id

Revision ID: b907b5e944b0
Revises: 4f212f833206
Create Date: 2024-12-16 04:41:17.397978

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'b907b5e944b0'
down_revision: Union[str, None] = '4f212f833206'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('city', sa.Column('google_place_id', mysql.VARCHAR(length=255), nullable=True))
    op.alter_column('invitation', 'invitation_link',
               existing_type=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=255),
               type_=sa.Text(),
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('invitation', 'invitation_link',
               existing_type=sa.Text(),
               type_=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=255),
               existing_nullable=True)
    op.drop_column('city', 'google_place_id')
    # ### end Alembic commands ###
