"""create continent table

Revision ID: 09245970bc1d
Revises: 1a4ecfc5d751
Create Date: 2025-02-26 08:38:18.767849

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '09245970bc1d'
down_revision: Union[str, None] = '1a4ecfc5d751'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('continent',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.Column('last_user_to_interact', sa.Integer(), nullable=True),
    sa.Column('import_id', sa.Text(), nullable=True),
    sa.Column('name', sa.String(length=255), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_continent_last_user_to_interact'), 'continent', ['last_user_to_interact'], unique=False)
    op.create_index(op.f('ix_continent_name'), 'continent', ['name'], unique=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_continent_name'), table_name='continent')
    op.drop_index(op.f('ix_continent_last_user_to_interact'), table_name='continent')
    op.drop_table('continent')
    # ### end Alembic commands ###
