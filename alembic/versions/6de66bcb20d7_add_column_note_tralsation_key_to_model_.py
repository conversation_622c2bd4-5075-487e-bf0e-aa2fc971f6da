"""add column note tralsation key to model product 

Revision ID: 6de66bcb20d7
Revises: 7e0ed8ad62a5
Create Date: 2025-02-18 06:06:56.678555

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '6de66bcb20d7'
down_revision: Union[str, None] = '7e0ed8ad62a5'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('product', sa.Column('description_note_translation_key', sa.String(length=255), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('product', 'description_note_translation_key')
    # ### end Alembic commands ###
