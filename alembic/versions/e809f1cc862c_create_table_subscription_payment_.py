"""create table subscription_payment_transaction

Revision ID: e809f1cc862c
Revises: e5d312f6490e
Create Date: 2024-07-30 11:02:08.170486

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'e809f1cc862c'
down_revision: Union[str, None] = 'e5d312f6490e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('subscription_payment_transaction',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('paid', sa.<PERSON>(), nullable=True),
    sa.Column('currency', sa.String(length=10), nullable=True),
    sa.Column('payment_date', sa.DateTime(), nullable=True),
    sa.Column('payment_method_id', sa.Integer(), nullable=True),
    sa.Column('subscription_id', sa.Integer(), nullable=True),
    sa.Column('next_payment_date', sa.DateTime(), nullable=True),
    sa.Column('payment_transaction_json', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.Column('last_user_to_interact', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['payment_method_id'], ['payment_method.id'], ),
    sa.ForeignKeyConstraint(['subscription_id'], ['subscription.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_subscription_payment_transaction_last_user_to_interact'), 'subscription_payment_transaction', ['last_user_to_interact'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_subscription_payment_transaction_last_user_to_interact'), table_name='subscription_payment_transaction')
    op.drop_table('subscription_payment_transaction')
    # ### end Alembic commands ###
