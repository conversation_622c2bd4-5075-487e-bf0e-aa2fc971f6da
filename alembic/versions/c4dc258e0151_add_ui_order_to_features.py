"""add ui_order to features

Revision ID: c4dc258e0151
Revises: a1a1e561dee5
Create Date: 2024-10-22 12:31:14.456078

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'c4dc258e0151'
down_revision: Union[str, None] = 'a1a1e561dee5'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('features', sa.Column('ui_order', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('features', 'ui_order')
    # ### end Alembic commands ###

