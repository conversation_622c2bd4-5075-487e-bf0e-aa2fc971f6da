"""add new region

Revision ID: 31218447b739
Revises: e80ba12c5a05
Create Date: 2025-04-15 05:52:44.722157

"""
import os  # Importer os
import json  # Assurez-vous que json est également importé
from typing import Sequence, Union
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql
from app.models.country import Country
from app.models.region import Region
from sqlalchemy.orm import sessionmaker

# révision identifiers
revision: str = '31218447b739'
down_revision: Union[str, None] = 'e80ba12c5a05'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def insert_data_from_json(json_data):
    bind = op.get_bind()
    session = sessionmaker(bind=bind)()
    for country_data in json_data:
        country = session.query(Country).filter(Country.code_alpha_2 == country_data["countryShortCode"]).first()

        if not country:
            print(f"Le pays avec le code {country_data['countryShortCode']} n'existe pas.")
            continue

        for region_data in country_data["regions"]:
            normalized_name = region_data["name"].lower().replace(" ", "-")

            # Vérifier si 'shortCode' existe, sinon assigner None (NULL dans la base de données)
            short_code = region_data.get("shortCode", None)

            region = Region(
                name=region_data["name"],
                short_code=short_code,
                normalized_name=normalized_name,
                country_id=country.id
            )
            session.add(region)

        session.commit()

def upgrade():
    migration_dir = os.path.dirname(__file__)

    # Construire le chemin vers 'alembic/data/regionv2.json'
    json_file_path = os.path.join(migration_dir, '..', 'data', 'regionv2.json')

    with open(json_file_path, 'r') as f:
        json_data = json.load(f)

    insert_data_from_json(json_data)

def downgrade():
    print("Pas de suppression des régions. Cette migration insère des données, donc aucune action de suppression n'est nécessaire.")
