"""add new feature to particular account

Revision ID: 5fc6da85c327
Revises: ea21ad2f969c
Create Date: 2025-05-15 09:50:54.635031

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '5fc6da85c327'
down_revision: Union[str, None] = 'ea21ad2f969c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    product_features = sa.table(
        'product_features',
        sa.column('product_id', sa.Integer()),
        sa.column('features_id', sa.Integer()),
        sa.column('allow', sa.Integer())
    )
    op.bulk_insert(
        product_features,
        [
            {'product_id': 11, 'features_id': 12, 'allow': 1},
            {'product_id': 12, 'features_id': 12, 'allow': 1},
            {'product_id': 13, 'features_id': 12, 'allow': 1},
            {'product_id': 17, 'features_id': 12, 'allow': 1},
        ]
    )


def downgrade() -> None:
    op.execute("""
        DELETE FROM product_features
        WHERE product_id IN (11, 12, 13, 17) AND features_id = 12
    """)
