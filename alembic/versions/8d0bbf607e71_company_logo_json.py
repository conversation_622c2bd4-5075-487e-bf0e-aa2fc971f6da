"""company_logo_json

Revision ID: 8d0bbf607e71
Revises: 9240d8e90503
Create Date: 2024-11-05 06:22:06.302915

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '8d0bbf607e71'
down_revision: Union[str, None] = '9240d8e90503'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('customer', sa.Column('company_logo_json', sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('customer', 'company_logo_json')
    # ### end Alembic commands ###
