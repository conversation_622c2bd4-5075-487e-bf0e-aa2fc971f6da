"""Add default_referential_info_json in customer table


Revision ID: c3dd1e3b845f
Revises: 292fccc95d8b
Create Date: 2024-10-08 14:46:04.556084

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'c3dd1e3b845f'
down_revision: Union[str, None] = '292fccc95d8b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('customer', sa.Column('default_referential_info_json', sa.JSON(), nullable=True))
    op.alter_column('subscription_payment_transaction', 'amount_paid',
               existing_type=mysql.DOUBLE(asdecimal=True),
               type_=sa.Float(precision=53),
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('subscription_payment_transaction', 'amount_paid',
               existing_type=sa.Float(precision=53),
               type_=mysql.DOUBLE(asdecimal=True),
               existing_nullable=True)
    op.drop_column('customer', 'default_referential_info_json')
    # ### end Alembic commands ###
