"""add bool consumption

Revision ID: 441b9744778e
Revises: 63f9bc113d0f
Create Date: 2025-03-24 07:41:34.811298

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '441b9744778e'
down_revision: Union[str, None] = '63f9bc113d0f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
   
    op.add_column('country', sa.Column('use_avg_residential_consumption', sa.<PERSON>(), nullable=True))
   
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    
    op.drop_column('country', 'use_avg_residential_consumption')
    
    # ### end Alembic commands ###
