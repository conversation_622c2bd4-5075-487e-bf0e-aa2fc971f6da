"""Ajout du champ region_id à la table simulation

Revision ID: bded8126d2b2
Revises: 9c9231d6ebb8
Create Date: 2025-04-09 05:53:48.966618

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'bded8126d2b2'
down_revision: Union[str, None] = '9c9231d6ebb8'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade():
    op.add_column('simulation', sa.Column('region_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'simulation', 'region', ['region_id'], ['id'])

def downgrade():
    op.drop_constraint(None, 'simulation', type_='foreignkey')
    op.drop_column('simulation', 'region_id')