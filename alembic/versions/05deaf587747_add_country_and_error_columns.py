"""Add country and error columns

Revision ID: 05deaf587747
Revises: 3439f760f37f
Create Date: 2025-01-21 11:34:40.391634

"""
import json
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '05deaf587747'
down_revision: Union[str, None] = '3439f760f37f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('country', sa.Column('email_sender_json', sa.JSON(), nullable=True))
    op.add_column('country', sa.Column('default_customer_json', sa.JSON(), nullable=True))
    op.add_column('customer_email_item', sa.Column('error', mysql.LONGTEXT(), nullable=True))
    senders = [
        {"email": "<EMAIL>", "name": "<PERSON>", "country_codes": ['gb']} , 
        {"email": "<EMAIL>", "name": "Marie", "country_codes": ['fr']} , 
        {"email": "<EMAIL>", "name": "Anna", "country_codes": ['ru']} , 
        {"email": "<EMAIL>", "name": "Amira", "country_codes": ["bd","dz", "bh", "km", "dj", "eg", "iq", "jo", "kw", "lb", "ly", "mr", "ma", "om", "ps", "qa", "sa", "so", "sd", "sy", "tn", "ae", "ye"]} , 
        {"email": "<EMAIL>", "name": "Yuino", "country_codes": ['cn']} , 
        {"email": "<EMAIL>", "name": "Hinata", "country_codes": ['jp']} , 
        {"email": "<EMAIL>", "name": "Ishika", "country_codes": ['in']} , 
    ]
    update_query = f"""
        UPDATE country
        SET email_sender_json = :sender_json
        WHERE code_alpha_2 IN :list_of_codes
    """
    for sender in senders: 
        country_codes = sender.pop("country_codes", None) 
        op.execute(sa.text(update_query).bindparams(sender_json=json.dumps(sender) , list_of_codes=tuple(country_codes)))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('customer_email_item', 'error')
    op.drop_column('country', 'default_customer_json')
    op.drop_column('country', 'email_sender_json')
    # ### end Alembic commands ###
