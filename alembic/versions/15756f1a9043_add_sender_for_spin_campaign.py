"""add sender for spin campaign

Revision ID: 15756f1a9043
Revises: dd456676eea4
Create Date: 2025-05-02 05:19:31.800278

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '15756f1a9043'
down_revision: Union[str, None] = 'dd456676eea4'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    
    customer_email_campaign = sa.table(
        'customer_email_campaign',
        sa.column('id', sa.Integer()),
        sa.column('name', sa.String()),
        sa.column('key', sa.String()),
        sa.column('is_active', sa.Integer())
    )

    op.bulk_insert(
        customer_email_campaign,
        [{
            'id': 5,
            'name': 'Spin TAP',
            'key': 'spin_t_a_p',
            'is_active': 0
        }]
    )

    customer_email_task = sa.table(
        'customer_email_task',
        sa.column('id', sa.Integer()),
        sa.column('key', sa.String()),
        sa.column('name', sa.String()),
        sa.column('email_number', sa.Integer()),
        sa.column('cms_key', sa.String()),
        sa.column('is_active', sa.Integer()),
        sa.column('customer_email_campaign_id', sa.Integer())
    )

    op.bulk_insert(
        customer_email_task,
        [
            {'id': 20, 'key': 'pvgis_email_campaign_day1_verify_quote', 'name': 'Verify Quote', 'email_number': 20, 'cms_key': 'pvgis-email-campaign-day1-verify-quote', 'is_active': 1, 'customer_email_campaign_id': 5},
            {'id': 21, 'key': 'pvgis_email_campaign_day3_exaggerated_estimates', 'name': 'Exaggerated estimates', 'email_number': 21, 'cms_key': 'pvgis-email-campaign-day3-exaggerated-estimates', 'is_active': 1, 'customer_email_campaign_id': 5},
            {'id': 22, 'key': 'pvgis_email_campaign_day5_long_term_loss', 'name': 'Long-term loss', 'email_number': 22, 'cms_key': 'pvgis-email-campaign-day5-long-term-loss', 'is_active': 1, 'customer_email_campaign_id': 5},
            {'id': 23, 'key': 'pvgis_email_campaign_day7_reliable_analysis_9eur', 'name': 'Reliable analysis (9€)', 'email_number': 23, 'cms_key': 'pvgis-email-campaign-day7-reliable-analysis-9eur', 'is_active': 1, 'customer_email_campaign_id': 5},
            {'id': 24, 'key': 'pvgis_email_campaign_day14_compare_offers', 'name': 'Compare offers', 'email_number': 24, 'cms_key': 'pvgis-email-campaign-day14-compare-offers', 'is_active': 1, 'customer_email_campaign_id': 5},
            {'id': 25, 'key': 'pvgis_email_campaign_day16_profitability_check', 'name': 'Profitability check', 'email_number': 25, 'cms_key': 'pvgis-email-campaign-day16-profitability-check', 'is_active': 1, 'customer_email_campaign_id': 5},
            {'id': 26, 'key': 'pvgis_email_campaign_day18_poor_choice_cost', 'name': 'Poor choice cost', 'email_number': 26, 'cms_key': 'pvgis-email-campaign-day18-poor-choice-cost', 'is_active': 1, 'customer_email_campaign_id': 5},
            {'id': 27, 'key': 'pvgis_email_campaign_day20_validate_project', 'name': 'Validate project', 'email_number': 27, 'cms_key': 'pvgis-email-campaign-day20-validate-project', 'is_active': 1, 'customer_email_campaign_id': 5}
        ]
    )

    
    
    ce_sender_email_campaign_country = sa.table(
        'ce_sender_email_campaign_country',
        sa.column('customer_email_campaign_id', sa.Integer()),
        sa.column('customer_email_sender_id', sa.Integer()),
        sa.column('country_id', sa.Integer())
    )
     
    op.bulk_insert(
        ce_sender_email_campaign_country,
        [
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 1, 'country_id': 73},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 2, 'country_id': 187},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 3, 'country_id': 105},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 4, 'country_id': 226},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 5, 'country_id': 235},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 6, 'country_id': 115},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 7, 'country_id': 52},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 8, 'country_id': 113},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 7, 'country_id': 225},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 9, 'country_id': 64},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 10, 'country_id': 66},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 11, 'country_id': 237},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 3, 'country_id': 16},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 6, 'country_id': 20},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 12, 'country_id': 217},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 13, 'country_id': 136},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 14, 'country_id': 99},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 9, 'country_id': 2},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 15, 'country_id': 39},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 7, 'country_id': 230},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 7, 'country_id': 144},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 15, 'country_id': 33},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 16, 'country_id': 163},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 17, 'country_id': 172},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 18, 'country_id': 18},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 19, 'country_id': 232},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 20, 'country_id': 3},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 21, 'country_id': 118},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 5, 'country_id': 138},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 19, 'country_id': 119},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 6, 'country_id': 56},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 21, 'country_id': 143},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 19, 'country_id': 117},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 22, 'country_id': 41},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 23, 'country_id': 179},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 16, 'country_id': 174},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 24, 'country_id': 159},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 19, 'country_id': 213},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 7, 'country_id': 241},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 17, 'country_id': 29},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 3, 'country_id': 21},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 5, 'country_id': 146},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 20, 'country_id': 188},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 4, 'country_id': 7},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 25, 'country_id': 82},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 13, 'country_id': 14},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 19, 'country_id': 121},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 19, 'country_id': 151},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 26, 'country_id': 154},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 13, 'country_id': 245},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 19, 'country_id': 128},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 13, 'country_id': 40},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 18, 'country_id': 8},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 27, 'country_id': 231},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 20, 'country_id': 45},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 25, 'country_id': 110},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 28, 'country_id': 70},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 7, 'country_id': 239},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 19, 'country_id': 147},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 29, 'country_id': 182},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 15, 'country_id': 156},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 27, 'country_id': 215},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 30, 'country_id': 75},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 16, 'country_id': 96},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 27, 'country_id': 208},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 7, 'country_id': 169},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 28, 'country_id': 127},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 31, 'country_id': 104},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 20, 'country_id': 209},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 17, 'country_id': 15},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 20, 'country_id': 22},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 29, 'country_id': 67},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 16, 'country_id': 109},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 32, 'country_id': 220},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 20, 'country_id': 74},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 19, 'country_id': 102},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 1, 'country_id': 59},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 18, 'country_id': 249},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 33, 'country_id': 24},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 34, 'country_id': 35},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 34, 'country_id': 17},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 13, 'country_id': 214},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 19, 'country_id': 114},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 33, 'country_id': 153},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 10, 'country_id': 185},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 13, 'country_id': 126},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 13, 'country_id': 207},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 35, 'country_id': 199},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 36, 'country_id': 28},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 37, 'country_id': 30},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 38, 'country_id': 88},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 10, 'country_id': 238},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 13, 'country_id': 196},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 22, 'country_id': 203},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 21, 'country_id': 135},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 22, 'country_id': 149},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 37, 'country_id': 38},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 16, 'country_id': 197},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 39, 'country_id': 10},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 40, 'country_id': 131},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 41, 'country_id': 134},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 22, 'country_id': 236},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 10, 'country_id': 11},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 42, 'country_id': 152},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 43, 'country_id': 171},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 44, 'country_id': 50},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 11, 'country_id': 166},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 20, 'country_id': 177},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 4, 'country_id': 106},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 45, 'country_id': 12},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 27, 'country_id': 1},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 29, 'country_id': 53},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 15, 'country_id': 168},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 37, 'country_id': 79},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 7, 'country_id': 85},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 6, 'country_id': 58},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 46, 'country_id': 158},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 16, 'country_id': 55},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 19, 'country_id': 65},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 20, 'country_id': 201},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 1, 'country_id': 78},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 42, 'country_id': 137},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 16, 'country_id': 243},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 39, 'country_id': 26},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 41, 'country_id': 34},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 14, 'country_id': 222},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 7, 'country_id': 242},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 16, 'country_id': 155},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 47, 'country_id': 107},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 7, 'country_id': 181},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 48, 'country_id': 244},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 27, 'country_id': 229},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 19, 'country_id': 31},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 49, 'country_id': 167},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 16, 'country_id': 48},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 36, 'country_id': 125},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 16, 'country_id': 192},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 19, 'country_id': 129},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 50, 'country_id': 233},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 51, 'country_id': 77},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 20, 'country_id': 86},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 52, 'country_id': 204},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 53, 'country_id': 184},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 18, 'country_id': 111},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 15, 'country_id': 116},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 54, 'country_id': 61},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 55, 'country_id': 94},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 15, 'country_id': 4},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 19, 'country_id': 140},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 51, 'country_id': 176},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 56, 'country_id': 190},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 19, 'country_id': 123},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 57, 'country_id': 98},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 58, 'country_id': 23},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 7, 'country_id': 37},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 55, 'country_id': 27},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 6, 'country_id': 112},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 59, 'country_id': 247},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 7, 'country_id': 161},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 60, 'country_id': 120},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 60, 'country_id': 162},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 56, 'country_id': 51},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 27, 'country_id': 100},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 29, 'country_id': 83},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 15, 'country_id': 248},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 19, 'country_id': 145},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 7, 'country_id': 89},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 27, 'country_id': 93},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 16, 'country_id': 69},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 27, 'country_id': 198},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 7, 'country_id': 142},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 19, 'country_id': 189},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 7, 'country_id': 240},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 20, 'country_id': 44},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 20, 'country_id': 202},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 19, 'country_id': 122},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 16, 'country_id': 157},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 20, 'country_id': 218},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 7, 'country_id': 130},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 7, 'country_id': 97},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 29, 'country_id': 84},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 61, 'country_id': 234},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 57, 'country_id': 101},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 49, 'country_id': 148},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 60, 'country_id': 90},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 56, 'country_id': 71},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 62, 'country_id': 193},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 20, 'country_id': 95},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 62, 'country_id': 80},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 56, 'country_id': 60},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 63, 'country_id': 133},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 57, 'country_id': 81},
            {'customer_email_campaign_id': 5, 'customer_email_sender_id': 51, 'country_id': 9},
        ]
    )


def downgrade() -> None:
    op.execute("""
        DELETE FROM customer_email_task
        WHERE customer_email_campaign_id = 5
    """)
    op.execute("""
        DELETE FROM customer_email_campaign
        WHERE id = 5
    """)
    
    op.execute("""
        DELETE FROM ce_sender_email_campaign_country
        WHERE customer_email_campaign_id = 5
    """)

