"""add stripe terminal status

Revision ID: 00113156e075
Revises: 31b935930577
Create Date: 2025-05-05 08:37:30.963287

"""
from typing import Sequence, Union
from sqlalchemy.dialects import mysql
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '00113156e075'
down_revision: Union[str, None] = '31b935930577'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    
    pass


def downgrade() -> None:
    op.drop_column('subscription_payment_transaction', 'stripe_terminal_status')
