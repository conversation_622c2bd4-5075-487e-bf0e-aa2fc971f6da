"""set to double lat and lon

Revision ID: cd214654cdfd
Revises: ca371712e8b1
Create Date: 2024-11-20 17:41:31.830848

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'cd214654cdfd'
down_revision: Union[str, None] = 'ca371712e8b1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f('ix_google_analytics_last_user_to_interact'), 'google_analytics', ['last_user_to_interact'], unique=False)
    op.alter_column('simulation', 'latitude',
               existing_type=mysql.DECIMAL(precision=8, scale=6),
               type_=mysql.DOUBLE(asdecimal=True),
               existing_nullable=False)
    op.alter_column('simulation', 'longitude',
               existing_type=mysql.DECIMAL(precision=8, scale=6),
               type_=mysql.DOUBLE(asdecimal=True),
               existing_nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('simulation', 'longitude',
               existing_type=mysql.DOUBLE(asdecimal=True),
               type_=mysql.DECIMAL(precision=8, scale=6),
               existing_nullable=False)
    op.alter_column('simulation', 'latitude',
               existing_type=mysql.DOUBLE(asdecimal=True),
               type_=mysql.DECIMAL(precision=8, scale=6),
               existing_nullable=False)
    op.drop_index(op.f('ix_google_analytics_last_user_to_interact'), table_name='google_analytics')
    # ### end Alembic commands ###
