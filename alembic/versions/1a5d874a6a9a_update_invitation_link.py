"""update invitation link

Revision ID: 1a5d874a6a9a
Revises: a947fb5af95b
Create Date: 2024-12-06 08:54:49.912732

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '1a5d874a6a9a'
down_revision: Union[str, None] = 'a947fb5af95b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('invitation', 'invitation_link',
               existing_type=mysql.VARCHAR(collation='utf8mb4_general_ci', length=255),
               type_=sa.Text(),
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('invitation', 'invitation_link',
               existing_type=sa.Text(),
               type_=mysql.VARCHAR(collation='utf8mb4_general_ci', length=255),
               existing_nullable=True)
    # ### end Alembic commands ###
