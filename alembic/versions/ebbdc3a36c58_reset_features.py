"""reset features

Revision ID: ebbdc3a36c58
Revises: 3194ab158fd5
Create Date: 2025-03-03 10:32:26.718777

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ebbdc3a36c58'
down_revision: Union[str, None] = '3194ab158fd5'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.execute('DELETE FROM product_features')
    op.execute('DELETE FROM features')
    


def downgrade() -> None:
    pass
