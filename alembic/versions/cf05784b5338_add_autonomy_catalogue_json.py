"""add autonomy catalogue json

Revision ID: cf05784b5338
Revises: 4abe404e6fa2
Create Date: 2025-06-05 05:23:49.468835

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'cf05784b5338'
down_revision: Union[str, None] = '4abe404e6fa2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
     
    op.add_column('customer', sa.Column('autonomy_catalogue_json', sa.JSON(), nullable=True))
    
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
     
    op.drop_column('customer', 'autonomy_catalogue_json')
    
    # ### end Alembic commands ###
