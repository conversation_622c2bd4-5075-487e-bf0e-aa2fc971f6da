"""create ip location table

Revision ID: e80ba12c5a05
Revises: 1dc09b4e607c
Create Date: 2025-04-14 11:15:56.979134

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'e80ba12c5a05'
down_revision: Union[str, None] = '1dc09b4e607c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('ip_location',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('deleted_at', sa.DateTime(), nullable=True),
        sa.Column('last_user_to_interact', sa.Integer(), nullable=True),
        sa.Column('import_id', sa.Text(), nullable=True),
        sa.Column('ip', sa.String(length=50), nullable=True),
        sa.Column('data_json', sa.JSON(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_ip_location_ip'), 'ip_location', ['ip'], unique=True)
    op.create_index(op.f('ix_ip_location_last_user_to_interact'), 'ip_location', ['last_user_to_interact'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_ip_location_last_user_to_interact'), table_name='ip_location')
    op.drop_index(op.f('ix_ip_location_ip'), table_name='ip_location')
    op.drop_table('ip_location')
    # ### end Alembic commands ###
