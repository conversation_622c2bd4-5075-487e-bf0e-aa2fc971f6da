"""add table for stripe webhook

Revision ID: 7285bf8e90d0
Revises: cd214654cdfd
Create Date: 2024-12-02 06:34:32.188582

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '7285bf8e90d0'
down_revision: Union[str, None] = 'cd214654cdfd'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('stripe_webhook',
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.Column('last_user_to_interact', sa.Integer(), nullable=True),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('event_id', sa.String(length=255), nullable=True),
    sa.Column('event_type', sa.String(length=255), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_stripe_webhook_last_user_to_interact'), 'stripe_webhook', ['last_user_to_interact'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_stripe_webhook_last_user_to_interact'), table_name='stripe_webhook')
    op.drop_table('stripe_webhook')
    # ### end Alembic commands ###
