"""update features

Revision ID: f2c2b09d66ea
Revises: ce55d82f8c15
Create Date: 2025-06-03 09:17:17.302023

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f2c2b09d66ea'
down_revision: Union[str, None] = 'ce55d82f8c15'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    conn = op.get_bind()
    conn.execute(sa.text("""
        UPDATE product_features
        SET allow = 1
        WHERE id IN (145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156)
    """))


def downgrade() -> None:
    conn = op.get_bind()
    conn.execute(sa.text("""
        UPDATE product_features
        SET allow = 0
        WHERE id IN (145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156)
    """))
