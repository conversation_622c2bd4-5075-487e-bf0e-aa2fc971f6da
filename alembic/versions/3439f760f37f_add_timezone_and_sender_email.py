"""Add timezone and sender_email

Revision ID: 3439f760f37f
Revises: 1783d9ec05ec
Create Date: 2025-01-16 08:46:47.292490

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '3439f760f37f'
down_revision: Union[str, None] = '1783d9ec05ec'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('country', sa.Column('timezone_offset', sa.String(length=50), nullable=True))
    op.add_column('customer_email_item', sa.Column('sender_email', sa.String(length=255), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('customer_email_item', 'sender_email')
    op.drop_column('country', 'timezone_offset')
    # ### end Alembic commands ###
