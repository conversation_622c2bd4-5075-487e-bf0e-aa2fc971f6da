"""add receipt status

Revision ID: 20c5a5d87aa3
Revises: 55dfe823d7e0
Create Date: 2025-08-11 07:59:19.543122

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '20c5a5d87aa3'
down_revision: Union[str, None] = '55dfe823d7e0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('subscription_payment_receipt', sa.Column('status', sa.Enum('FAILURE', 'PENDING', 'SUCCESS', name='receiptstatusenum'), nullable=True))
    op.execute(
        "UPDATE subscription_payment_receipt SET status = 'SUCCESS' WHERE status IS NULL"
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('subscription_payment_receipt', 'status')
    # ### end Alembic commands ###
