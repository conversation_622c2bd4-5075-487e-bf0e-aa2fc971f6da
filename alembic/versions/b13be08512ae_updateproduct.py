"""updateProduct

Revision ID: b13be08512ae
Revises: 8d0bbf607e71
Create Date: 2024-11-06 06:34:55.260500

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'b13be08512ae'
down_revision: Union[str, None] = '8d0bbf607e71'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('product', sa.Column('additional', sa.<PERSON>(), nullable=True))
   
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    
    op.drop_column('product', 'additional')

    # ### end Alembic commands ###
