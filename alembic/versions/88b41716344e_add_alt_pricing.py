"""add alt pricing

Revision ID: 88b41716344e
Revises: 40f30a74707d
Create Date: 2025-02-17 07:22:32.257847

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '88b41716344e'
down_revision: Union[str, None] = '40f30a74707d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('product_alt_pricing',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.Column('last_user_to_interact', sa.Integer(), nullable=True),
    sa.Column('import_id', sa.Text(), nullable=True),
    sa.Column('billing_period_interval', sa.Enum('year','month', name='billingperiodintervalenum'), nullable=False),
    sa.Column('stripe_price_id', sa.String(length=255), nullable=True),
    sa.Column('product_id', sa.Integer(), nullable=False),
    sa.Column('discount_stripe_id', sa.String(length=255), nullable=True),
    sa.Column('credit', sa.Float(), nullable=True),
    sa.Column('price', sa.Float(), nullable=True),
    sa.ForeignKeyConstraint(['product_id'], ['product.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_product_alt_pricing_last_user_to_interact'), 'product_alt_pricing', ['last_user_to_interact'], unique=False)
    op.add_column('subscription', sa.Column('product_alt_pricing_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'subscription', 'product_alt_pricing', ['product_alt_pricing_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'subscription', type_='foreignkey')
    op.drop_column('subscription', 'product_alt_pricing_id')
    op.drop_index(op.f('ix_product_alt_pricing_last_user_to_interact'), table_name='product_alt_pricing')
    op.drop_table('product_alt_pricing')
    # ### end Alembic commands ###
