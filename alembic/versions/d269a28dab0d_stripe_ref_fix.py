"""stripe ref fix

Revision ID: d269a28dab0d
Revises: f73953df8c67
Create Date: 2025-05-14 06:01:37.458235

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'd269a28dab0d'
down_revision: Union[str, None] = 'f73953df8c67'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.execute("""
                UPDATE subscription_payment_receipt spr
                JOIN subscription_payment_transaction spt 
                ON spr.subscription_payment_transaction_id = spt.id
                SET spr.stripe_reference = 
                CASE 
                    WHEN JSON_UNQUOTE(JSON_EXTRACT(spt.payment_transaction_json, '$.object')) = 'invoice'
                    THEN JSON_UNQUOTE(JSON_EXTRACT(spt.payment_transaction_json, '$.charge'))
                    ELSE JSON_UNQUOTE(JSON_EXTRACT(spt.payment_transaction_json, '$.latest_charge'))
                END
                WHERE JSON_UNQUOTE(JSON_EXTRACT(spt.payment_transaction_json, '$.object')) IN ('invoice', 'payment_intent')
    """)


def downgrade() -> None:
    pass
