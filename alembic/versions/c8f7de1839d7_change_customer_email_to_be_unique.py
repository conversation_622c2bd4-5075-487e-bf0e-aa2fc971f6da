"""change customer email to be unique

Revision ID: c8f7de1839d7
Revises: 3b9be2d3fe6e
Create Date: 2024-08-08 12:03:14.903634

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c8f7de1839d7'
down_revision: Union[str, None] = '3b9be2d3fe6e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint(None, 'customer', ['email'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'customer', type_='unique')
    # ### end Alembic commands ###
