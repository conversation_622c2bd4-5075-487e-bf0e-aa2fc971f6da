"""add_customer_import_tracking

Revision ID: 21b0eb0f9387
Revises: 433044cd334b
Create Date: 2025-03-06 11:17:54.683145

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '21b0eb0f9387'
down_revision: Union[str, None] = '433044cd334b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('customer_migration_error',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.Column('last_user_to_interact', sa.Integer(), nullable=True),
    sa.Column('company', sa.String(length=255), nullable=True),
    sa.Column('email', sa.String(length=255), nullable=True),
    sa.Column('import_id', sa.String(length=500), nullable=True),
    sa.Column('source_row_id', sa.Integer(), nullable=True),
    sa.Column('error', mysql.LONGTEXT(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_customer_migration_error_last_user_to_interact'), 'customer_migration_error', ['last_user_to_interact'], unique=False)
    
    op.add_column('customer', sa.Column('source_file_name', sa.String(length=500), nullable=True))
    op.add_column('customer', sa.Column('source_row_id', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('customer', 'source_row_id')
    op.drop_column('customer', 'source_file_name')
  
    op.drop_index(op.f('ix_customer_migration_error_last_user_to_interact'), table_name='customer_migration_error')
    op.drop_table('customer_migration_error')
    # ### end Alembic commands ###
