"""add use_avg_residential_consumption to monotonous_electricity_consumption

Revision ID: 9c9231d6ebb8
Revises: 8209739f3826
Create Date: 2025-04-04 08:32:54.048576

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '9c9231d6ebb8'
down_revision: Union[str, None] = '8209739f3826'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade():
    op.add_column('monotonous_electricity_consumption', sa.Column('use_avg_residential_consumption', sa.<PERSON>an(), nullable=True))

def downgrade():
    op.drop_column('monotonous_electricity_consumption', 'use_avg_residential_consumption')