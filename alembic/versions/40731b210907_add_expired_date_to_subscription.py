"""add expired date to subscription

Revision ID: 40731b210907
Revises: 1acc2f143dd8
Create Date: 2025-02-19 06:42:56.208507

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '40731b210907'
down_revision: Union[str, None] = '1acc2f143dd8'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('subscription', sa.Column('expired_date', sa.DateTime(), nullable=True))
    op.execute("""
        UPDATE subscription
        SET expired_date = date_add(start_date, interval 1 month)
        WHERE expired_date IS NULL
    """)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('subscription', 'expired_date')
    # ### end Alembic commands ###
