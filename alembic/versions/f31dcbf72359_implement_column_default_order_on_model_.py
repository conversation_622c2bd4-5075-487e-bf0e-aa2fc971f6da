"""implement column default order on model school category 

Revision ID: f31dcbf72359
Revises: 07479789cc87
Create Date: 2025-05-08 04:45:10.792194

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'f31dcbf72359'
down_revision: Union[str, None] = '07479789cc87'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('school_category', sa.Column('default_order', sa.Integer(), autoincrement=True, nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('school_category', 'default_order')
    # ### end Alembic commands ###
