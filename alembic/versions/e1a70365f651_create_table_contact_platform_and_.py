"""Create table contact_platform and customer_contact_platform_information

Revision ID: e1a70365f651
Revises: 5f4f378f3b9d
Create Date: 2024-08-02 09:49:41.246579

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'e1a70365f651'
down_revision: Union[str, None] = '5f4f378f3b9d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('contact_platform',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('key', sa.String(length=255), nullable=True),
    sa.Column('label', sa.String(length=255), nullable=True),
    sa.Column('type', sa.Enum('SOCIAL', 'MESSENGER', name='contacttypeenum'), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.Column('last_user_to_interact', sa.Integer(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_contact_platform_key'), 'contact_platform', ['key'], unique=True)
    op.create_index(op.f('ix_contact_platform_last_user_to_interact'), 'contact_platform', ['last_user_to_interact'], unique=False)
    op.create_table('customer_contact_platform_information',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('value', sa.String(length=255), nullable=True),
    sa.Column('contact_platform_id', sa.Integer(), nullable=True),
    sa.Column('customer_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.Column('last_user_to_interact', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['contact_platform_id'], ['contact_platform.id'], ),
    sa.ForeignKeyConstraint(['customer_id'], ['customer.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_customer_contact_platform_information_last_user_to_interact'), 'customer_contact_platform_information', ['last_user_to_interact'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_customer_contact_platform_information_last_user_to_interact'), table_name='customer_contact_platform_information')
    op.drop_table('customer_contact_platform_information')
    op.drop_index(op.f('ix_contact_platform_last_user_to_interact'), table_name='contact_platform')
    op.drop_index(op.f('ix_contact_platform_key'), table_name='contact_platform')
    op.drop_table('contact_platform')
    # ### end Alembic commands ###
