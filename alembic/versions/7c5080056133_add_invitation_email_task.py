"""add invitation email task

Revision ID: 7c5080056133
Revises: ebbdc3a36c58
Create Date: 2025-03-04 07:54:44.608237

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '7c5080056133'
down_revision: Union[str, None] = 'ebbdc3a36c58'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    customer_email_task = sa.table(
        'customer_email_task',  
        sa.column('id', sa.Integer()),  
        sa.column('key', sa.String()),  
        sa.column('name', sa.String()),  
        sa.column('email_number', sa.Integer()),  
        sa.column('cms_key', sa.String()),
        sa.column('is_active', sa.Integer()) 
    )

    op.bulk_insert(
        customer_email_task,
        [{
            'id': 6, 
            'key': 'installer_invitation', 
            'name': 'Installer Invitation', 
            'email_number': 6, 
            'cms_key': 'pvgis-installer-invitation',
            'is_active': 0
        }]
    )


def downgrade() -> None:
    op.execute("""
        DELETE FROM customer_email_task
        WHERE key = 'installer_invitation'
    """)
