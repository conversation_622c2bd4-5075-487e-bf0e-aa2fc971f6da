"""update acount information models

Revision ID: 8ad6b30b2a36
Revises: 170c36ac0700
Create Date: 2025-04-29 06:21:31.655661

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '8ad6b30b2a36'
down_revision: Union[str, None] = '170c36ac0700'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('account_information', sa.Column('other_category', sa.String(length=255), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('account_information', 'other_category')
    # ### end Alembic commands ###
