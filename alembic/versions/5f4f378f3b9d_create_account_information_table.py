"""create account information table

Revision ID: 5f4f378f3b9d
Revises: da789db27d96
Create Date: 2024-08-02 06:44:55.645633

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '5f4f378f3b9d'
down_revision: Union[str, None] = 'da789db27d96'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('account_information',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('customer_id', sa.Integer(), nullable=True),
    sa.Column('account_type_id', sa.Integer(), nullable=True),
    sa.Column('professional_category_id', sa.Integer(), nullable=True),
    sa.Column('school_category_id', sa.Integer(), nullable=True),
    sa.Column('profession', sa.String(length=255), nullable=True),
    sa.Column('age_range', sa.Enum('AGE_18_24', 'AGE_25_34', 'AGE_35_49', 'AGE_50_64', 'AGE_65_PLUS', name='agerangeenum'), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.Column('last_user_to_interact', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['account_type_id'], ['account_type.id'], ),
    sa.ForeignKeyConstraint(['customer_id'], ['customer.id'], ),
    sa.ForeignKeyConstraint(['professional_category_id'], ['professional_category.id'], ),
    sa.ForeignKeyConstraint(['school_category_id'], ['school_category.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_account_information_last_user_to_interact'), 'account_information', ['last_user_to_interact'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_account_information_last_user_to_interact'), table_name='account_information')
    op.drop_table('account_information')
    # ### end Alembic commands ###
