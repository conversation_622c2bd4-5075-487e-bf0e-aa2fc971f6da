"""Add style and region_id to MonotonousElectricityConsumption

Revision ID: 8209739f3826
Revises: 9fd252b25500
Create Date: 2025-04-04 07:11:13.894841

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '8209739f3826'
down_revision: Union[str, None] = '9fd252b25500'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade():
    op.add_column('monotonous_electricity_consumption', sa.Column('style', mysql.LONGTEXT(), nullable=True))
    op.add_column('monotonous_electricity_consumption', sa.Column('region_id', sa.Integer(), nullable=True))
    op.create_foreign_key('fk_monotonous_electricity_consumption_region', 'monotonous_electricity_consumption', 'region', ['region_id'], ['id'])

def downgrade():
    op.drop_column('monotonous_electricity_consumption', 'style')
    op.drop_column('monotonous_electricity_consumption', 'region_id')