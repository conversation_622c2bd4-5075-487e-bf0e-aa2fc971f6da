"""add status pvgis24 in subscription

Revision ID: ca371712e8b1
Revises: 960eb275d0a9
Create Date: 2024-11-19 07:48:18.518588

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'ca371712e8b1'
down_revision: Union[str, None] = '960eb275d0a9'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('subscription', 'subscription_status', type_=sa.Enum('ACTIVE', 'PENDING', 'PVGIS24', 'INACTIVE', name='subscriptionstatusenum'), nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('subscription', 'subscription_status', type_=sa.Enum('ACTIVE', 'PENDING', 'INACTIVE', name='subscriptionstatusenum'), nullable=True)
    # ### end Alembic commands ###
