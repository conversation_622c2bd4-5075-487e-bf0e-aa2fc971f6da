"""add column simulation info

Revision ID: cab246d36604
Revises: 88634b1b0135
Create Date: 2024-09-06 10:17:30.222263

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = 'cab246d36604'
down_revision: Union[str, None] = '88634b1b0135'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('simulation', sa.Column('simulation_info', sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('simulation', 'simulation_info')
    # ### end Alembic commands ###
