"""refactor free features

Revision ID: 02e18c309217
Revises: 44c8aaff48c6
Create Date: 2025-03-19 10:39:58.977804

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '02e18c309217'
down_revision: Union[str, None] = '44c8aaff48c6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute('DELETE FROM product_features')
    op.execute('DELETE FROM features')
    op.add_column('features', sa.Column('allow_for_free', sa.Bo<PERSON>an(), nullable=False))
    op.drop_column('features', 'free_list_decorator')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('features', sa.Column('free_list_decorator', mysql.VARCHAR(collation='utf8mb4_general_ci', length=255), nullable=True))
    op.drop_column('features', 'allow_for_free')
    
    # ### end Alembic commands ###
