"""implement column languagea to model testimony

Revision ID: 62578a9a3706
Revises: 46de296fa504
Create Date: 2025-04-16 08:29:23.668917

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '62578a9a3706'
down_revision: Union[str, None] = '46de296fa504'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('testimony', sa.Column('languages', sa.String(length=100), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('testimony', 'languages')
    # ### end Alembic commands ###
