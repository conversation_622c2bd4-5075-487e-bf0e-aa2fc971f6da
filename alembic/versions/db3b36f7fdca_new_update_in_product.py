"""new update in product

Revision ID: db3b36f7fdca
Revises: 10f068080232
Create Date: 2025-05-26 06:08:38.856425

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'db3b36f7fdca'
down_revision: Union[str, None] = '10f068080232'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    conn = op.get_bind()

    # conn.execute(sa.text("UPDATE product SET name = 'PVGIS 24 ONLY' WHERE id IN (2, 8);"))
    # conn.execute(sa.text("UPDATE product SET name = 'Resale & Self-Consumption' WHERE id IN (3, 9);"))
    # conn.execute(sa.text("UPDATE product SET name = 'Autonomy' WHERE id IN (4, 10);"))
    conn.execute(sa.text("UPDATE product SET name = 'PVGIS24 ONLY', monthly_price = 9 WHERE id = 11;"))
    conn.execute(sa.text("UPDATE product SET name = 'Resale & Self-Consumption', monthly_price = 19 WHERE id = 12;"))
    conn.execute(sa.text("UPDATE product SET name = 'Autonomy', monthly_price = 29 WHERE id = 13;"))

    conn.execute(sa.text("""
        INSERT INTO product (
            id, name, user_count, monthly_credit, monthly_price, `show`, allow_new_subscribers,
            is_recommended, description_translation_key, ui_order,
            subscription_max_count, allow_trial, additional, import_id,
            description_note_translation_key, billing_period_interval, type
        ) VALUES
        (20, 'PVGIS24 ONLY', 1, 3, 19, 1, 1, 0,
            'pvgis.product_revente_totale_bref_desc', 20,
            NULL, NULL, NULL, NULL,
            'pvgis.product_revente_totale_note', 'year', 2),
        (21, 'Resale & Self-Consumption', 1, 3, 29, 1, 1, 0,
            'pvgis.product_hybride_bref_desc', 21,
            NULL, NULL, NULL, NULL,
            'pvgis.product_hybride_note', 'year', 2),
        (22, 'Autonomy', 1, 3, 49, 1, 1, 0,
            'pvgis.product_autonomie_bref_desc', 22,
            NULL, NULL, NULL, NULL,
            'pvgis.product_autonomie_note', 'year', 2);
    """))

def downgrade() -> None:
    conn = op.get_bind()
    conn.execute(sa.text("""
        UPDATE product SET name = 'PVGIS24 PREMIUM' WHERE id IN (2, 8);
        UPDATE product SET name = 'PVGIS24 PRO' WHERE id IN (3, 9);
        UPDATE product SET name = 'PVGIS24 EXPERT' WHERE id IN (4, 10);
        UPDATE product SET name = 'PVGIS24 Revente Totale', monthly_price = 19 WHERE id = 11;
        UPDATE product SET name = 'PVGIS24 Hybride', monthly_price = 25 WHERE id = 12;
        UPDATE product SET name = 'PVGIS24 Autonomie', monthly_price = 29 WHERE id = 13;
    """))
    
    conn.execute(sa.text("""
        DELETE FROM product WHERE id IN (20, 21, 22);
    """))