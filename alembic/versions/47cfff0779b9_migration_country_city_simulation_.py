"""migration country, city, simulation listing

Revision ID: 47cfff0779b9
Revises: fbcad79f6367
Create Date: 2024-12-05 11:30:32.386434

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '47cfff0779b9'
down_revision: Union[str, None] = 'fbcad79f6367'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('country',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.Column('last_user_to_interact', sa.Integer(), nullable=True),
    sa.Column('name', sa.String(length=255), nullable=True),
    sa.Column('import_id', sa.String(length=255), nullable=True),
    sa.Column('codeAlpha2', sa.String(length=10), nullable=True),
    sa.Column('codeAlpha3', sa.String(length=10), nullable=True),
    sa.Column('currencies', sa.JSON(), nullable=True),
    sa.Column('languages', sa.JSON(), nullable=True),
    sa.Column('status', sa.Boolean(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('codeAlpha2'),
    sa.UniqueConstraint('codeAlpha3')
    )
    op.create_index(op.f('ix_country_last_user_to_interact'), 'country', ['last_user_to_interact'], unique=False)
    op.create_index(op.f('ix_country_name'), 'country', ['name'], unique=False)
    op.create_table('city',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.Column('last_user_to_interact', sa.Integer(), nullable=True),
    sa.Column('name', sa.String(length=255), nullable=True),
    sa.Column('latitude', mysql.DOUBLE(asdecimal=True), nullable=False),
    sa.Column('longitude', mysql.DOUBLE(asdecimal=True), nullable=False),
    sa.Column('import_id', sa.String(length=255), nullable=True),
    sa.Column('country_id', sa.Integer(), nullable=True),
    sa.Column('status', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['country_id'], ['country.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_city_last_user_to_interact'), 'city', ['last_user_to_interact'], unique=False)
    op.create_index(op.f('ix_city_name'), 'city', ['name'], unique=False)
    op.create_table('simulation_listing',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.Column('last_user_to_interact', sa.Integer(), nullable=True),
    sa.Column('city_id', sa.Integer(), nullable=True),
    sa.Column('simulation_data', sa.JSON(), nullable=True),
    sa.Column('simulation_image', sa.JSON(), nullable=True),
    sa.Column('content', mysql.LONGTEXT(), nullable=True),
    sa.ForeignKeyConstraint(['city_id'], ['city.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_simulation_listing_last_user_to_interact'), 'simulation_listing', ['last_user_to_interact'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_simulation_listing_last_user_to_interact'), table_name='simulation_listing')
    op.drop_table('simulation_listing')
    op.drop_index(op.f('ix_city_name'), table_name='city')
    op.drop_index(op.f('ix_city_last_user_to_interact'), table_name='city')
    op.drop_table('city')
    op.drop_index(op.f('ix_country_name'), table_name='country')
    op.drop_index(op.f('ix_country_last_user_to_interact'), table_name='country')
    op.drop_table('country')
    # ### end Alembic commands ###
