"""Add city migration management

Revision ID: d768eb357748
Revises: b907b5e944b0
Create Date: 2024-12-19 07:18:51.275217

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'd768eb357748'
down_revision: Union[str, None] = 'b907b5e944b0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('city', sa.Column('source_file_name', mysql.VARCHAR(length=500), nullable=True))
    op.add_column('city', sa.Column('source_row_id', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('city', 'source_row_id')
    op.drop_column('city', 'source_file_name')
    # ### end Alembic commands ###
