"""create table simulation item

Revision ID: 284f26d570e8
Revises: c9f71141f546
Create Date: 2024-07-30 09:47:20.867560

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '284f26d570e8'
down_revision: Union[str, None] = 'c9f71141f546'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('simulation_item',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('params_json', sa.JSON(), nullable=True),
    sa.Column('name', sa.String(length=255), nullable=True),
    sa.Column('energy_purpose', sa.String(length=255), nullable=True),
    sa.Column('created_by', sa.Integer(), nullable=True),
    sa.Column('simulation_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.Column('last_user_to_interact', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['simulation_id'], ['simulation.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_simulation_item_last_user_to_interact'), 'simulation_item', ['last_user_to_interact'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_simulation_item_last_user_to_interact'), table_name='simulation_item')
    op.drop_table('simulation_item')
    # ### end Alembic commands ###
