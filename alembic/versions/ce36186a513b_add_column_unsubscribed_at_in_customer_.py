"""add column unsubscribed_at in customer model

Revision ID: ce36186a513b
Revises: 3f503cb6ed10
Create Date: 2025-04-08 06:57:04.728805

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ce36186a513b'
down_revision: Union[str, None] = '3f503cb6ed10'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('customer', sa.Column('unsubscribed_at', sa.DateTime(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('customer', 'unsubscribed_at')
    # ### end Alembic commands ###
