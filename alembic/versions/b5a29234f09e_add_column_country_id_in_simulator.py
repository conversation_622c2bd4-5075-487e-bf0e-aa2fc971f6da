"""add country_id in simluation tablr

Revision ID: b5a29234f09e
Revises: fa200d61a0fa
Create Date: 2025-03-27 10:20:27.560778

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'b5a29234f09e'
down_revision: Union[str, None] = 'fa200d61a0fa'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Add the country_id column
    op.add_column('simulation', sa.Column('country_id', sa.Integer(), nullable=True))

    # Ensure the country table has the correct index
    op.create_index('idx_country_id', 'country', ['id'])

    # Create the foreign key constraint
    op.create_foreign_key('fk_simulation_country', 'simulation', 'country', ['country_id'], ['id'])

def downgrade() -> None:
    # Drop the foreign key constraint
    op.drop_constraint('fk_simulation_country', 'simulation', type_='foreignkey')

    # Drop the country_id column
    op.drop_column('simulation', 'country_id')