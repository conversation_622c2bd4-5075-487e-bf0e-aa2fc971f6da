"""update receipt

Revision ID: fa588360f586
Revises: 02e6ade6cb69
Create Date: 2025-05-09 08:51:17.042174

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'fa588360f586'
down_revision: Union[str, None] = '02e6ade6cb69'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
     
    op.add_column('subscription_payment_receipt', sa.Column('amount_paid', mysql.DOUBLE(asdecimal=True), nullable=True))
    op.add_column('subscription_payment_receipt', sa.Column('refunded_stripe_object_id', sa.String(length=255), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('subscription_payment_receipt', 'refunded_stripe_object_id')
    op.drop_column('subscription_payment_receipt', 'amount_paid')
     
    # ### end Alembic commands ###
