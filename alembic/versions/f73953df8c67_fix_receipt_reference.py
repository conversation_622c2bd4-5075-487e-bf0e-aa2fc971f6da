"""fix receipt reference

Revision ID: f73953df8c67
Revises: 469d73368bd4
Create Date: 2025-05-13 11:46:08.554378

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f73953df8c67'
down_revision: Union[str, None] = '469d73368bd4'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.execute("""
               UPDATE subscription_payment_receipt
                SET reference = CONCAT(
                    DATE_FORMAT(created_at, '%Y-%m'), 
                    '-', 
                    LPAD(id, 4, '0')
                )
               """)


def downgrade() -> None:
    pass
