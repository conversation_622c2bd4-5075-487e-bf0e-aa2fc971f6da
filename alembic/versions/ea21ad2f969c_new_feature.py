"""new feature

Revision ID: ea21ad2f969c
Revises: d6520abb13cb
Create Date: 2025-05-15 09:39:31.822317

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ea21ad2f969c'
down_revision: Union[str, None] = 'd6520abb13cb'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    features = sa.table(
        'features',
        sa.column('id', sa.Integer()),
        sa.column('name', sa.String()),
        sa.column('description', sa.String()),
        sa.column('key', sa.String()),
        sa.column('ui_order', sa.Integer()),
        sa.column('display_for_free', sa.Integer()),
        sa.column('allow_for_free', sa.Integer()),
        sa.column('hide', sa.Integer()),
    )

    op.bulk_insert(
        features,
        [{
            'id': 12,
            'name': 'Change active project',
            'description': 'Can change the project',
            'key': 'update_project',
            'display_for_free': 1,
            'allow_for_free': 1,
            'hide': 1,
        }]
    )


def downgrade() -> None:
    op.execute("""
        DELETE FROM features
        WHERE id = 12
    """)
