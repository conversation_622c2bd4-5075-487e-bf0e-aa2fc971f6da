"""new table

Revision ID: 4d48aef98eda
Revises: 62578a9a3706
Create Date: 2025-04-15 10:05:51.640665

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '4d48aef98eda'
down_revision: Union[str, None] = '62578a9a3706'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        'monotonous_electricity_consumption_daily',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('country_id', sa.Integer(), nullable=True),
        sa.Column('region_id', sa.Integer(), nullable=True),
        sa.Column('residential_consumption_json', sa.JSON(), nullable=True),
        sa.Column('content', mysql.LONGTEXT(), nullable=True),
        sa.Column('style', mysql.LONGTEXT(), nullable=True),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('deleted_at', sa.DateTime(), nullable=True),
        sa.Column('import_id', sa.Text(), nullable=True),
        sa.Column('last_user_to_interact', sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(['region_id'], ['region.id'], name='monotonous_electricity_consumption_daily_ibfk_12'),
        sa.ForeignKeyConstraint(['country_id'], ['country.id'], name='monotonous_electricity_consumption_daily_ibfk_1'),
        sa.PrimaryKeyConstraint('id')
    )



def downgrade() -> None:
    op.drop_table('monotonous_electricity_consumption_daily')
