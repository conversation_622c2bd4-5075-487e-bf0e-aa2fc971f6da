"""Add region table

Revision ID: 9fd252b25500
Revises: d2a001293a4b
Create Date: 2025-04-03 08:49:20.172076

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql
from app.models.country import Country
from app.models.region import Region
from sqlalchemy.orm import sessionmaker
import json
import os
from sqlalchemy import inspect


# revision identifiers, used by Alembic.
revision: str = '9fd252b25500'
down_revision: Union[str, None] = 'd2a001293a4b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None



def insert_data_from_json(json_data):
    bind = op.get_bind()
    session = sessionmaker(bind=bind)()
    for country_data in json_data:
        country = session.query(Country).filter(Country.code_alpha_2 == country_data["countryShortCode"]).first()
        
        if not country:
            print(f"Le pays avec le code {country_data['countryShortCode']} n'existe pas.")
            continue  

        for region_data in country_data["regions"]:
            normalized_name = region_data["name"].lower().replace(" ", "-")
            
            # Vérifier si 'shortCode' existe, sinon assigner None (NULL dans la base de données)
            short_code = region_data.get("shortCode", None)
            
            region = Region(
                name=region_data["name"],
                short_code=short_code,
                normalized_name=normalized_name,
                country_id=country.id  
            )
            session.add(region)

        session.commit()



def upgrade():
  
    op.create_table(
        'region',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=True),
        sa.Column('short_code', sa.String(length=10), nullable=True),
        sa.Column('country_id', sa.Integer(), nullable=True),
        sa.Column('normalized_name', sa.String(length=255), nullable=True),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('deleted_at', sa.DateTime(), nullable=True),
        sa.Column('import_id', sa.Text(), nullable=True),
        sa.Column('last_user_to_interact', sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(['country_id'], ['country.id'], name='region_ibfk_1'),
        sa.PrimaryKeyConstraint('id')
     
    )
    
   
    migration_dir = os.path.dirname(__file__)

    # Construire le chemin vers 'alembic/data/region.json'
    json_file_path = os.path.join(migration_dir, '..', 'data', 'region.json')

    
    with open(json_file_path, 'r') as f:
        json_data = json.load(f)
    
 
    insert_data_from_json(json_data)

def downgrade():
    inspector = inspect(bind)
    if 'region' in inspector.get_table_names():
        op.drop_table('region')
        op.drop_index(op.f('region_ibfk_1'), table_name='region')
    else:
        print("La table 'region' n'existe pas.")
