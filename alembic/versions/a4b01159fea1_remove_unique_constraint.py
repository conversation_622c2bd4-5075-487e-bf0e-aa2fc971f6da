"""remove unique constraint

Revision ID: a4b01159fea1
Revises: fa588360f586
Create Date: 2025-05-09 09:48:07.335747

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'a4b01159fea1'
down_revision: Union[str, None] = 'fa588360f586'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    
    op.drop_index('stripe_object_id', table_name='subscription_payment_receipt')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('stripe_object_id', 'subscription_payment_receipt', ['stripe_object_id'], unique=True)
     
    # ### end Alembic commands ###
