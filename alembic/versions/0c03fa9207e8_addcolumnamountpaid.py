"""AddColumnAmountPaid

Revision ID: 0c03fa9207e8
Revises: fa72f360b08b
Create Date: 2024-08-23 11:04:18.993404

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0c03fa9207e8'
down_revision: Union[str, None] = 'fa72f360b08b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('subscription_payment_transaction', sa.Column('amount_paid', sa.Float(precision=53), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('subscription_payment_transaction', 'amount_paid')
    # ### end Alembic commands ###
