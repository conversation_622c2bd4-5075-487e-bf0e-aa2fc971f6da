"""add colomn support to model customer

Revision ID: 373e62a7694c
Revises: b5a29234f09e
Create Date: 2025-04-01 06:16:35.170612

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '373e62a7694c'
down_revision: Union[str, None] = 'b5a29234f09e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('customer', sa.Column('support', sa.Enum('THINKING_ABOUT_PERSONAL_PROJECT', 'CHECK_A_QUOTE_OR_AN_OFFER', 'CHECK_ITS_PROFITABILITY', name='supportenum'), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('idx_country_id', 'country', ['id'], unique=False)
    # ### end Alembic commands ###
