"""Add column timezone_offset to customer

Revision ID: 498b5b36e81a
Revises: 05deaf587747
Create Date: 2025-01-22 10:42:35.225673

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '498b5b36e81a'
down_revision: Union[str, None] = '05deaf587747'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('customer', sa.Column('country_id', sa.Integer(), nullable=True))
    op.add_column('customer', sa.Column('timezone_offset', sa.String(length=50), nullable=True))
    op.create_foreign_key(None, 'customer', 'country', ['country_id'], ['id'])
    
    op.execute('update customer cu join country co on co.name = cu.country set cu.country_id = co.id')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'customer', type_='foreignkey')
    op.drop_column('customer', 'timezone_offset')
    op.drop_column('customer', 'country_id')
    # ### end Alembic commands ###
