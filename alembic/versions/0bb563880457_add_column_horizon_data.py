"""Add column horizon_data

Revision ID: 0bb563880457
Revises: d14279c33bb7
Create Date: 2025-01-15 04:46:25.234551

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '0bb563880457'
down_revision: Union[str, None] = 'd14279c33bb7'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('simulation_listing', sa.Column('horizon_data', mysql.LONGTEXT(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('simulation_listing', 'horizon_data')
    # ### end Alembic commands ###
