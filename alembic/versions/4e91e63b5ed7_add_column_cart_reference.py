"""add column cart reference

Revision ID: 4e91e63b5ed7
Revises: ebc37e21a260
Create Date: 2024-09-16 07:05:19.399073

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '4e91e63b5ed7'
down_revision: Union[str, None] = 'ebc37e21a260'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('customer', sa.Column('cart_reference', sa.String(length=255), nullable=True))
   
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('customer', 'cart_reference')
    # ### end Alembic commands ###
