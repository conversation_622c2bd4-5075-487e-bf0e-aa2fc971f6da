"""DropCustomerPaymentInformation

Revision ID: b8dfe2e79f1a
Revises: d5df21d43959
Create Date: 2024-08-22 09:26:44.175838

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'b8dfe2e79f1a'
down_revision: Union[str, None] = 'd5df21d43959'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_customer_payment_information_last_user_to_interact', table_name='customer_payment_information')
    op.drop_table('customer_payment_information')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('customer_payment_information',
    sa.Column('id', mysql.INTEGER(display_width=11), autoincrement=True, nullable=False),
    sa.Column('name', mysql.VARCHAR(length=255), nullable=True),
    sa.Column('customer_id', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True),
    sa.Column('payment_method_id', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True),
    sa.Column('card_number', mysql.VARCHAR(length=16), nullable=True),
    sa.Column('cardholder_name', mysql.VARCHAR(length=255), nullable=True),
    sa.Column('card_expiration_date', mysql.DATETIME(), nullable=True),
    sa.Column('card_cryptogram', mysql.VARCHAR(length=10), nullable=True),
    sa.Column('created_at', mysql.DATETIME(), server_default=sa.text('current_timestamp()'), nullable=True),
    sa.Column('updated_at', mysql.DATETIME(), nullable=True),
    sa.Column('deleted_at', mysql.DATETIME(), nullable=True),
    sa.Column('last_user_to_interact', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['customer_id'], ['customer.id'], name='customer_payment_information_ibfk_1'),
    sa.ForeignKeyConstraint(['payment_method_id'], ['payment_method.id'], name='customer_payment_information_ibfk_2'),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_general_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.create_index('ix_customer_payment_information_last_user_to_interact', 'customer_payment_information', ['last_user_to_interact'], unique=False)
    # ### end Alembic commands ###
