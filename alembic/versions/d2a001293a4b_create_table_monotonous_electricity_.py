"""create table monotonous_electricity_consumption

Revision ID: d2a001293a4b
Revises: 683af19b4e6b
Create Date: 2025-04-01 07:17:14.773295

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'd2a001293a4b'
down_revision: Union[str, None] = '683af19b4e6b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade():
    # Create the new table
    op.create_table(
        'monotonous_electricity_consumption',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('country_id', sa.Integer(), nullable=True),
        sa.Column('residential_consumption_json', sa.JSON(), nullable=True),
        sa.Column('content', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('deleted_at', sa.DateTime(), nullable=True),
        sa.Column('import_id', sa.Text(), nullable=True),
        sa.Column('last_user_to_interact', sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(['country_id'], ['country.id'], name='monotonous_electricity_consumption_ibfk_1'),
        sa.PrimaryKeyConstraint('id')
    )


def downgrade():
    op.drop_table('monotonous_electricity_consumption'),
    op.drop_index(op.f('monotonous_electricity_consumption_ibfk_1'), table_name='monotonous_electricity_consumption')