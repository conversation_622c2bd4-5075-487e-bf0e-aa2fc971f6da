"""populate customer json

Revision ID: 55dfe823d7e0
Revises: 28b41c2d9ec6
Create Date: 2025-07-28 06:45:48.107632

""" 
from typing import Sequence, Union
from app.models.customer import Customer
from app.models.subscription import Subscription
from app.models.subscription_payment_transaction import SubscriptionPaymentTransaction 
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql 
from sqlalchemy.orm import sessionmaker,joinedload
from fastapi.encoders import jsonable_encoder
import json

# revision identifiers, used by Alembic.
revision: str = '55dfe823d7e0'
down_revision: Union[str, None] = '28b41c2d9ec6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    bind = op.get_bind()
    session = None 
    try:
        session = sessionmaker(bind=bind)()
        while hasMoreEmptyCustomerJson(session):
            customers = (
                generateCustomerJsonPopulateQuery(session)
                .limit(100)
                .all()
            )
            for customer in customers:
                customer.autonomy_catalogue_json = None
                customer.default_referential_info_json = None
                session.execute(
                    """
                    UPDATE subscription_payment_transaction spt 
                    join subscription sub on spt.subscription_id = sub.id
                    SET spt.customer_json = :customer_json
                    WHERE sub.customer_id = :customer_id
                    """,
                    {
                        "customer_json": json.dumps(jsonable_encoder(customer)),
                        "customer_id": customer.id,
                    },
                )
            session.commit()
    except Exception as e:
        if session:
            session.rollback()
        raise e
    pass


def downgrade() -> None:
    pass


def generateCustomerJsonPopulateQuery(session):
    return ( 
            session.query(Customer)
            .join(Customer.subscriptions)
            .join(Subscription.subscription_payment_transaction) 
            .filter(SubscriptionPaymentTransaction.customer_json.is_(None))
            .options(joinedload(Customer.account_information, innerjoin=False), joinedload(Customer.country_rel, innerjoin=False))
    )
    
def hasMoreEmptyCustomerJson(session) -> bool: 
    query = generateCustomerJsonPopulateQuery(session)
    # print(str(query.statement.compile(compile_kwargs={"literal_binds": True})).replace('\n', ' '))
    count = query.count()

    return count > 0