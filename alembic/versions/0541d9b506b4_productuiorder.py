"""ProductUIOrder

Revision ID: 0541d9b506b4
Revises: a1259371eeac
Create Date: 2024-09-19 05:11:26.784433

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '0541d9b506b4'
down_revision: Union[str, None] = 'a1259371eeac'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('product', sa.Column('ui_order', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('product', 'ui_order')
    # ### end Alembic commands ###
