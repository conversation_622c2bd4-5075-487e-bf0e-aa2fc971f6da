"""update customer table

Revision ID: af8d313f10b8
Revises: d300b5463b93
Create Date: 2024-08-01 09:28:56.879520

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'af8d313f10b8'
down_revision: Union[str, None] = 'd300b5463b93'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('customer', sa.Column('email', sa.String(length=255), nullable=True))
    op.add_column('customer', sa.Column('title', sa.Enum('MR', 'MD', name='titleenum'), nullable=True))
    op.add_column('customer', sa.Column('street_address', sa.String(length=255), nullable=True))
    op.add_column('customer', sa.Column('district_postal_code', sa.String(length=255), nullable=True))
    op.add_column('customer', sa.Column('city', sa.String(length=255), nullable=True))
    op.add_column('customer', sa.Column('country', sa.String(length=255), nullable=True))
    op.add_column('customer', sa.Column('pseudo', sa.String(length=255), nullable=True))
    op.add_column('customer', sa.Column('mobile_number', sa.String(length=255), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('customer', 'mobile_number')
    op.drop_column('customer', 'pseudo')
    op.drop_column('customer', 'country')
    op.drop_column('customer', 'city')
    op.drop_column('customer', 'district_postal_code')
    op.drop_column('customer', 'street_address')
    op.drop_column('customer', 'title')
    op.drop_column('customer', 'email')
    # ### end Alembic commands ###
