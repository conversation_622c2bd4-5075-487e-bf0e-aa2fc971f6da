"""remove rev sim for premium

Revision ID: 10f068080232
Revises: 4db26c6db39d
Create Date: 2025-05-23 06:42:36.746629

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '10f068080232'
down_revision: Union[str, None] = '4db26c6db39d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    conn = op.get_bind()
    conn.execute(sa.text("""
        UPDATE product_features
        SET allow = 0
        WHERE product_id IN (2, 8) AND features_id = 4
    """))
    conn.execute(sa.text("""
        UPDATE product_features
        SET allow = 0
        WHERE product_id IN (2, 8) AND features_id = 5
    """))
    conn.execute(sa.text("""
        UPDATE product_features
        SET allow = 0
        WHERE product_id IN (2, 8) AND features_id = 6
    """))
    # conn.execute(sa.text("""
    #     UPDATE product
    #     SET monthly_credit = 100000
    #     WHERE id IN (2, 8)
    # """))

def downgrade() -> None:
    conn = op.get_bind()
    conn.execute(sa.text("""
        UPDATE product_features
        SET allow = 1
        WHERE product_id IN (2, 8) AND features_id = 4
    """))
    conn.execute(sa.text("""
        UPDATE product_features
        SET allow = 1
        WHERE product_id IN (2, 8) AND features_id = 5
    """))
    conn.execute(sa.text("""
        UPDATE product_features
        SET allow = 1
        WHERE product_id IN (2, 8) AND features_id = 6
    """))
    # conn.execute(sa.text("""
    #     UPDATE product
    #     SET monthly_credit = 10
    #     WHERE id IN (2, 8)
    # """))
