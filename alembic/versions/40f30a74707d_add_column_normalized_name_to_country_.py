"""Add column normalized_name  to Country  model

Revision ID: 40f30a74707d
Revises: 20378b7411ce
Create Date: 2025-02-18 09:53:09.305809

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '40f30a74707d'
down_revision: Union[str, None] = '20378b7411ce'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('country', sa.Column('normalized_name', sa.String(length=255), nullable=True))
    op.create_index(op.f('ix_country_normalized_name'), 'country', ['normalized_name'], unique=False)
    # ### end Alembic commands ###
    op.execute("""
        UPDATE country
        SET normalized_name = LOWER(REPLACE(name, ' ', '-'))
    """)

def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_country_normalized_name'), table_name='country')
    op.drop_column('country', 'normalized_name')
    # ### end Alembic commands ###
