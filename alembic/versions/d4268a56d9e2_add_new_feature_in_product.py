"""add new feature in product

Revision ID: d4268a56d9e2
Revises: 727cc33b9257
Create Date: 2025-05-28 10:29:41.854100

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'd4268a56d9e2'
down_revision: Union[str, None] = '727cc33b9257'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    product_features = sa.table(
        'product_features',
        sa.column('product_id', sa.Integer()),
        sa.column('features_id', sa.Integer()),
        sa.column('allow', sa.Integer())
    )

    op.bulk_insert(
        product_features,
        [
            {'product_id': 11, 'features_id': 8, 'allow': 1},
            {'product_id': 12, 'features_id': 8, 'allow': 1},
            {'product_id': 13, 'features_id': 8, 'allow': 1},
            {'product_id': 20, 'features_id': 8, 'allow': 1},
            {'product_id': 21, 'features_id': 8, 'allow': 1},
            {'product_id': 22, 'features_id': 8, 'allow': 1}
        ]
    )


def downgrade() -> None:
    conn = op.get_bind()
    conn.execute(sa.text("""
        DELETE FROM product_features
        WHERE features_id = 8 AND product_id IN (11, 12, 13, 20, 21, 22)
    """))
