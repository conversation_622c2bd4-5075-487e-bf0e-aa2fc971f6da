"""add is_auto_renew column

Revision ID: c1a1ed4d9a1e
Revises: 236cf81dbdb2
Create Date: 2024-08-19 09:26:58.410012

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c1a1ed4d9a1e'
down_revision: Union[str, None] = '236cf81dbdb2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('subscription', sa.Column('is_auto_renew', sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('subscription', 'is_auto_renew')
    # ### end Alembic commands ###
