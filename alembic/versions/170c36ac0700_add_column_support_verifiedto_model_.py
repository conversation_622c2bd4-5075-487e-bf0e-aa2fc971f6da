"""add column support verifiedto model account information

Revision ID: 170c36ac0700
Revises: 212db8470c87
Create Date: 2025-04-28 06:10:35.750682

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '170c36ac0700'
down_revision: Union[str, None] = '212db8470c87'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('account_information', sa.Column('support_verified', sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('account_information', 'support_verified')
    # ### end Alembic commands ###
