"""add column descripction tralsation key to model product_alt_pricing 

Revision ID: 7e0ed8ad62a5
Revises: e8f108210d83
Create Date: 2025-02-18 05:13:26.054956

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '7e0ed8ad62a5'
down_revision: Union[str, None] = 'e8f108210d83'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('product_alt_pricing', sa.Column('description_translation_key', sa.String(length=255), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('product_alt_pricing', 'description_translation_key')
    # ### end Alembic commands ###
