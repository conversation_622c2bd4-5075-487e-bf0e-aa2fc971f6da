"""Add column simulationType

Revision ID: d4a6588ed11f
Revises: cab246d36604
Create Date: 2024-09-11 08:34:30.839758

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'd4a6588ed11f'
down_revision: Union[str, None] = 'cab246d36604'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('simulation_item', sa.Column('simulation_type', sa.String(length=255), nullable=True))
    op.alter_column('subscription_payment_transaction', 'amount_paid',
               existing_type=mysql.DOUBLE(asdecimal=True),
               type_=sa.Float(precision=53),
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('subscription_payment_transaction', 'amount_paid',
               existing_type=sa.Float(precision=53),
               type_=mysql.DOUBLE(asdecimal=True),
               existing_nullable=True)
    op.drop_column('simulation_item', 'simulation_type')
    # ### end Alembic commands ###
