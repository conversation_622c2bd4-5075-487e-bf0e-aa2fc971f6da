"""new contact type

Revision ID: 960eb275d0a9
Revises: 825133805295
Create Date: 2024-11-10 10:28:58.138986

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '960eb275d0a9'
down_revision: Union[str, None] = '825133805295'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "contact_platform",
        "type",
        type_=sa.Enum(
            'SOCIAL', 'MESSENGER', 'NETWORK',
            name="contacttypeenum",
        ),
        nullable=True,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
