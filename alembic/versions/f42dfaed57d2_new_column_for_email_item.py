"""new column for email item

Revision ID: f42dfaed57d2
Revises: c0a4e38e58bc
Create Date: 2025-08-22 07:24:22.102530

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'f42dfaed57d2'
down_revision: Union[str, None] = 'c0a4e38e58bc'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None
def upgrade() -> None:
    op.add_column('customer_email_item', sa.Column('current_task', sa.Integer(), nullable=True))
    op.add_column('customer_email_item', sa.Column('campaign_number_brevo', sa.Integer(), nullable=True))
    
def downgrade() -> None:
    op.drop_column('customer_email_item', 'campaign_number_brevo')
    op.drop_column('customer_email_item', 'current_task')