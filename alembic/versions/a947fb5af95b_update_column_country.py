"""update column country

Revision ID: a947fb5af95b
Revises: 439b3bdaf2d5
Create Date: 2024-12-06 08:13:03.326408

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'a947fb5af95b'
down_revision: Union[str, None] = '439b3bdaf2d5'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('country', sa.Column('code_alpha_2', sa.String(length=10), nullable=True))
    op.add_column('country', sa.Column('code_alpha_3', sa.String(length=10), nullable=True))
    op.drop_index('codeAlpha2', table_name='country')
    op.drop_index('codeAlpha3', table_name='country')
    op.create_unique_constraint(None, 'country', ['code_alpha_2'])
    op.create_unique_constraint(None, 'country', ['code_alpha_3'])
    op.drop_column('country', 'codeAlpha2')
    op.drop_column('country', 'codeAlpha3')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('country', sa.Column('codeAlpha3', mysql.VARCHAR(collation='utf8mb4_general_ci', length=10), nullable=True))
    op.add_column('country', sa.Column('codeAlpha2', mysql.VARCHAR(collation='utf8mb4_general_ci', length=10), nullable=True))
    op.drop_constraint(None, 'country', type_='unique')
    op.drop_constraint(None, 'country', type_='unique')
    op.create_index('codeAlpha3', 'country', ['codeAlpha3'], unique=True)
    op.create_index('codeAlpha2', 'country', ['codeAlpha2'], unique=True)
    op.drop_column('country', 'code_alpha_3')
    op.drop_column('country', 'code_alpha_2')
    # ### end Alembic commands ###
