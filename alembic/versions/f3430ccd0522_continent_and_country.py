"""continent and country

Revision ID: f3430ccd0522
Revises: 09245970bc1d
Create Date: 2025-02-26 09:18:46.980383

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f3430ccd0522'
down_revision: Union[str, None] = '09245970bc1d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('country', sa.Column('continent_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'country', 'continent', ['continent_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'country', type_='foreignkey')
    op.drop_column('country', 'continent_id')
    # ### end Alembic commands ###
