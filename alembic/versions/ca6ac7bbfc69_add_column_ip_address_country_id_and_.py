"""Add column ip_address, country_id and language  to Cart model

Revision ID: ca6ac7bbfc69
Revises: 498b5b36e81a
Create Date: 2025-01-27 08:49:25.519540

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ca6ac7bbfc69'
down_revision: Union[str, None] = '498b5b36e81a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('cart', sa.Column('country_id', sa.Integer(), nullable=True))
    op.add_column('cart', sa.Column('language_iso_2', sa.String(length=50), nullable=True))
    op.add_column('cart', sa.Column('ip_address', sa.String(length=255), nullable=True))
    op.create_foreign_key(None, 'cart', 'country', ['country_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'cart', type_='foreignkey')
    op.drop_column('cart', 'ip_address')
    op.drop_column('cart', 'language_iso_2')
    op.drop_column('cart', 'country_id')
    # ### end Alembic commands ###
