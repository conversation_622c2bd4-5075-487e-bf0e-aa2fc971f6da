"""manage receipt

Revision ID: 31b935930577
Revises: 15756f1a9043
Create Date: 2025-05-02 06:49:21.426510

"""
from typing import Sequence, Union
from sqlalchemy.orm import sessionmaker
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from app.crud import crud_subscription_payment_receipt, crud_subscription_payment_transaction

# revision identifiers, used by Alembic.
revision: str = '31b935930577'
down_revision: Union[str, None] = '15756f1a9043'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    ### commands auto generated by Alembic - please adjust! ###
    op.create_table('subscription_payment_receipt',
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.Column('last_user_to_interact', sa.Integer(), nullable=True),
    sa.Column('import_id', sa.Text(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('reference', sa.String(length=255), nullable=True),
    sa.Column('stripe_object_id', sa.String(length=255), nullable=True),
    sa.Column('subscription_stripe_id', sa.String(length=255), nullable=True),
    sa.Column('subscription_payment_transaction_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['subscription_payment_transaction_id'], ['subscription_payment_transaction.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_subscription_payment_receipt_last_user_to_interact'), 'subscription_payment_receipt', ['last_user_to_interact'], unique=False)
    
    
    
    op.add_column('subscription_payment_transaction', sa.Column('stripe_object_id', sa.String(length=255), nullable=True))
    op.add_column('subscription_payment_transaction', 
                 sa.Column('stripe_terminal_status', mysql.VARCHAR(length=255), nullable=True))
    # Fill the stripe_object_id column with the id from payment_transaction_json
    session = None 
    try: 
        bind = op.get_bind()
        session = sessionmaker(bind=bind)() 
        crud_subscription_payment_transaction.subscription_payment_transaction.fill_stripe_ob_id(session)
        crud_subscription_payment_receipt.subscription_payment_receipt.fill_legacy_transactions(session)
        session.commit()
    except Exception as e:
        if session:
            session.rollback()
        raise e
    finally:
        if session:
            session.close()
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('subscription_payment_transaction', 'stripe_object_id')
    op.drop_index(op.f('ix_subscription_payment_receipt_last_user_to_interact'), table_name='subscription_payment_receipt')
    op.drop_table('subscription_payment_receipt')
    # ### end Alembic commands ###
