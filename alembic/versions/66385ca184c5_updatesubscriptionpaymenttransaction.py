"""UpdateSubscriptionPaymentTransaction

Revision ID: 66385ca184c5
Revises: 7572f0ee7145
Create Date: 2024-08-12 10:23:26.137092

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '66385ca184c5'
down_revision: Union[str, None] = '7572f0ee7145'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('subscription_payment_transaction', sa.Column('payment_gateway_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'subscription_payment_transaction', 'payment_gateway', ['payment_gateway_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'subscription_payment_transaction', type_='foreignkey')
    op.drop_column('subscription_payment_transaction', 'payment_gateway_id')
    # ### end Alembic commands ###
