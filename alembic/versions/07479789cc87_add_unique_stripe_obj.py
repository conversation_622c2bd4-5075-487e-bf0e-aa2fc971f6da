"""add unique stripe obj

Revision ID: 07479789cc87
Revises: 00113156e075
Create Date: 2025-05-06 08:53:35.369725

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '07479789cc87'
down_revision: Union[str, None] = '00113156e075'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    
    op.create_unique_constraint(None, 'subscription_payment_receipt', ['stripe_object_id'])
    op.create_unique_constraint(None, 'subscription_payment_transaction', ['stripe_object_id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'subscription_payment_transaction', type_='unique')
    op.drop_constraint(None, 'subscription_payment_receipt', type_='unique')
    
    # ### end Alembic commands ###
