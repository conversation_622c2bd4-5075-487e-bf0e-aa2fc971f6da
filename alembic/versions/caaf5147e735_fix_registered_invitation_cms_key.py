"""fix registered invitation cms key

Revision ID: caaf5147e735
Revises: ce36186a513b
Create Date: 2025-04-08 09:10:04.285319

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'caaf5147e735'
down_revision: Union[str, None] = 'ce36186a513b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    
    op.execute("UPDATE customer_email_task SET cms_key = 'mail-registered-user-invitation' WHERE `key` = 'registered_invitation'")


def downgrade() -> None:
    pass
