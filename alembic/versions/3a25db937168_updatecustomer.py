"""updateCustomer

Revision ID: 3a25db937168
Revises: 1a837fe475b2
Create Date: 2024-10-25 06:51:48.767706

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '3a25db937168'
down_revision: Union[str, None] = '1a837fe475b2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('subscription', sa.Column('trial_end', sa.DateTime(), nullable=True))
    
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('subscription', 'trial_end')
    # ### end Alembic commands ###
