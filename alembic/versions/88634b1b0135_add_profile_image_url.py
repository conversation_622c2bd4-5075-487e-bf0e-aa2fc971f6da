"""add profile_image_url

Revision ID: 88634b1b0135
Revises: e750dd48c1ec
Create Date: 2024-09-03 08:41:33.894298

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '88634b1b0135'
down_revision: Union[str, None] = 'e750dd48c1ec'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('customer', sa.Column('profile_image_url', sa.String(length=255), nullable=True))
    op.create_unique_constraint(None, 'customer', ['pseudo'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'customer', type_='unique')
    op.drop_column('customer', 'profile_image_url')
    # ### end Alembic commands ###
