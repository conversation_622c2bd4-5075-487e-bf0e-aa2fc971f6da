"""rename table user to customer

Revision ID: 7226941a3b93
Revises: e809f1cc862c
Create Date: 2024-07-31 05:13:38.653398

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = "7226941a3b93"
down_revision: Union[str, None] = "e809f1cc862c"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("ix_user_auth_user_id", table_name="user")
    op.drop_index("ix_user_last_user_to_interact", table_name="user")
    op.drop_constraint(
        "subscription_user_ibfk_2", "subscription_user", type_="foreignkey"
    )
    op.drop_column("subscription_user", "user_id")
    op.drop_table("user")

    op.create_table(
        "customer",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("first_name", sa.String(length=255), nullable=True),
        sa.Column("last_name", sa.String(length=255), nullable=True),
        sa.Column("auth_user_id", sa.Integer(), nullable=True),
        sa.Column("created_by", sa.Integer(), nullable=True),
        sa.Column(
            "created_at", sa.DateTime(), server_default=sa.text("now()"), nullable=True
        ),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.Column("deleted_at", sa.DateTime(), nullable=True),
        sa.Column("last_user_to_interact", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["created_by"],
            ["customer.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_customer_auth_user_id"), "customer", ["auth_user_id"], unique=False
    )
    op.create_index(
        op.f("ix_customer_last_user_to_interact"),
        "customer",
        ["last_user_to_interact"],
        unique=False,
    )
    op.add_column(
        "subscription_user", sa.Column("customer_id", sa.Integer(), nullable=True)
    )
    op.create_foreign_key(
        None, "subscription_user", "customer", ["customer_id"], ["id"]
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "subscription_user",
        sa.Column("user_id", mysql.INTEGER(), autoincrement=False, nullable=True),
    )
    op.drop_constraint(None, "subscription_user", type_="foreignkey")
    op.create_foreign_key(
        "subscription_user_ibfk_2", "subscription_user", "user", ["user_id"], ["id"]
    )
    op.drop_column("subscription_user", "customer_id")
    op.create_table(
        "user",
        sa.Column("id", mysql.INTEGER(), autoincrement=True, nullable=False),
        sa.Column("first_name", mysql.VARCHAR(length=255), nullable=True),
        sa.Column("last_name", mysql.VARCHAR(length=255), nullable=True),
        sa.Column("auth_user_id", mysql.INTEGER(), autoincrement=False, nullable=True),
        sa.Column("created_by", mysql.INTEGER(), autoincrement=False, nullable=True),
        sa.Column(
            "created_at",
            mysql.DATETIME(),
            server_default=sa.text("CURRENT_TIMESTAMP"),
            nullable=True,
        ),
        sa.Column("updated_at", mysql.DATETIME(), nullable=True),
        sa.Column("deleted_at", mysql.DATETIME(), nullable=True),
        sa.Column(
            "last_user_to_interact", mysql.INTEGER(), autoincrement=False, nullable=True
        ),
        sa.ForeignKeyConstraint(["created_by"], ["user.id"], name="user_ibfk_1"),
        sa.PrimaryKeyConstraint("id"),
        mysql_collate="utf8mb4_0900_ai_ci",
        mysql_default_charset="utf8mb4",
        mysql_engine="InnoDB",
    )
    op.create_index(
        "ix_user_last_user_to_interact", "user", ["last_user_to_interact"], unique=False
    )
    op.create_index("ix_user_auth_user_id", "user", ["auth_user_id"], unique=False)
    op.drop_index(op.f("ix_customer_last_user_to_interact"), table_name="customer")
    op.drop_index(op.f("ix_customer_auth_user_id"), table_name="customer")
    op.drop_table("customer")
    # ### end Alembic commands ###
