"""UpdateAccountInfo

Revision ID: e8dc58c56548
Revises: 136687cd1979
Create Date: 2024-08-06 08:53:13.607841

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'e8dc58c56548'
down_revision: Union[str, None] = '136687cd1979'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('account_information', sa.Column('company_name', sa.String(length=255), nullable=True))
    op.alter_column('account_information', 'siret_number',
               existing_type=mysql.INTEGER(display_width=11),
               type_=sa.String(length=25),
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('account_information', 'siret_number',
               existing_type=sa.String(length=25),
               type_=mysql.INTEGER(display_width=11),
               existing_nullable=True)
    op.drop_column('account_information', 'company_name')
    # ### end Alembic commands ###
