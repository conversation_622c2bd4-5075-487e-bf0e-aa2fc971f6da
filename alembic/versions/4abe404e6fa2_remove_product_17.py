"""remove product 17

Revision ID: 4abe404e6fa2
Revises: 4a050c896872
Create Date: 2025-06-03 11:57:23.557312

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '4abe404e6fa2'
down_revision: Union[str, None] = '4a050c896872'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    conn = op.get_bind()

    result = conn.execute(sa.text("""
        SELECT 1 FROM product WHERE id = :product_id
    """), {'product_id': 17}).fetchone()

    if result:
        conn.execute(sa.text("""
            DELETE FROM product_features
            WHERE product_id = :product_id
        """), {'product_id': 17})
        
        conn.execute(sa.text("""
            DELETE FROM product
            WHERE id = :product_id
        """), {'product_id': 17})


def downgrade() -> None:
    pass
