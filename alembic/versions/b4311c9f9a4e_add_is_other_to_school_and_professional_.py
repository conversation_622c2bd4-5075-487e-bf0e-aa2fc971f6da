"""Add is other to school and professional category

Revision ID: b4311c9f9a4e
Revises: e8dc58c56548
Create Date: 2024-08-06 10:54:00.678352

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'b4311c9f9a4e'
down_revision: Union[str, None] = 'e8dc58c56548'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('professional_category', sa.Column('is_other', sa.<PERSON><PERSON>(), nullable=True))
    op.add_column('school_category', sa.Column('is_other', sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('school_category', 'is_other')
    op.drop_column('professional_category', 'is_other')
    # ### end Alembic commands ###
