"""add once billing interval

Revision ID: 683af19b4e6b
Revises: c8381d8d4e6f
Create Date: 2025-03-26 08:09:04.870673

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '683af19b4e6b'
down_revision: Union[str, None] = 'c8381d8d4e6f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('product', 'billing_period_interval',
        existing_type=sa.Enum('year','month', name='billingperiodintervalenum'),
        type_=sa.Enum('year','month','once', name='billingperiodintervalenum'),
        existing_nullable=False
    )
    
    op.execute("UPDATE product set billing_period_interval = 'once' where id = 7")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # ### end Alembic commands ###
    pass
   
