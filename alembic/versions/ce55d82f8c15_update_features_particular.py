"""update features particular

Revision ID: ce55d82f8c15
Revises: ec799233a9d2
Create Date: 2025-06-02 10:19:03.307493

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ce55d82f8c15'
down_revision: Union[str, None] = 'ec799233a9d2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    conn = op.get_bind()
    conn.execute(sa.text("""
        UPDATE product_features
        SET allow = 1
        WHERE id IN (139, 140, 141, 142, 143, 144)
    """))


def downgrade() -> None:
    conn = op.get_bind()
    conn.execute(sa.text("""
        UPDATE product_features
        SET allow = 0
        WHERE id IN (139, 140, 141, 142, 143, 144)
    """))
