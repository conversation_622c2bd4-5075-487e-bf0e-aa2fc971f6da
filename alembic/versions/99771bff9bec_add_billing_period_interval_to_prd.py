"""add billing period interval to prd

Revision ID: 99771bff9bec
Revises: 40731b210907
Create Date: 2025-02-19 09:55:31.791913

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '99771bff9bec'
down_revision: Union[str, None] = '40731b210907'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_product_alt_pricing_last_user_to_interact', table_name='product_alt_pricing')
    op.drop_constraint('cart_ibfk_3', 'cart', type_='foreignkey')
    op.drop_column('cart', 'product_alt_pricing_id')
    op.drop_constraint('subscription_ibfk_3', 'subscription', type_='foreignkey')
    op.drop_column('subscription', 'product_alt_pricing_id')
    op.drop_table('product_alt_pricing')
    
    op.add_column('product', sa.Column('billing_period_interval', sa.Enum('year', 'month', name='billingperiodintervalenum'), nullable=True))
    op.execute("UPDATE product set billing_period_interval = 'month' where billing_period_interval is null")
    op.execute("UPDATE product SET description_note_translation_key = 'pvgis.product_pricing.expert' "
               "WHERE description_note_translation_key IS NULL AND TRIM(name) LIKE '%PVGIS24 EXPERT%'")

    op.execute("UPDATE product SET description_note_translation_key = 'pvgis.product_pricing.premium' "
               "WHERE description_note_translation_key IS NULL AND TRIM(name) LIKE '%PVGIS24 PREMIUM%'")

    op.execute("UPDATE product SET description_note_translation_key = 'pvgis.product_pricing.pro' "
               "WHERE description_note_translation_key IS NULL AND TRIM(name) LIKE '%PVGIS24 PRO%'")
    # ### end Alembic commands ###


def downgrade() -> None:
    pass
    # ### commands auto generated by Alembic - please adjust! ###
    # ### end Alembic commands ###
