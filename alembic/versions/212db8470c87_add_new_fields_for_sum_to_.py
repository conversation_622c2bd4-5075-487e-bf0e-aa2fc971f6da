"""Add new fields for sum   to dailymonotonous  model

Revision ID: 212db8470c87
Revises: 885f95864a9e
Create Date: 2025-04-24 10:38:26.287447

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '212db8470c87'
down_revision: Union[str, None] = '885f95864a9e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade():
    op.add_column('monotonous_electricity_consumption_daily', sa.Column('residential_consumption_sum', sa.JSON(), nullable=True))
    op.add_column('monotonous_electricity_consumption_daily', sa.Column('residential_consumption_percentage_json', sa.JSON(), nullable=True))

def downgrade():
    op.drop_column('monotonous_electricity_consumption_daily', 'residential_consumption_sum')
    op.drop_column('monotonous_electricity_consumption_daily', 'residential_consumption_percentage_json')