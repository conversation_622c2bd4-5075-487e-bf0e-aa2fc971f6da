"""Add column use_avg_residential_consumption  to dailymonotonous  model

Revision ID: 885f95864a9e
Revises: 4d48aef98eda
Create Date: 2025-04-24 10:00:55.340260

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '885f95864a9e'
down_revision: Union[str, None] = '4d48aef98eda'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column(
        'monotonous_electricity_consumption_daily',
        sa.Column('use_avg_residential_consumption', sa.<PERSON>(), nullable=True)
    )


def downgrade() -> None:
    op.drop_column('monotonous_electricity_consumption_daily', 'use_avg_residential_consumption')