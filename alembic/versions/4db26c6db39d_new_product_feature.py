"""new product feature

Revision ID: 4db26c6db39d
Revises: f89758d724e9
Create Date: 2025-05-23 06:21:46.220014

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.sql import text


# revision identifiers, used by Alembic.
revision: str = '4db26c6db39d'
down_revision: Union[str, None] = 'f89758d724e9'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    product_features = sa.table(
        'product_features',
        sa.column('product_id', sa.Integer()),
        sa.column('features_id', sa.Integer()),
        sa.column('allow', sa.Integer())
    )

    op.bulk_insert(
        product_features,
        [
            {'product_id': 1, 'features_id': 13, 'allow': 1},
            {'product_id': 2, 'features_id': 13, 'allow': 1},
            {'product_id': 3, 'features_id': 13, 'allow': 1},
            {'product_id': 4, 'features_id': 13, 'allow': 1},
            {'product_id': 8, 'features_id': 13, 'allow': 1},
            {'product_id': 9, 'features_id': 13, 'allow': 1},
            {'product_id': 10, 'features_id': 13, 'allow': 1},
        ]
    )


def downgrade() -> None:
    conn = op.get_bind()
    conn.execute(sa.text("""
        DELETE FROM product_features
        WHERE features_id = 13 AND product_id IN (1, 2, 3, 4, 8, 9, 10)
    """))
