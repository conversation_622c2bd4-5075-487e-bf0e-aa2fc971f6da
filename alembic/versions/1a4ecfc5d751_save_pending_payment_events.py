"""save_pending_payment_events

Revision ID: 1a4ecfc5d751
Revises: e81acd99f18b
Create Date: 2025-02-25 07:23:28.887919

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '1a4ecfc5d751'
down_revision: Union[str, None] = 'e81acd99f18b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('stripe_temp_payment_success',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.Column('last_user_to_interact', sa.Integer(), nullable=True),
    sa.Column('import_id', sa.Text(), nullable=True),
    sa.Column('subscription_stripe_id', sa.String(length=500), nullable=True),
    sa.Column('event_ob', sa.JSON(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_stripe_temp_payment_success_last_user_to_interact'), 'stripe_temp_payment_success', ['last_user_to_interact'], unique=False)
   
    op.add_column('subscription', sa.Column('subscription_stripe_id', sa.String(length=255), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('subscription', 'subscription_stripe_id')
    op.drop_index(op.f('ix_stripe_temp_payment_success_last_user_to_interact'), table_name='stripe_temp_payment_success')
    op.drop_table('stripe_temp_payment_success')
    # ### end Alembic commands ###
