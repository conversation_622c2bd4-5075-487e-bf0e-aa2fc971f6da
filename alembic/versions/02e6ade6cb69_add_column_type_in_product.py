"""Add column type in product

Revision ID: 02e6ade6cb69
Revises: f31dcbf72359
Create Date: 2025-05-12 11:46:21.147442

"""
from alembic import op
import sqlalchemy as sa
from typing import Sequence, Union

# revision identifiers, used by Alembic.
revision: str = '02e6ade6cb69'
down_revision: Union[str, None] = 'f31dcbf72359'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column('product', sa.Column('type', sa.Integer(), nullable=True))


def downgrade() -> None:
    op.drop_column('product', 'type')
