"""Add column siret_number to the account_information table

Revision ID: e30f1a1cb66f
Revises: e1a70365f651
Create Date: 2024-08-05 06:27:52.982752

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'e30f1a1cb66f'
down_revision: Union[str, None] = 'e1a70365f651'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('account_information', sa.Column('siret_number', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('account_information', 'siret_number')
    # ### end Alembic commands ###
