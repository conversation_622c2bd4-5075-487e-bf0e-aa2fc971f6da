"""updateSubscription

Revision ID: d422f456f914
Revises: 3a25db937168
Create Date: 2024-10-25 07:18:18.056717

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'd422f456f914'
down_revision: Union[str, None] = '3a25db937168'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('subscription', sa.Column('coupon_end', sa.DateTime(), nullable=True))
    op.drop_column('subscription', 'trial_end')
   
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('subscription', sa.Column('trial_end', mysql.DATETIME(), nullable=True))
    op.drop_column('subscription', 'coupon_end')
    # ### end Alembic commands ###
