"""removeCartItem

Revision ID: 48ff38f7ae8d
Revises: ad447458bedf
Create Date: 2024-08-01 07:06:24.850943

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '48ff38f7ae8d'
down_revision: Union[str, None] = 'ad447458bedf'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_cart_item_last_user_to_interact', table_name='cart_item')
    op.drop_table('cart_item')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('cart_item',
    sa.Column('created_at', mysql.DATETIME(), server_default=sa.text('current_timestamp()'), nullable=True),
    sa.Column('updated_at', mysql.DATETIME(), nullable=True),
    sa.Column('deleted_at', mysql.DATETIME(), nullable=True),
    sa.Column('last_user_to_interact', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True),
    sa.Column('id', mysql.INTEGER(display_width=11), autoincrement=True, nullable=False),
    sa.Column('cart_id', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True),
    sa.Column('product_id', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True),
    sa.Column('quantity', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['cart_id'], ['cart.id'], name='cart_item_ibfk_1'),
    sa.ForeignKeyConstraint(['product_id'], ['product.id'], name='cart_item_ibfk_2'),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_general_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.create_index('ix_cart_item_last_user_to_interact', 'cart_item', ['last_user_to_interact'], unique=False)
    # ### end Alembic commands ###
