"""Add column remove_from_mailing_list in customer

Revision ID: f1f2ae2cc0d6
Revises: ca6ac7bbfc69
Create Date: 2025-02-03 06:42:13.235948

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f1f2ae2cc0d6'
down_revision: Union[str, None] = 'ca6ac7bbfc69'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('customer', sa.Column('remove_from_mailing_list', sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('customer', 'remove_from_mailing_list')
    # ### end Alembic commands ###
