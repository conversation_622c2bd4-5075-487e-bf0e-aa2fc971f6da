"""add_campaign_table

Revision ID: 433044cd334b
Revises: aa5caa49cbbb
Create Date: 2025-03-06 08:03:09.003179

"""
from typing import Sequence, Union
from sqlalchemy.orm import sessionmaker
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from app.models.customer_email_campaign import CustomerEmailCampaign 
from app.models.customer_email_task import CustomerEmailTask

# revision identifiers, used by Alembic.
revision: str = '433044cd334b'
down_revision: Union[str, None] = 'aa5caa49cbbb'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

default_campaigns = [
    {"id": 1, "name": "Marketing", "key": "marketing"},
    {"id": 2, "name": "Installer invitation", "key": "installer_invitation"}
]
def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('customer_email_campaign',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.Column('last_user_to_interact', sa.Integer(), nullable=True),
    sa.Column('import_id', sa.Text(), nullable=True),
    sa.Column('name', sa.String(length=255), nullable=True),
    sa.Column('key', sa.String(length=255), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_customer_email_campaign_last_user_to_interact'), 'customer_email_campaign', ['last_user_to_interact'], unique=False)
   
    op.add_column('customer_email_task', sa.Column('customer_email_campaign_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'customer_email_task', 'customer_email_campaign', ['customer_email_campaign_id'], ['id'])
    
    session = None 
    try: 
        bind = op.get_bind()
        session = sessionmaker(bind=bind)() 
        session.add_all([CustomerEmailCampaign(**dc) for dc in default_campaigns])
        session.flush() 
        session.query(CustomerEmailTask).filter(CustomerEmailTask.key.in_([
            "welcome_and_discovery",
            "user_testimonial",
            "highlighting_features",
            "special_offer",
            "last_chance",
        ])).update({"customer_email_campaign_id": 1}, synchronize_session="fetch")
        session.query(CustomerEmailTask).filter(CustomerEmailTask.key.in_([
            "installer_invitation"
        ])).update({"customer_email_campaign_id": 2}, synchronize_session="fetch")
        session.commit()
    except Exception as e:
        if session:
            session.rollback()
        raise e
    finally:
        if session:
            session.close()
        
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'customer_email_task', type_='foreignkey')
    op.drop_column('customer_email_task', 'customer_email_campaign_id') 
    
    op.drop_index(op.f('ix_customer_email_campaign_last_user_to_interact'), table_name='customer_email_campaign')
    op.drop_table('customer_email_campaign')
    # ### end Alembic commands ###
