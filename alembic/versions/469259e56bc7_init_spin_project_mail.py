"""init spin project mail

Revision ID: 469259e56bc7
Revises: cf05784b5338
Create Date: 2025-06-05 05:38:22.784731

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '469259e56bc7'
down_revision: Union[str, None] = 'cf05784b5338'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    customer_email_campaign = sa.table(
        'customer_email_campaign',
        sa.column('id', sa.Integer()),
        sa.column('name', sa.String()),
        sa.column('key', sa.String()),
        sa.column('is_active', sa.Integer())
    )

    op.bulk_insert(
        customer_email_campaign,
        [{
            'id': 6,
            'name': 'Spin : Project',
            'key': 'spin_projet',
            'is_active': 0
        }]
    )
    
    customer_email_task = sa.table(
        'customer_email_task',
        sa.column('id', sa.Integer()),
        sa.column('key', sa.String()),
        sa.column('name', sa.String()),
        sa.column('email_number', sa.Integer()),
        sa.column('cms_key', sa.String()),
        sa.column('is_active', sa.Integer()),
        sa.column('customer_email_campaign_id', sa.Integer())
    )

    op.bulk_insert(
        customer_email_task,
        [
            {'id': 28, 'key': 'pvgis-email-projet-spin-1-j-1', 'name': 'Spin Projet 1', 'email_number': 28, 'cms_key': 'pvgis-email-projet-spin-1-j-1', 'is_active': 1, 'customer_email_campaign_id': 6},
            {'id': 29, 'key': 'pvgis-email-projet-spin-2-j-3', 'name': 'Spin Projet 2', 'email_number': 29, 'cms_key': 'pvgis-email-projet-spin-2-j-3', 'is_active': 1, 'customer_email_campaign_id': 6},
            {'id': 30, 'key': 'pvgis-email-projet-spin-3-j-5', 'name': 'Spin Projet 3', 'email_number': 30, 'cms_key': 'pvgis-email-projet-spin-3-j-5', 'is_active': 1, 'customer_email_campaign_id': 6},
            {'id': 31, 'key': 'pvgis-email-projet-spin-4-j-7', 'name': 'Spin Projet 4', 'email_number': 31, 'cms_key': 'pvgis-email-projet-spin-4-j-7', 'is_active': 1, 'customer_email_campaign_id': 6},
            {'id': 32, 'key': 'pvgis-email-projet-spin-5-j-14', 'name': 'Spin Projet 5', 'email_number': 32, 'cms_key': 'pvgis-email-projet-spin-5-j-14', 'is_active': 1, 'customer_email_campaign_id': 6},
            {'id': 33, 'key': 'pvgis-email-projet-spin-6-j-16', 'name': 'Spin Projet 6', 'email_number': 33, 'cms_key': 'pvgis-email-projet-spin-6-j-16', 'is_active': 1, 'customer_email_campaign_id': 6},
            {'id': 34, 'key': 'pvgis-email-projet-spin-7-j-18', 'name': 'Spin Projet 7', 'email_number': 34, 'cms_key': 'pvgis-email-projet-spin-7-j-18', 'is_active': 1, 'customer_email_campaign_id': 6},
            {'id': 35, 'key': 'pvgis-email-projet-spin-8-j-20', 'name': 'Spin Projet 8', 'email_number': 35, 'cms_key': 'pvgis-email-projet-spin-8-j-20', 'is_active': 1, 'customer_email_campaign_id': 6}
        ]
    )



def downgrade() -> None:
    op.execute(
        sa.text("""
            DELETE FROM customer_email_task
            WHERE customer_email_campaign_id = :campaign_id
        """).bindparams(campaign_id=6)
    )

    op.execute(
        sa.text("""
            DELETE FROM customer_email_campaign
            WHERE id = :campaign_id
        """).bindparams(campaign_id=6)
    )
