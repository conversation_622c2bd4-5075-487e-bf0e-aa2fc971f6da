"""new column feature

Revision ID: d6520abb13cb
Revises: 7c6ab4206360
Create Date: 2025-05-15 09:07:01.817177

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'd6520abb13cb'
down_revision: Union[str, None] = '7c6ab4206360'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column('features', sa.Column('hide', sa.<PERSON>(), nullable=True))

def downgrade() -> None:
    op.drop_column('features', 'hide')
