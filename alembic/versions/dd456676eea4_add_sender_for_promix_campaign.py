"""add sender for promix campaign

Revision ID: dd456676eea4
Revises: 3c4292fabcbf
Create Date: 2025-05-02 05:02:54.846721

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'dd456676eea4'
down_revision: Union[str, None] = '3c4292fabcbf'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    ce_sender_email_campaign_country = sa.table(
        'ce_sender_email_campaign_country',
        sa.column('customer_email_campaign_id', sa.Integer()),
        sa.column('customer_email_sender_id', sa.Integer()),
        sa.column('country_id', sa.Integer())
    )

    op.bulk_insert(
        ce_sender_email_campaign_country,
        [
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 1, 'country_id': 73},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 2, 'country_id': 187},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 3, 'country_id': 105},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 4, 'country_id': 226},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 5, 'country_id': 235},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 6, 'country_id': 115},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 7, 'country_id': 52},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 8, 'country_id': 113},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 7, 'country_id': 225},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 9, 'country_id': 64},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 10, 'country_id': 66},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 11, 'country_id': 237},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 3, 'country_id': 16},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 6, 'country_id': 20},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 12, 'country_id': 217},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 13, 'country_id': 136},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 14, 'country_id': 99},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 9, 'country_id': 2},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 15, 'country_id': 39},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 7, 'country_id': 230},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 7, 'country_id': 144},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 15, 'country_id': 33},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 16, 'country_id': 163},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 17, 'country_id': 172},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 18, 'country_id': 18},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 19, 'country_id': 232},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 20, 'country_id': 3},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 21, 'country_id': 118},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 5, 'country_id': 138},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 19, 'country_id': 119},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 6, 'country_id': 56},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 21, 'country_id': 143},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 19, 'country_id': 117},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 22, 'country_id': 41},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 23, 'country_id': 179},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 16, 'country_id': 174},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 24, 'country_id': 159},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 19, 'country_id': 213},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 7, 'country_id': 241},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 17, 'country_id': 29},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 3, 'country_id': 21},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 5, 'country_id': 146},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 20, 'country_id': 188},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 4, 'country_id': 7},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 25, 'country_id': 82},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 13, 'country_id': 14},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 19, 'country_id': 121},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 19, 'country_id': 151},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 26, 'country_id': 154},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 13, 'country_id': 245},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 19, 'country_id': 128},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 13, 'country_id': 40},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 18, 'country_id': 8},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 27, 'country_id': 231},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 20, 'country_id': 45},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 25, 'country_id': 110},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 28, 'country_id': 70},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 7, 'country_id': 239},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 19, 'country_id': 147},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 29, 'country_id': 182},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 15, 'country_id': 156},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 27, 'country_id': 215},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 30, 'country_id': 75},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 16, 'country_id': 96},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 27, 'country_id': 208},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 7, 'country_id': 169},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 28, 'country_id': 127},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 31, 'country_id': 104},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 20, 'country_id': 209},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 17, 'country_id': 15},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 20, 'country_id': 22},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 29, 'country_id': 67},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 16, 'country_id': 109},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 32, 'country_id': 220},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 20, 'country_id': 74},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 19, 'country_id': 102},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 1, 'country_id': 59},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 18, 'country_id': 249},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 33, 'country_id': 24},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 34, 'country_id': 35},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 34, 'country_id': 17},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 13, 'country_id': 214},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 19, 'country_id': 114},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 33, 'country_id': 153},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 10, 'country_id': 185},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 13, 'country_id': 126},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 13, 'country_id': 207},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 35, 'country_id': 199},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 36, 'country_id': 28},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 37, 'country_id': 30},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 38, 'country_id': 88},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 10, 'country_id': 238},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 13, 'country_id': 196},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 22, 'country_id': 203},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 21, 'country_id': 135},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 22, 'country_id': 149},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 37, 'country_id': 38},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 16, 'country_id': 197},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 39, 'country_id': 10},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 40, 'country_id': 131},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 41, 'country_id': 134},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 22, 'country_id': 236},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 10, 'country_id': 11},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 42, 'country_id': 152},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 43, 'country_id': 171},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 44, 'country_id': 50},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 11, 'country_id': 166},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 20, 'country_id': 177},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 4, 'country_id': 106},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 45, 'country_id': 12},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 27, 'country_id': 1},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 29, 'country_id': 53},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 15, 'country_id': 168},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 37, 'country_id': 79},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 7, 'country_id': 85},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 6, 'country_id': 58},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 46, 'country_id': 158},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 16, 'country_id': 55},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 19, 'country_id': 65},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 20, 'country_id': 201},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 1, 'country_id': 78},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 42, 'country_id': 137},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 16, 'country_id': 243},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 39, 'country_id': 26},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 41, 'country_id': 34},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 14, 'country_id': 222},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 7, 'country_id': 242},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 16, 'country_id': 155},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 47, 'country_id': 107},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 7, 'country_id': 181},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 48, 'country_id': 244},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 27, 'country_id': 229},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 19, 'country_id': 31},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 49, 'country_id': 167},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 16, 'country_id': 48},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 36, 'country_id': 125},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 16, 'country_id': 192},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 19, 'country_id': 129},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 50, 'country_id': 233},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 51, 'country_id': 77},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 20, 'country_id': 86},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 52, 'country_id': 204},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 53, 'country_id': 184},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 18, 'country_id': 111},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 15, 'country_id': 116},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 54, 'country_id': 61},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 55, 'country_id': 94},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 15, 'country_id': 4},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 19, 'country_id': 140},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 51, 'country_id': 176},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 56, 'country_id': 190},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 19, 'country_id': 123},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 57, 'country_id': 98},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 58, 'country_id': 23},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 7, 'country_id': 37},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 55, 'country_id': 27},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 6, 'country_id': 112},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 59, 'country_id': 247},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 7, 'country_id': 161},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 60, 'country_id': 120},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 60, 'country_id': 162},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 56, 'country_id': 51},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 27, 'country_id': 100},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 29, 'country_id': 83},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 15, 'country_id': 248},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 19, 'country_id': 145},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 7, 'country_id': 89},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 27, 'country_id': 93},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 16, 'country_id': 69},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 27, 'country_id': 198},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 7, 'country_id': 142},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 19, 'country_id': 189},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 7, 'country_id': 240},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 20, 'country_id': 44},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 20, 'country_id': 202},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 19, 'country_id': 122},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 16, 'country_id': 157},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 20, 'country_id': 218},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 7, 'country_id': 130},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 7, 'country_id': 97},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 29, 'country_id': 84},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 61, 'country_id': 234},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 57, 'country_id': 101},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 49, 'country_id': 148},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 60, 'country_id': 90},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 56, 'country_id': 71},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 62, 'country_id': 193},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 20, 'country_id': 95},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 62, 'country_id': 80},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 56, 'country_id': 60},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 63, 'country_id': 133},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 57, 'country_id': 81},
            {'customer_email_campaign_id': 4, 'customer_email_sender_id': 51, 'country_id': 9},
        ]
    )

def downgrade() -> None:
     op.execute("""
        DELETE FROM ce_sender_email_campaign_country
        WHERE customer_email_campaign_id = 4
    """)
