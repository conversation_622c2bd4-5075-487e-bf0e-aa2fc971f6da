"""new colomun

Revision ID: ce3702036744
Revises: 437ee5ef334d
Create Date: 2025-09-02 12:00:51.796072

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'ce3702036744'
down_revision: Union[str, None] = '437ee5ef334d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('customer_email_item_tracking', sa.Column('device_used', sa.String(length=255), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('customer_email_item_tracking', 'device_used')
    # ### end Alembic commands ###
