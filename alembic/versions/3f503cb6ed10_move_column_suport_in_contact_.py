"""move column suport in contact information

Revision ID: 3f503cb6ed10
Revises: 4a2ac8ff4686
Create Date: 2025-04-08 06:26:35.327401

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '3f503cb6ed10'
down_revision: Union[str, None] = '4a2ac8ff4686'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('account_information', sa.Column('support', sa.Enum('THINKING_ABOUT_PERSONAL_PROJECT', 'CHECK_A_QUOTE_OR_AN_OFFER', 'CHECK_ITS_PROFITABILITY', name='supportenum'), nullable=True))
    op.drop_column('customer', 'support')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('customer', sa.Column('support', mysql.ENUM('THINKING_ABOUT_PERSONAL_PROJECT', 'CHECK_A_QUOTE_OR_AN_OFFER', 'CHECK_ITS_PROFITABILITY', collation='utf8mb4_general_ci'), nullable=True))
    op.drop_column('account_information', 'support')
    # ### end Alembic commands ###
