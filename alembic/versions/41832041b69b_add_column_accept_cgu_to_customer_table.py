"""Add column accept_cgu to customer table

Revision ID: 41832041b69b
Revises: 3c6da6ae4ebd
Create Date: 2024-08-07 05:12:39.930096

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '41832041b69b'
down_revision: Union[str, None] = '3c6da6ae4ebd'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('customer', sa.Column('accept_cgu', sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('customer', 'accept_cgu')
    # ### end Alembic commands ###
