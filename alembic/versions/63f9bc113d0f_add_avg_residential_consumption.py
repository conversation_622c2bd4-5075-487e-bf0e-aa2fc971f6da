"""add avg residential consumption

Revision ID: 63f9bc113d0f
Revises: 02e18c309217
Create Date: 2025-03-20 11:29:40.031374

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '63f9bc113d0f'
down_revision: Union[str, None] = '02e18c309217'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
     
    op.add_column('country', sa.Column('avg_residential_consumption_json', sa.JSON(), nullable=True))
    
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    
    op.drop_column('country', 'avg_residential_consumption_json')
   
    # ### end Alembic commands ###
