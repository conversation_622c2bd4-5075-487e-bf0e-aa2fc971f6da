"""create new table email_item_tracking

Revision ID: e787eaa6a5ea
Revises: 3c334262492c
Create Date: 2025-09-02 08:45:45.880217

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'e787eaa6a5ea'
down_revision: Union[str, None] = '3c334262492c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('customer_email_item_tracking',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.Column('last_user_to_interact', sa.Integer(), nullable=True),
    sa.Column('import_id', sa.Text(), nullable=True),
    sa.Column('event', sa.String(length=255), nullable=True),
    sa.Column('payload', sa.JSON(), nullable=True),
    sa.Column('date_event', sa.DateTime(), nullable=True),
    sa.Column('sent_at', sa.DateTime(), nullable=True),
    sa.Column('sender_email', sa.String(length=255), nullable=True),
    sa.Column('sending_ip', sa.String(length=255), nullable=True),
    sa.Column('customer_id', sa.Integer(), nullable=True),
    sa.Column('language', sa.String(length=5), nullable=True),
    sa.Column('brevo_template', sa.Integer(), nullable=True),
    sa.Column('workflow_id', sa.Integer(), nullable=True),
    sa.Column('current_task', sa.Integer(), nullable=True),
    sa.Column('campaign_number_brevo', sa.Integer(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_customer_email_item_tracking_event'), 'customer_email_item_tracking', ['event'], unique=False)
    op.create_index(op.f('ix_customer_email_item_tracking_last_user_to_interact'), 'customer_email_item_tracking', ['last_user_to_interact'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_customer_email_item_tracking_last_user_to_interact'), table_name='customer_email_item_tracking')
    op.drop_index(op.f('ix_customer_email_item_tracking_event'), table_name='customer_email_item_tracking')
    op.drop_table('customer_email_item_tracking')
    # ### end Alembic commands ###
