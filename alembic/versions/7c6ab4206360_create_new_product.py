"""create new product

Revision ID: 7c6ab4206360
Revises: d269a28dab0d
Create Date: 2025-05-15 06:20:42.721237

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '7c6ab4206360'
down_revision: Union[str, None] = 'd269a28dab0d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    product = sa.table(
        'product',
        sa.column('id', sa.Integer()),
        sa.column('name', sa.String()),
        sa.column('user_count', sa.Integer()),
        sa.column('monthly_credit', sa.Integer()),
        sa.column('monthly_price', sa.Float()),
        sa.column('show', sa.Integer()),
        sa.column('allow_new_subscribers', sa.Integer()),
        sa.column('is_recommended', sa.Integer()),
        sa.column('description_translation_key', sa.String()),
        sa.column('ui_order', sa.Integer()),
        sa.column('description_note_translation_key', sa.String()),
        sa.column('billing_period_interval', sa.String()),
        sa.column('type', sa.Integer())
    )
    op.bulk_insert(
        product,
        [
            {'id': 11,'name': 'PVGIS24 Revente Totale', 'user_count': 1, 'monthly_credit': 1, 'monthly_price': 19, 'show': 1, 'allow_new_subscribers': 1, 'is_recommended': 0, 'description_translation_key': 'pvgis.product_revente_totale_bref_desc', 'ui_order': 10, 'description_note_translation_key': 'pvgis.product_revente_totale_note', 'billing_period_interval': 'year', 'type': 2},
            {'id': 12,'name': 'PVGIS24 Hybride', 'user_count': 1, 'monthly_credit': 1, 'monthly_price': 25, 'show': 1, 'allow_new_subscribers': 1, 'is_recommended': 0, 'description_translation_key': 'pvgis.product_hybride_bref_desc', 'ui_order': 11, 'description_note_translation_key': 'pvgis.product_hybride_note', 'billing_period_interval': 'year', 'type': 2},
            {'id': 13,'name': 'PVGIS24 Autonomie', 'user_count': 1, 'monthly_credit': 1, 'monthly_price': 29, 'show': 1, 'allow_new_subscribers': 1, 'is_recommended': 0, 'description_translation_key': 'pvgis.product_autonomie_bref_desc', 'ui_order': 12, 'description_note_translation_key': 'pvgis.product_autonomie_note', 'billing_period_interval': 'year', 'type': 2}
        ]
    )
    product_features = sa.table(
        'product_features',
        sa.column('product_id', sa.Integer()),
        sa.column('features_id', sa.Integer()),
        sa.column('allow', sa.Integer())
    )
    op.bulk_insert(
        product_features,
        [
            {'product_id': 11, 'features_id': 2, 'allow': 1},
            {'product_id': 11, 'features_id': 3, 'allow': 1},
            {'product_id': 11, 'features_id': 4, 'allow': 1},
            {'product_id': 11, 'features_id': 5, 'allow': 0},
            {'product_id': 11, 'features_id': 6, 'allow': 0},
            {'product_id': 11, 'features_id': 7, 'allow': 1},
            {'product_id': 11, 'features_id': 11, 'allow': 0},
            {'product_id': 12, 'features_id': 2, 'allow': 1},
            {'product_id': 12, 'features_id': 3, 'allow': 1},
            {'product_id': 12, 'features_id': 4, 'allow': 1},
            {'product_id': 12, 'features_id': 5, 'allow': 1},
            {'product_id': 12, 'features_id': 6, 'allow': 0},
            {'product_id': 12, 'features_id': 7, 'allow': 1},
            {'product_id': 12, 'features_id': 11, 'allow': 0},
            {'product_id': 13, 'features_id': 2, 'allow': 1},
            {'product_id': 13, 'features_id': 3, 'allow': 1},
            {'product_id': 13, 'features_id': 4, 'allow': 1},
            {'product_id': 13, 'features_id': 5, 'allow': 1},
            {'product_id': 13, 'features_id': 6, 'allow': 1},
            {'product_id': 13, 'features_id': 7, 'allow': 1},
            {'product_id': 13, 'features_id': 11, 'allow': 0}
        ]
    )
    # product = sa.table(
    #     'product',
    #     sa.column('id', sa.Integer()),
    #     sa.column('name', sa.String()),
    #     sa.column('user_count', sa.Integer()),
    #     sa.column('monthly_credit', sa.Integer()),
    #     sa.column('monthly_price', sa.Float()),
    #     sa.column('show', sa.Integer()),
    #     sa.column('allow_new_subscribers', sa.Integer()),
    #     sa.column('is_recommended', sa.Integer()),
    #     sa.column('description_translation_key', sa.String()),
    #     sa.column('ui_order', sa.Integer()),
    #     sa.column('description_note_translation_key', sa.String()),
    #     sa.column('billing_period_interval', sa.String()),
    #     sa.column('type', sa.Integer())
    # )
    # op.bulk_insert(
    #     product,
    #     [
    #         {'id': 11,'name': 'PVGIS24 Revente Totale', 'user_count': 1, 'monthly_credit': 1, 'monthly_price': 19, 'show': 1, 'allow_new_subscribers': 1, 'is_recommended': 0, 'description_translation_key': 'pvgis.product_revente_totale_bref_desc', 'ui_order': 10, 'description_note_translation_key': 'pvgis.product_revente_totale_note', 'billing_period_interval': 'year', 'type': 2},
    #         {'id': 12,'name': 'PVGIS24 Hybride', 'user_count': 1, 'monthly_credit': 1, 'monthly_price': 25, 'show': 1, 'allow_new_subscribers': 1, 'is_recommended': 0, 'description_translation_key': 'pvgis.product_hybride_bref_desc', 'ui_order': 11, 'description_note_translation_key': 'pvgis.product_hybride_note', 'billing_period_interval': 'year', 'type': 2},
    #         {'id': 13,'name': 'PVGIS24 Autonomie', 'user_count': 1, 'monthly_credit': 1, 'monthly_price': 29, 'show': 1, 'allow_new_subscribers': 1, 'is_recommended': 0, 'description_translation_key': 'pvgis.product_autonomie_bref_desc', 'ui_order': 12, 'description_note_translation_key': 'pvgis.product_autonomie_note', 'billing_period_interval': 'year', 'type': 2},
    #         {'id': 17,'name': 'Pack Multi Projet', 'user_count': 1, 'monthly_credit': 3, 'monthly_price': 49, 'show': 1, 'allow_new_subscribers': 1, 'is_recommended': 1, 'description_translation_key': 'pvgis.product_multi_project_bref_desc', 'ui_order': 16, 'description_note_translation_key': 'pvgis.product_multi_project_note', 'billing_period_interval': 'year', 'type': 2}
    #     ]
    # )
    # product_features = sa.table(
    #     'product_features',
    #     sa.column('product_id', sa.Integer()),
    #     sa.column('features_id', sa.Integer()),
    #     sa.column('allow', sa.Integer())
    # )
    # op.bulk_insert(
    #     product_features,
    #     [
    #         {'product_id': 11, 'features_id': 2, 'allow': 1},
    #         {'product_id': 11, 'features_id': 3, 'allow': 1},
    #         {'product_id': 11, 'features_id': 4, 'allow': 1},
    #         {'product_id': 11, 'features_id': 5, 'allow': 0},
    #         {'product_id': 11, 'features_id': 6, 'allow': 0},
    #         {'product_id': 11, 'features_id': 7, 'allow': 1},
    #         {'product_id': 11, 'features_id': 11, 'allow': 0},
    #         {'product_id': 12, 'features_id': 2, 'allow': 1},
    #         {'product_id': 12, 'features_id': 3, 'allow': 1},
    #         {'product_id': 12, 'features_id': 4, 'allow': 1},
    #         {'product_id': 12, 'features_id': 5, 'allow': 1},
    #         {'product_id': 12, 'features_id': 6, 'allow': 0},
    #         {'product_id': 12, 'features_id': 7, 'allow': 1},
    #         {'product_id': 12, 'features_id': 11, 'allow': 0},
    #         {'product_id': 13, 'features_id': 2, 'allow': 1},
    #         {'product_id': 13, 'features_id': 3, 'allow': 1},
    #         {'product_id': 13, 'features_id': 4, 'allow': 1},
    #         {'product_id': 13, 'features_id': 5, 'allow': 1},
    #         {'product_id': 13, 'features_id': 6, 'allow': 1},
    #         {'product_id': 13, 'features_id': 7, 'allow': 1},
    #         {'product_id': 13, 'features_id': 11, 'allow': 0},
    #         {'product_id': 17, 'features_id': 2, 'allow': 1},
    #         {'product_id': 17, 'features_id': 3, 'allow': 1},
    #         {'product_id': 17, 'features_id': 4, 'allow': 1},
    #         {'product_id': 17, 'features_id': 5, 'allow': 1},
    #         {'product_id': 17, 'features_id': 6, 'allow': 1},
    #         {'product_id': 17, 'features_id': 7, 'allow': 1},
    #         {'product_id': 17, 'features_id': 11, 'allow': 1}
    #     ]
    # )
def downgrade() -> None:
    op.execute("""
        DELETE FROM product
        WHERE name IN ('PVGIS24 Revente Totale', 'PVGIS24 Hybride', 'PVGIS24 Autonomie', 'Pack Multi Projet')
    """)
    op.execute("""
        DELETE FROM product_features
        WHERE product_id IN (11, 12, 13)
    """)
