"""rename brevo_task_id to workflow_id

Revision ID: 3c334262492c
Revises: f42dfaed57d2
Create Date: 2025-08-25 03:52:24.684775

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '3c334262492c'
down_revision: Union[str, None] = 'f42dfaed57d2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.alter_column(
        "customer_email_item",
        "brevo_task_id",
        new_column_name="workflow_id",
        existing_type=sa.Integer(),
        existing_nullable=True,
    )
    op.alter_column(
        "customer_email_item",
        "brevo",
        existing_type=sa.<PERSON>an(),
        nullable=True,
        server_default=None
    )
    op.alter_column(
        "customer_email_item",
        "brevo_template",
        existing_type=sa.Integer(),
        nullable=True,
        server_default=None
    )


def downgrade() -> None:
    op.alter_column(
        "customer_email_item",
        "workflow_id",
        new_column_name="brevo_task_id",
        existing_type=sa.Integer(),
        existing_nullable=True,
    )

    op.alter_column(
        "customer_email_item",
        "brevo",
        existing_type=sa.Boolean(),
        nullable=True,
        server_default=sa.text("0")  
    )
    op.alter_column(
        "customer_email_item",
        "brevo_template",
        existing_type=sa.Integer(),
        nullable=True,
        server_default=sa.text("0")
    )