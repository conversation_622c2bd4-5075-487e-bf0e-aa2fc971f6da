"""Ajout de la colonne essai_count nullable

Revision ID: 717b167c36b1
Revises: 5fc6da85c327
Create Date: 2025-05-12 11:12:45.702527

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '717b167c36b1'
down_revision: Union[str, None] = '5fc6da85c327'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('simulation', sa.Column('essai_count', sa.Integer(), nullable=True, server_default="1"))
    op.execute("UPDATE simulation SET essai_count = 1 WHERE essai_count IS NULL")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('simulation', 'essai_count')
    # ### end Alembic commands ###
