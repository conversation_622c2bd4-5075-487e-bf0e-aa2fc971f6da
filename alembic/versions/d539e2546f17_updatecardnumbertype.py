"""UpdateCardNumberType

Revision ID: d539e2546f17
Revises: c8f7de1839d7
Create Date: 2024-08-09 05:53:31.402720

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'd539e2546f17'
down_revision: Union[str, None] = 'c8f7de1839d7'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('customer_payment_information', 'card_number',
               existing_type=mysql.INTEGER(display_width=11),
               type_=sa.String(length=16),
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('customer_payment_information', 'card_number',
               existing_type=sa.String(length=16),
               type_=mysql.INTEGER(display_width=11),
               existing_nullable=True)
    # ### end Alembic commands ###
