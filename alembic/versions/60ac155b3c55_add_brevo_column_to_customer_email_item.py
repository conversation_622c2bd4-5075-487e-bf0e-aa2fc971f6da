"""Add brevo column to customer_email_item

Revision ID: 60ac155b3c55
Revises: 20c5a5d87aa3
Create Date: 2025-08-20 11:56:39.845344

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '60ac155b3c55'
down_revision: Union[str, None] = '20c5a5d87aa3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column('customer_email_item', sa.Column('brevo', sa.<PERSON>(), nullable=True, default=False))


def downgrade() -> None:
    op.drop_column('customer_email_item', 'brevo')
