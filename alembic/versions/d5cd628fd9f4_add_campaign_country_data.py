"""add campaign country data

Revision ID: d5cd628fd9f4
Revises: 21b0eb0f9387
Create Date: 2025-03-07 06:51:47.153572

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'd5cd628fd9f4'
down_revision: Union[str, None] = '21b0eb0f9387'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('ce_campaign_country_data',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.Column('last_user_to_interact', sa.Integer(), nullable=True),
    sa.Column('import_id', sa.Text(), nullable=True),
    sa.Column('country_id', sa.Integer(), nullable=True),
    sa.Column('customer_email_campaign_id', sa.Integer(), nullable=True),
    sa.Column('default_customer_json', sa.JSON(), nullable=True),
    sa.Column('default_language', sa.String(length=50), nullable=True),
    sa.ForeignKeyConstraint(['country_id'], ['country.id'], ),
    sa.ForeignKeyConstraint(['customer_email_campaign_id'], ['customer_email_campaign.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_ce_campaign_country_data_last_user_to_interact'), 'ce_campaign_country_data', ['last_user_to_interact'], unique=False)
    op.create_table('ce_sender_email_task_country',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.Column('last_user_to_interact', sa.Integer(), nullable=True),
    sa.Column('import_id', sa.Text(), nullable=True),
    sa.Column('customer_email_task_id', sa.Integer(), nullable=True),
    sa.Column('customer_email_sender_id', sa.Integer(), nullable=True),
    sa.Column('country_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['country_id'], ['country.id'], ),
    sa.ForeignKeyConstraint(['customer_email_sender_id'], ['customer_email_sender.id'], ),
    sa.ForeignKeyConstraint(['customer_email_task_id'], ['customer_email_task.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('customer_email_task_id', 'customer_email_sender_id', 'country_id', name='uq_sender_et_c')
    )
    op.create_index(op.f('ix_ce_sender_email_task_country_last_user_to_interact'), 'ce_sender_email_task_country', ['last_user_to_interact'], unique=False)
    
    op.execute('DELETE FROM sender_email_task_country')
    op.drop_table('sender_email_task_country')
     
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    
    op.create_table('sender_email_task_country',
    sa.Column('id', mysql.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('created_at', mysql.DATETIME(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True),
    sa.Column('updated_at', mysql.DATETIME(), nullable=True),
    sa.Column('deleted_at', mysql.DATETIME(), nullable=True),
    sa.Column('last_user_to_interact', mysql.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('import_id', mysql.TEXT(collation='utf8mb4_unicode_ci'), nullable=True),
    sa.Column('customer_email_task_id', mysql.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('customer_email_sender_id', mysql.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('country_id', mysql.INTEGER(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['country_id'], ['country.id'], name='sender_email_task_country_ibfk_2'),
    sa.ForeignKeyConstraint(['customer_email_sender_id'], ['customer_email_sender.id'], name='sender_email_task_country_ibfk_1'),
    sa.ForeignKeyConstraint(['customer_email_task_id'], ['customer_email_task.id'], name='sender_email_task_country_ibfk_3'),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_unicode_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.create_index('uq_sender_et_c', 'sender_email_task_country', ['customer_email_task_id', 'customer_email_sender_id', 'country_id'], unique=True)
    op.create_index('ix_sender_email_task_country_last_user_to_interact', 'sender_email_task_country', ['last_user_to_interact'], unique=False)
    op.drop_index(op.f('ix_ce_sender_email_task_country_last_user_to_interact'), table_name='ce_sender_email_task_country')
    op.drop_table('ce_sender_email_task_country')
    op.drop_index(op.f('ix_ce_campaign_country_data_last_user_to_interact'), table_name='ce_campaign_country_data')
    op.drop_table('ce_campaign_country_data')
    # ### end Alembic commands ###
