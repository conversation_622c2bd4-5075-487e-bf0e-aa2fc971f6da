"""add sender-country-campaign relation

Revision ID: 1d571e6a22eb
Revises: d5cd628fd9f4
Create Date: 2025-03-07 08:49:23.701101

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '1d571e6a22eb'
down_revision: Union[str, None] = 'd5cd628fd9f4'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('ce_sender_email_campaign_country',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.Column('last_user_to_interact', sa.Integer(), nullable=True),
    sa.Column('import_id', sa.Text(), nullable=True),
    sa.Column('customer_email_campaign_id', sa.Integer(), nullable=True),
    sa.Column('customer_email_sender_id', sa.Integer(), nullable=True),
    sa.Column('country_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['country_id'], ['country.id'], ),
    sa.ForeignKeyConstraint(['customer_email_campaign_id'], ['customer_email_campaign.id'], ),
    sa.ForeignKeyConstraint(['customer_email_sender_id'], ['customer_email_sender.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('customer_email_campaign_id', 'customer_email_sender_id', 'country_id', name='uq_sender_et_c')
    )
    op.create_index(op.f('ix_ce_sender_email_campaign_country_last_user_to_interact'), 'ce_sender_email_campaign_country', ['last_user_to_interact'], unique=False)
    op.execute('delete from ce_sender_email_task_country')
    op.drop_table('ce_sender_email_task_country')
    
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
     
    op.create_table('ce_sender_email_task_country',
    sa.Column('id', mysql.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('created_at', mysql.DATETIME(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True),
    sa.Column('updated_at', mysql.DATETIME(), nullable=True),
    sa.Column('deleted_at', mysql.DATETIME(), nullable=True),
    sa.Column('last_user_to_interact', mysql.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('import_id', mysql.TEXT(collation='utf8mb4_unicode_ci'), nullable=True),
    sa.Column('customer_email_task_id', mysql.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('customer_email_sender_id', mysql.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('country_id', mysql.INTEGER(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['country_id'], ['country.id'], name='ce_sender_email_task_country_ibfk_1'),
    sa.ForeignKeyConstraint(['customer_email_sender_id'], ['customer_email_sender.id'], name='ce_sender_email_task_country_ibfk_2'),
    sa.ForeignKeyConstraint(['customer_email_task_id'], ['customer_email_task.id'], name='ce_sender_email_task_country_ibfk_3'),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_unicode_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.create_index('uq_sender_et_c', 'ce_sender_email_task_country', ['customer_email_task_id', 'customer_email_sender_id', 'country_id'], unique=True)
    op.create_index('ix_ce_sender_email_task_country_last_user_to_interact', 'ce_sender_email_task_country', ['last_user_to_interact'], unique=False)
    op.drop_index(op.f('ix_ce_sender_email_campaign_country_last_user_to_interact'), table_name='ce_sender_email_campaign_country')
    op.drop_table('ce_sender_email_campaign_country')
    # ### end Alembic commands ###
