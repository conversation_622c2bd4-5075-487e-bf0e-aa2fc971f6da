"""refactor features

Revision ID: 44c8aaff48c6
Revises: 1d571e6a22eb
Create Date: 2025-03-19 10:33:38.682938

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '44c8aaff48c6'
down_revision: Union[str, None] = '1d571e6a22eb'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute('DELETE FROM product_features')
    op.add_column('product_features', sa.Column('allow', sa.<PERSON>(), nullable=False))
    op.drop_column('product_features', 'list_decoration')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('product_features', sa.Column('list_decoration', mysql.VARCHAR(collation='utf8mb4_general_ci', length=255), nullable=True))
    op.drop_column('product_features', 'allow')
      
    # ### end Alembic commands ###
