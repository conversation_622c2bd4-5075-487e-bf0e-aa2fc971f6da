"""modifyProduct

Revision ID: a1a1e561dee5
Revises: c3dd1e3b845f
Create Date: 2024-10-22 10:42:54.379316

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'a1a1e561dee5'
down_revision: Union[str, None] = 'c3dd1e3b845f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('product', sa.Column('allow_trial', sa.<PERSON>(), nullable=True))
    op.add_column('product', sa.Column('discount_stripe_id', sa.String(length=255), nullable=True))
    
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
   
    op.drop_column('product', 'discount_stripe_id')
    op.drop_column('product', 'allow_trial')
    # ### end Alembic commands ###
