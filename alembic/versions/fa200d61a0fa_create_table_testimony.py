"""create table testimony

Revision ID: fa200d61a0fa
Revises: 441b9744778e
Create Date: 2025-03-28 06:32:30.972722

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'fa200d61a0fa'
down_revision: Union[str, None] = '441b9744778e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('testimony',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.Column('last_user_to_interact', sa.Integer(), nullable=True),
    sa.Column('import_id', sa.Text(), nullable=True),
    sa.Column('name', sa.String(length=255), nullable=True),
    sa.Column('flag', sa.String(length=255), nullable=True),
    sa.Column('country_id', sa.Integer(), nullable=True),
    sa.Column('testimony', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['country_id'], ['country.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_testimony_last_user_to_interact'), 'testimony', ['last_user_to_interact'], unique=False)
    op.create_index(op.f('ix_testimony_name'), 'testimony', ['name'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_testimony_name'), table_name='testimony')
    op.drop_index(op.f('ix_testimony_last_user_to_interact'), table_name='testimony')
    op.drop_table('testimony')
    # ### end Alembic commands ###
