"""update models features

Revision ID: 1acc2f143dd8
Revises: 6de66bcb20d7
Create Date: 2025-02-19 04:56:49.222003

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '1acc2f143dd8'
down_revision: Union[str, None] = '6de66bcb20d7'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('features', sa.Column('is_free', sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('features', 'is_free')
    # ### end Alembic commands ###
