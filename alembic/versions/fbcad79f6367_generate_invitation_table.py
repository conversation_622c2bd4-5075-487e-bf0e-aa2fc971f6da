"""Generate invitation table

Revision ID: fbcad79f6367
Revises: 7285bf8e90d0
Create Date: 2024-12-04 07:56:28.448695

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'fbcad79f6367'
down_revision: Union[str, None] = '7285bf8e90d0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('invitation',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.Column('last_user_to_interact', sa.Integer(), nullable=True),
    sa.Column('customer_id', sa.Integer(), nullable=True),
    sa.Column('user_sender_id', sa.Integer(), nullable=True),
    sa.Column('invitation_link', sa.String(length=255), nullable=True),
    sa.Column('invitation_sent_at', sa.DateTime(), nullable=True),
    sa.Column('invitation_accepted_at', sa.DateTime(), nullable=True),
    sa.Column('email', sa.String(length=255), nullable=True),
    sa.ForeignKeyConstraint(['customer_id'], ['customer.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_invitation_last_user_to_interact'), 'invitation', ['last_user_to_interact'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_invitation_last_user_to_interact'), table_name='invitation')
    op.drop_table('invitation')
    # ### end Alembic commands ###
