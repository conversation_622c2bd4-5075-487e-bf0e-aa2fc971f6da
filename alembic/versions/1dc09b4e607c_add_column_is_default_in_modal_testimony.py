"""add column is_default in modal testimony

Revision ID: 1dc09b4e607c
Revises: bded8126d2b2
Create Date: 2025-04-11 10:32:58.269829

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '1dc09b4e607c'
down_revision: Union[str, None] = 'bded8126d2b2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('testimony', sa.Column('is_default', sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('testimony', 'is_default')
    # ### end Alembic commands ###
