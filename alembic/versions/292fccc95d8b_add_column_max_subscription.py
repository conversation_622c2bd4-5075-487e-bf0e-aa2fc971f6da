"""Add column max_subscription

Revision ID: 292fccc95d8b
Revises: 0541d9b506b4
Create Date: 2024-09-19 15:16:00.446609

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '292fccc95d8b'
down_revision: Union[str, None] = '0541d9b506b4'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('product', sa.Column('subscription_max_count', sa.Integer(), nullable=True))
    op.alter_column('subscription_payment_transaction', 'amount_paid',
               existing_type=mysql.DOUBLE(asdecimal=True),
               type_=sa.Float(precision=53),
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('subscription_payment_transaction', 'amount_paid',
               existing_type=sa.Float(precision=53),
               type_=mysql.DOUBLE(asdecimal=True),
               existing_nullable=True)
    op.drop_column('product', 'subscription_max_count')
    # ### end Alembic commands ###
