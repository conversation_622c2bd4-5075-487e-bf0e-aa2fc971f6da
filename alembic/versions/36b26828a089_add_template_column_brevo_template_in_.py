"""Add template column brevo template in mail item

Revision ID: 36b26828a089
Revises: 907b1a24f774
Create Date: 2025-08-21 07:43:09.711746

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '36b26828a089'
down_revision: Union[str, None] = '907b1a24f774'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column('customer_email_item', sa.Column('brevo_template', sa.Integer(), nullable=True, default=0))
    pass


def downgrade() -> None:
    op.drop_column('customer_email_item', 'brevo_template')
    pass
