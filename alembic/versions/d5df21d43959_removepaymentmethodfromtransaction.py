"""RemovePaymentMethodFromTransaction

Revision ID: d5df21d43959
Revises: c1a1ed4d9a1e
Create Date: 2024-08-22 09:19:23.692482

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'd5df21d43959'
down_revision: Union[str, None] = 'c1a1ed4d9a1e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('subscription_payment_transaction_ibfk_3', 'subscription_payment_transaction', type_='foreignkey')
    op.drop_column('subscription_payment_transaction', 'customer_payment_information_id')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('subscription_payment_transaction', sa.Column('customer_payment_information_id', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True))
    op.create_foreign_key('subscription_payment_transaction_ibfk_3', 'subscription_payment_transaction', 'customer_payment_information', ['customer_payment_information_id'], ['id'])
    # ### end Alembic commands ###
