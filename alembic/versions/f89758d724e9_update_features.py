"""update features

Revision ID: f89758d724e9
Revises: 717b167c36b1
Create Date: 2025-05-23 06:07:46.399264

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.sql import text


# revision identifiers, used by Alembic.
revision: str = 'f89758d724e9'
down_revision: Union[str, None] = '717b167c36b1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    conn = op.get_bind()

    conn.execute(text("""
        UPDATE features
        SET ui_order = ui_order + 1
        WHERE ui_order BETWEEN 4 AND 12
    """))

    conn.execute(text("""
        INSERT INTO features (id, name, description, `key`, ui_order, allow_for_free, display_for_free)
        VALUES (:id, :name, :description, :key, :ui_order, :allow_for_free, :display_for_free)
    """), {
        'id': 13,
        'name': 'PDF printing',
        'description': 'PDF printing',
        'key': 'pdf_printing',
        'ui_order': 4,
        'allow_for_free': 1,
        'display_for_free': 1
    })



def downgrade() -> None:
    conn = op.get_bind()

    # Delete the new feature
    conn.execute(text("""
        DELETE FROM features WHERE id = 13
    """))

    # Shift ui_order values from 5–13 back to 4–12
    conn.execute(text("""
        UPDATE features
        SET ui_order = ui_order - 1
        WHERE ui_order BETWEEN 5 AND 13
    """))
