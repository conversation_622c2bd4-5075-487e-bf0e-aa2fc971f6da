"""init spin control  mail

Revision ID: f99ff5ea6ef0
Revises: 469259e56bc7
Create Date: 2025-06-05 05:46:29.861345

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f99ff5ea6ef0'
down_revision: Union[str, None] = '469259e56bc7'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    customer_email_campaign = sa.table(
        'customer_email_campaign',
        sa.column('id', sa.Integer()),
        sa.column('name', sa.String()),
        sa.column('key', sa.String()),
        sa.column('is_active', sa.Integer())
    )

    op.bulk_insert(
        customer_email_campaign,
        [{
            'id': 7,
            'name': 'Spin : Control',
            'key': 'spin_control',
            'is_active': 0
        }]
    )
    
    customer_email_task = sa.table(
        'customer_email_task',
        sa.column('id', sa.Integer()),
        sa.column('key', sa.String()),
        sa.column('name', sa.String()),
        sa.column('email_number', sa.Integer()),
        sa.column('cms_key', sa.String()),
        sa.column('is_active', sa.Integer()),
        sa.column('customer_email_campaign_id', sa.Integer())
    )
 
    op.bulk_insert(
        customer_email_task,
        [
            {'id': 36, 'key': 'pvgis-email-control-spin-1-j-1', 'name': 'Spin Control 1', 'email_number': 36, 'cms_key': 'pvgis-email-control-spin-1-j-1', 'is_active': 1, 'customer_email_campaign_id': 7},
            {'id': 37, 'key': 'pvgis-email-control-spin-2-j-3', 'name': 'Spin Control 2', 'email_number': 37, 'cms_key': 'pvgis-email-control-spin-2-j-3', 'is_active': 1, 'customer_email_campaign_id': 7},
            {'id': 38, 'key': 'pvgis-email-control-spin-3-j-5', 'name': 'Spin Control 3', 'email_number': 38, 'cms_key': 'pvgis-email-control-spin-3-j-5', 'is_active': 1, 'customer_email_campaign_id': 7},
            {'id': 39, 'key': 'pvgis-email-control-spin-4-j-7', 'name': 'Spin Control 4', 'email_number': 39, 'cms_key': 'pvgis-email-control-spin-4-j-7', 'is_active': 1, 'customer_email_campaign_id': 7},
            {'id': 40, 'key': 'pvgis-email-control-spin-5-j-14', 'name': 'Spin Control 5', 'email_number': 40, 'cms_key': 'pvgis-email-control-spin-5-j-14', 'is_active': 1, 'customer_email_campaign_id': 7},
            {'id': 41, 'key': 'pvgis-email-control-spin-6-j-16', 'name': 'Spin Control 6', 'email_number': 41, 'cms_key': 'pvgis-email-control-spin-6-j-16', 'is_active': 1, 'customer_email_campaign_id': 7},
            {'id': 42, 'key': 'pvgis-email-control-spin-7-j-18', 'name': 'Spin Control 7', 'email_number': 42, 'cms_key': 'pvgis-email-control-spin-7-j-18', 'is_active': 1, 'customer_email_campaign_id': 7},
            {'id': 43, 'key': 'pvgis-email-control-spin-8-j-20', 'name': 'Spin Control 8', 'email_number': 43, 'cms_key': 'pvgis-email-control-spin-8-j-20', 'is_active': 1, 'customer_email_campaign_id': 7}
        ]
    )


def downgrade() -> None:
    op.execute(
        sa.text("""
            DELETE FROM customer_email_task
            WHERE customer_email_campaign_id = :campaign_id
        """).bindparams(campaign_id=7)
    )

    op.execute(
        sa.text("""
            DELETE FROM customer_email_campaign
            WHERE id = :campaign_id
        """).bindparams(campaign_id=7)
    )
