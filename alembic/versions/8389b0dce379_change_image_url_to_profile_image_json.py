"""change image_url to profile_image_json

Revision ID: 8389b0dce379
Revises: d4a6588ed11f
Create Date: 2024-09-11 11:53:25.997079

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '8389b0dce379'
down_revision: Union[str, None] = 'd4a6588ed11f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('customer', sa.Column('profile_image_json', sa.JSON(), nullable=True))
    op.drop_column('customer', 'profile_image_url')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('customer', sa.<PERSON>umn('profile_image_url', mysql.VARCHAR(collation='utf8mb4_general_ci', length=255), nullable=True))
    op.drop_column('customer', 'profile_image_json')
    # ### end Alembic commands ###
