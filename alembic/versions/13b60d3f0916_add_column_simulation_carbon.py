"""Add column simulation_carbon

Revision ID: 13b60d3f0916
Revises: b13be08512ae
Create Date: 2024-11-08 09:31:55.700386

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '13b60d3f0916'
down_revision: Union[str, None] = 'b13be08512ae'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('simulation', sa.Column('simulation_carbon', sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('simulation', 'simulation_carbon')
    # ### end Alembic commands ###
