"""alter table simulation type

Revision ID: a7295d0ea578
Revises: 7226941a3b93
Create Date: 2024-07-31 06:45:54.743104

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'a7295d0ea578'
down_revision: Union[str, None] = '7226941a3b93'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('simulation', 'type',
               existing_type=mysql.ENUM('GRID_CONNECTED', 'TRACKING', 'DAILY', 'HOURLY', 'MONTHLY', 'OFFGRID', 'TMY'),
               type_=sa.Enum('TOTAL_RESALE', 'SELF_CONSUMPTION_RESALE_OF_SURPLUS', 'SIMPLE_SELF_CONSUMPTION', 'SELF_CONSUMPTION_PLUS_BATTERIES', 'AUTONOMY_PLUS_PUBLIC_GRID', 'BATTERY_AUTONOMY', 'ISOLATED_PARK', name='typeenum'),
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('simulation', 'type',
               existing_type=sa.Enum('TOTAL_RESALE', 'SELF_CONSUMPTION_RESALE_OF_SURPLUS', 'SIMPLE_SELF_CONSUMPTION', 'SELF_CONSUMPTION_PLUS_BATTERIES', 'AUTONOMY_PLUS_PUBLIC_GRID', 'BATTERY_AUTONOMY', 'ISOLATED_PARK', name='typeenum'),
               type_=mysql.ENUM('GRID_CONNECTED', 'TRACKING', 'DAILY', 'HOURLY', 'MONTHLY', 'OFFGRID', 'TMY'),
               existing_nullable=True)
    # ### end Alembic commands ###
