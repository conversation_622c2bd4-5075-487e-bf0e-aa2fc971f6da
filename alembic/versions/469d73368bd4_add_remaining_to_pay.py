"""add remaining to pay

Revision ID: 469d73368bd4
Revises: a4b01159fea1
Create Date: 2025-05-09 12:09:55.268826

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '469d73368bd4'
down_revision: Union[str, None] = 'a4b01159fea1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('subscription_payment_receipt', sa.Column('remaining_to_pay', mysql.DOUBLE(asdecimal=True), nullable=True))
    op.add_column('subscription_payment_receipt', sa.Column('amount_already_paid', mysql.DOUBLE(asdecimal=True), nullable=True))
    op.add_column('subscription_payment_receipt', sa.Column('stripe_reference', sa.String(length=255), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('subscription_payment_receipt', 'remaining_to_pay')
    op.drop_column('subscription_payment_receipt', 'amount_already_paid')
    # ### end Alembic commands ###
