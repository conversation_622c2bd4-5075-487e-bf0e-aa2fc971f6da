"""add brevo_task_id in email item

Revision ID: c0a4e38e58bc
Revises: 36b26828a089
Create Date: 2025-08-22 03:49:31.010843

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c0a4e38e58bc'
down_revision: Union[str, None] = '36b26828a089'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column('customer_email_item', sa.Column('brevo_task_id', sa.Integer(), nullable=True, default=None))
    pass


def downgrade() -> None:
    op.drop_column('customer_email_item', 'brevo_task_id')
    pass
