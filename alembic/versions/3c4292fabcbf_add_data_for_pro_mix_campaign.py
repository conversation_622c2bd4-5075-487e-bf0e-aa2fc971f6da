"""Add data for Pro mix campaign

Revision ID: 3c4292fabcbf
Revises: 8ad6b30b2a36
Create Date: 2025-04-30 06:04:44.124393

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '3c4292fabcbf'
down_revision: Union[str, None] = '8ad6b30b2a36'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    customer_email_campaign = sa.table(
        'customer_email_campaign',
        sa.column('id', sa.Integer()),
        sa.column('updated_at', sa.DateTime()),
        sa.column('deleted_at', sa.DateTime()),
        sa.column('last_user_to_interact', sa.Integer()),
        sa.column('import_id', sa.Integer()),
        sa.column('name', sa.String()),
        sa.column('key', sa.String()),
        sa.column('is_active', sa.Integer())
    )

    op.bulk_insert(
        customer_email_campaign,
        [{
            'id': 4,
            'last_user_to_interact': None,
            'import_id': None,
            'name': 'Pro Mix',
            'key': 'pro_mix',
            'is_active': 0
        }]
    )

    customer_email_task = sa.table(
        'customer_email_task',
        sa.column('id', sa.Integer()),
        sa.column('last_user_to_interact', sa.Integer()),
        sa.column('import_id', sa.Integer()),
        sa.column('key', sa.String()),
        sa.column('name', sa.String()),
        sa.column('email_number', sa.Integer()),
        sa.column('cms_key', sa.String()),
        sa.column('is_active', sa.Integer()),
        sa.column('customer_email_campaign_id', sa.Integer())
    )

    op.bulk_insert(
        customer_email_task,
        [
            {'id': 8, 'last_user_to_interact': None, 'import_id': None, 'key': 'pvgis_email_pro_mixte_1_j_1', 'name': 'Pro Mix 1', 'email_number': 8, 'cms_key': 'pvgis-email-pro-mixte-1-j-1', 'is_active': 1, 'customer_email_campaign_id': 4},
            {'id': 9, 'last_user_to_interact': None, 'import_id': None, 'key': 'pvgis_email_pro_mixte_2_j_3', 'name': 'Pro mix 2', 'email_number': 9, 'cms_key': 'pvgis-email-pro-mixte-2-j-3', 'is_active': 1, 'customer_email_campaign_id': 4},
            {'id': 10, 'last_user_to_interact': None, 'import_id': None, 'key': 'pvgis_email_pro_mixte_3_j_5', 'name': 'Pro mix 3', 'email_number': 10, 'cms_key': 'pvgis-email-pro-mixte-3-j-5', 'is_active': 1, 'customer_email_campaign_id': 4},
            {'id': 11, 'last_user_to_interact': None, 'import_id': None, 'key': 'pvgis_email_pro_mixte_4_j_7', 'name': 'Pro mix 4', 'email_number': 11, 'cms_key': 'pvgis-email-pro-mixte-4-j-7', 'is_active': 1, 'customer_email_campaign_id': 4},
            {'id': 12, 'last_user_to_interact': None, 'import_id': None, 'key': 'pvgis_email_pro_mixte_5_j_9', 'name': 'Pro mix 5', 'email_number': 12, 'cms_key': 'pvgis-email-pro-mixte-5-j-9', 'is_active': 1, 'customer_email_campaign_id': 4},
            {'id': 13, 'last_user_to_interact': None, 'import_id': None, 'key': 'pvgis_email_pro_mixte_6_j_11', 'name': 'Pro mix 6', 'email_number': 13, 'cms_key': 'pvgis-email-pro-mixte-6-j-11', 'is_active': 1, 'customer_email_campaign_id': 4},
            {'id': 14, 'last_user_to_interact': None, 'import_id': None, 'key': 'pvgis_email_pro_mixte_7_j_13', 'name': 'Pro mix 7', 'email_number': 14, 'cms_key': 'pvgis-email-pro-mixte-7-j-13', 'is_active': 1, 'customer_email_campaign_id': 4},
            {'id': 15, 'last_user_to_interact': None, 'import_id': None, 'key': 'pvgis_email_pro_mixte_8_j_15', 'name': 'Pro mix 8', 'email_number': 15, 'cms_key': 'pvgis-email-pro-mixte-8-j-15', 'is_active': 1, 'customer_email_campaign_id': 4},
            {'id': 16, 'last_user_to_interact': None, 'import_id': None, 'key': 'pvgis_email_pro_mixte_9_j_18', 'name': 'Pro mix 9', 'email_number': 16, 'cms_key': 'pvgis-email-pro-mixte-9-j-18', 'is_active': 1, 'customer_email_campaign_id': 4},
            {'id': 17, 'last_user_to_interact': None, 'import_id': None, 'key': 'pvgis_email_pro_mixte_10_j_21', 'name': 'Pro mix 10', 'email_number': 17, 'cms_key': 'pvgis-email-pro-mixte-10-j-21', 'is_active': 1, 'customer_email_campaign_id': 4},
            {'id': 18, 'last_user_to_interact': None, 'import_id': None, 'key': 'pvgis_email_pro_mixte_11_j_23', 'name': 'Pro mix 11', 'email_number': 18, 'cms_key': 'pvgis-email-pro-mixte-11-j-23', 'is_active': 1, 'customer_email_campaign_id': 4},
            {'id': 19, 'last_user_to_interact': None, 'import_id': None, 'key': 'pvgis_email_pro_mixte_12_j_30', 'name': 'Pro mix 12', 'email_number': 19, 'cms_key': 'pvgis-email-pro-mixte-12-j-30', 'is_active': 1, 'customer_email_campaign_id': 4}
        ]
    )


def downgrade() -> None:
    op.execute("""
        DELETE FROM customer_email_task
        WHERE customer_email_campaign_id = 4
    """)
    op.execute("""
        DELETE FROM customer_email_campaign
        WHERE id = 4
    """)