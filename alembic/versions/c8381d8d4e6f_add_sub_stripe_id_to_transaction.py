"""add sub stripe id to transaction

Revision ID: c8381d8d4e6f
Revises: caaf5147e735
Create Date: 2025-03-25 11:11:08.863436

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'c8381d8d4e6f'
down_revision: Union[str, None] = 'caaf5147e735'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
   
    op.add_column('subscription_payment_transaction', sa.Column('subscription_stripe_id', sa.String(length=255), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('subscription_payment_transaction', 'subscription_stripe_id')
    
    # ### end Alembic commands ###
