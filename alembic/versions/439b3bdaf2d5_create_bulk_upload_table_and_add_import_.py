"""create bulk upload table, and add import_id

Revision ID: 439b3bdaf2d5
Revises: 47cfff0779b9
Create Date: 2024-12-06 07:09:43.863571

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '439b3bdaf2d5'
down_revision: Union[str, None] = '47cfff0779b9'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('bulk_upload',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.Column('last_user_to_interact', sa.Integer(), nullable=True),
    sa.Column('import_id', sa.Text(), nullable=True),
    sa.Column('file_name', sa.Text(), nullable=True),
    sa.Column('resource_name', sa.Text(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_bulk_upload_last_user_to_interact'), 'bulk_upload', ['last_user_to_interact'], unique=False)
    op.add_column('account_information', sa.Column('import_id', sa.Text(), nullable=True))
    op.add_column('account_notification_setting', sa.Column('import_id', sa.Text(), nullable=True))
    op.add_column('account_type', sa.Column('import_id', sa.Text(), nullable=True))
    op.add_column('cart', sa.Column('import_id', sa.Text(), nullable=True))
    op.add_column('contact_platform', sa.Column('import_id', sa.Text(), nullable=True))
    op.add_column('customer', sa.Column('import_id', sa.Text(), nullable=True))
    op.add_column('customer_contact_platform_information', sa.Column('import_id', sa.Text(), nullable=True))
    op.add_column('features', sa.Column('import_id', sa.Text(), nullable=True))
    op.add_column('google_analytics', sa.Column('import_id', sa.Text(), nullable=True))
    op.add_column('invitation', sa.Column('import_id', sa.Text(), nullable=True))
    op.add_column('notification_setting', sa.Column('import_id', sa.Text(), nullable=True))
    op.add_column('payment_gateway', sa.Column('import_id', sa.Text(), nullable=True))
    op.add_column('payment_method', sa.Column('import_id', sa.Text(), nullable=True))
    op.add_column('product', sa.Column('import_id', sa.Text(), nullable=True))
    op.add_column('product_features', sa.Column('import_id', sa.Text(), nullable=True))
    op.add_column('professional_category', sa.Column('import_id', sa.Text(), nullable=True))
    op.add_column('school_category', sa.Column('import_id', sa.Text(), nullable=True))
    op.add_column('simulation', sa.Column('import_id', sa.Text(), nullable=True))
    op.add_column('simulation_item', sa.Column('import_id', sa.Text(), nullable=True))
    op.add_column('simulation_listing', sa.Column('import_id', sa.Text(), nullable=True))
    op.add_column('stripe_webhook', sa.Column('import_id', sa.Text(), nullable=True))
    op.add_column('subscription', sa.Column('import_id', sa.Text(), nullable=True))
    op.add_column('subscription_payment_transaction', sa.Column('import_id', sa.Text(), nullable=True))
    op.add_column('subscription_user', sa.Column('import_id', sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('subscription_user', 'import_id')
    op.drop_column('subscription_payment_transaction', 'import_id')
    op.drop_column('subscription', 'import_id')
    op.drop_column('stripe_webhook', 'import_id')
    op.drop_column('simulation_listing', 'import_id')
    op.drop_column('simulation_item', 'import_id')
    op.drop_column('simulation', 'import_id')
    op.drop_column('school_category', 'import_id')
    op.drop_column('professional_category', 'import_id')
    op.drop_column('product_features', 'import_id')
    op.drop_column('product', 'import_id')
    op.drop_column('payment_method', 'import_id')
    op.drop_column('payment_gateway', 'import_id')
    op.drop_column('notification_setting', 'import_id')
    op.drop_column('invitation', 'import_id')
    op.drop_column('google_analytics', 'import_id')
    op.drop_column('features', 'import_id')
    op.drop_column('customer_contact_platform_information', 'import_id')
    op.drop_column('customer', 'import_id')
    op.drop_column('contact_platform', 'import_id')
    op.drop_column('cart', 'import_id')
    op.drop_column('account_type', 'import_id')
    op.drop_column('account_notification_setting', 'import_id')
    op.drop_column('account_information', 'import_id')
    op.drop_index(op.f('ix_bulk_upload_last_user_to_interact'), table_name='bulk_upload')
    op.drop_table('bulk_upload')
    # ### end Alembic commands ###
