"""add column reference

Revision ID: 236cf81dbdb2
Revises: 66385ca184c5
Create Date: 2024-08-16 06:04:05.139479

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '236cf81dbdb2'
down_revision: Union[str, None] = '66385ca184c5'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('subscription_payment_transaction', sa.Column('reference', sa.String(length=255), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('subscription_payment_transaction', 'reference')
    # ### end Alembic commands ###
