"""Add column normalized_name  to CITY  model

Revision ID: 20378b7411ce
Revises: f1f2ae2cc0d6
Create Date: 2025-02-18 05:25:45.522434

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '20378b7411ce'
down_revision: Union[str, None] = 'f1f2ae2cc0d6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('city', sa.Column('normalized_name', sa.String(length=255), nullable=True))
    op.create_index(op.f('ix_city_normalized_name'), 'city', ['normalized_name'], unique=False)
    # ### end Alembic commands ###
    op.execute("""
        UPDATE city
        SET normalized_name = LOWER(REPLACE(name, ' ', '-'))
    """)


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_city_normalized_name'), table_name='city')
    op.drop_column('city', 'normalized_name')
    # ### end Alembic commands ###
