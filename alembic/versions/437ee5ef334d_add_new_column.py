"""add new column

Revision ID: 437ee5ef334d
Revises: e787eaa6a5ea
Create Date: 2025-09-02 11:27:25.223269

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '437ee5ef334d'
down_revision: Union[str, None] = 'e787eaa6a5ea'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column('customer_email_item_tracking', sa.Column('template_id', sa.Integer(), nullable=True))
def downgrade() -> None:
    op.drop_column('customer_email_item_tracking', 'template_id')

