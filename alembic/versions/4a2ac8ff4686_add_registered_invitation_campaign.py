"""add_registered_invitation_campaign

Revision ID: 4a2ac8ff4686
Revises: 373e62a7694c
Create Date: 2025-04-02 10:09:56.357491

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '4a2ac8ff4686'
down_revision: Union[str, None] = '373e62a7694c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    customer_email_campaign = sa.table(
        'customer_email_campaign',  
        sa.column('id', sa.Integer()),  
        sa.column('key', sa.String()),  
        sa.column('name', sa.String()),
        sa.column('is_active', sa.Integer()) 
    )
    customer_email_task = sa.table(
        'customer_email_task',  
        sa.column('id', sa.Integer()),  
        sa.column('key', sa.String()),  
        sa.column('name', sa.String()),  
        sa.column('email_number', sa.Integer()),  
        sa.column('cms_key', sa.String()),
        sa.column('is_active', sa.Integer()),
        sa.column('customer_email_campaign_id', sa.Integer()) 
    )
    op.bulk_insert(
        customer_email_campaign,
        [{
            'id': 3, 
            'key': 'registered_invitation', 
            'name': 'Registered Invitation', 
            'is_active': 0
        }]
    )
    op.bulk_insert(
        customer_email_task,
        [{
            'id': 7, 
            'key': 'registered_invitation', 
            'name': 'Registered Invitation', 
            'email_number': 7, 
            'cms_key': 'pvgis-installer-invitation',
            'is_active': 0,
            'customer_email_campaign_id': 3
        }]
    )


def downgrade() -> None:
    pass
