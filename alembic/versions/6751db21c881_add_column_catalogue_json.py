"""add column Catalogue json

Revision ID: 6751db21c881
Revises: d422f456f914
Create Date: 2024-10-29 10:55:16.063453

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '6751db21c881'
down_revision: Union[str, None] = 'd422f456f914'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('customer', sa.Column('catalogues_json', sa.JSON(), nullable=True))
    op.alter_column('subscription_payment_transaction', 'amount_paid',
               existing_type=mysql.DOUBLE(asdecimal=True),
               type_=sa.Float(precision=53),
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('subscription_payment_transaction', 'amount_paid',
               existing_type=sa.Float(precision=53),
               type_=mysql.DOUBLE(asdecimal=True),
               existing_nullable=True)
    op.drop_column('customer', 'catalogues_json')
    # ### end Alembic commands ###
