"""implement column base language to model testimony

Revision ID: 46de296fa504
Revises: 31218447b739
Create Date: 2025-04-16 05:46:58.349138

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '46de296fa504'
down_revision: Union[str, None] = '31218447b739'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('testimony', sa.Column('base_language', sa.String(length=10), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('testimony', 'base_language')
    # ### end Alembic commands ###
