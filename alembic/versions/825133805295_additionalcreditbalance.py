"""additionalCreditBalance

Revision ID: 825133805295
Revises: 13b60d3f0916
Create Date: 2024-11-09 05:14:30.343608

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '825133805295'
down_revision: Union[str, None] = '13b60d3f0916'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('subscription', sa.Column('additional_credit_balance', sa.Integer(), nullable=True))
    
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('subscription', 'additional_credit_balance')
    # ### end Alembic commands ###
