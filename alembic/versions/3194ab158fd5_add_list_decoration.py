"""add list decoration

Revision ID: 3194ab158fd5
Revises: 7b6e01d9a02c
Create Date: 2025-02-28 11:40:12.597542

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '3194ab158fd5'
down_revision: Union[str, None] = '7b6e01d9a02c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    
    op.add_column('features', sa.Column('display_for_free', sa.<PERSON>(), nullable=True))
    op.add_column('features', sa.Column('free_list_decorator', sa.String(length=255), nullable=True))
    op.drop_column('features', 'is_free')
    op.add_column('product_features', sa.Column('list_decoration', sa.String(length=255), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('product_features', 'list_decoration')
    op.add_column('features', sa.Column('is_free', mysql.TINYINT(display_width=1), autoincrement=False, nullable=True))
    op.drop_column('features', 'free_list_decorator')
    op.drop_column('features', 'display_for_free')
  
    # ### end Alembic commands ###
