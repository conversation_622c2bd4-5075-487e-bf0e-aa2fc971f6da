"""create simulation table

Revision ID: c9f71141f546
Revises: 17554c43105f
Create Date: 2024-07-30 08:46:06.932450

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c9f71141f546'
down_revision: Union[str, None] = '17554c43105f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('simulation',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(length=255), nullable=True),
    sa.Column('type', sa.Enum('GRID_CONNECTED', 'TRACKING', 'DAILY', 'HOURLY', 'MONTHLY', 'OFFGRID', 'TMY', name='simulationtypeenum'), nullable=True),
    sa.Column('location_type', sa.Enum('ADDRESS', 'GPS_POINT', 'GEOLOCATED_GPS_POINT', name='locationtypeenum'), nullable=True),
    sa.Column('latitude', sa.Numeric(precision=8, scale=6), nullable=False),
    sa.Column('longitude', sa.Numeric(precision=8, scale=6), nullable=False),
    sa.Column('street_number', sa.String(length=255), nullable=True),
    sa.Column('street', sa.String(length=255), nullable=True),
    sa.Column('city', sa.String(length=255), nullable=True),
    sa.Column('country', sa.String(length=255), nullable=True),
    sa.Column('subscription_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(), nullable=True),
    sa.Column('last_user_to_interact', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['subscription_id'], ['subscription.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_simulation_last_user_to_interact'), 'simulation', ['last_user_to_interact'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_simulation_last_user_to_interact'), table_name='simulation')
    op.drop_table('simulation')
    # ### end Alembic commands ###
