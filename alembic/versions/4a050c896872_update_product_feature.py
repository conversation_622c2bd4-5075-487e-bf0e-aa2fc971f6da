"""update product feature

Revision ID: 4a050c896872
Revises: f2c2b09d66ea
Create Date: 2025-06-03 11:19:34.360452

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '4a050c896872'
down_revision: Union[str, None] = 'f2c2b09d66ea'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    conn = op.get_bind()
    conn.execute(sa.text("""
        UPDATE product_features
        SET allow = 0
        WHERE product_id=11 AND features_id=4
    """))
    conn.execute(sa.text("""
        UPDATE product_features
        SET allow = 0
        WHERE product_id=20 AND features_id=4
    """))


def downgrade() -> None:
    pass
