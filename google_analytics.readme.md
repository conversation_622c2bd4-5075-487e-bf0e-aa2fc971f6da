In the [console.cloud.google.com](https://console.cloud.google.com/) 
# Step 1: Create a New Project if you don't already have one:
  - Click the project dropdown at the top of the page and select New Project.
  - Name your project and select the organization, then click Create.
# Step 2: Enable Google Analytics API and Google Analytics Data API:
  - In the project dashboard, click on APIs & Services > Library.
  - Search for Google Analytics API and Google Analytics Data API and click on it.
  - Click the Enable button to activate the API for your project

# Step 3: Set Up a Google Service Account
In the [console.cloud.google.com](https://console.cloud.google.com/) 
1. Create a Service Account in Google Cloud Console
  - Go to the Google Cloud Console.
  - Create a New Project or select an existing project.
  - Go to IAM & Admin > Service Accounts.
  - Click Create Service Account:
  - Give the service account a name and description.
      - Click Create.
      - In the next step, assign a role. Choose Viewer or Editor, or search for Analytics to select the appropriate permissions (e.g., Analytics Viewer or Analytics Editor).
  - Create and Download a JSON Key:
      - After setting the role, click Done.
      - Click on the newly created service account, and under the Keys tab, click Add Key > Create New Key.
      - Choose JSON format and download the file. Keep this JSON file secure, as it contains sensitive information like your service account credentials.

https://analytics.google.com/analytics/web/provision/#/provision
2. Add the Service Account to Google Analytics
  - Go to Google Analytics.
  - In the Admin section, under the Account or Property you wish to use:
      - In the Account or Property column, click Account Settings or Property Settings.
      - Scroll down to User Management and click on it.
      - Add the Service Account's Email (found in the downloaded JSON file) to the list of users.
      - Assign the role Viewer or Editor to give access to the Analytics data.
      - Save the changes.
