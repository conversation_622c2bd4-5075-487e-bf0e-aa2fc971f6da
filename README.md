# PVGIS API

This repository contains the Docker setup for running the PVGIS API application. The application uses FastAPI and requires a database migration tool, Alembic, to manage database schema changes.

## Prerequisites

Make sure you have the following installed on your system:

- Docker
- Docker Compose

## Getting Started

1. Clone the Repository

```bash
    git clone https://github.com/wimse-projects/pvgis-api-client
    cd pvgis-api-client

    git checkout pre-develop
```

2. Set Up Environment Variables

   Create a .env file in the root directory of your project and add the necessary environment variables. Refer to .env.example for the required variables.

3. Select the Appropriate Docker Compose File

   The project includes multiple Docker Compose files for different environments or configurations. Choose the appropriate file and copy it to docker-compose.yml.

   For example:

   ```bash
    cp docker-compose.example.yml docker-compose.yml
   ```

4. Build and Run the Docker Containers

   ```bash
    docker-compose up --build
   ```

   This command will:

   - Build the Docker image using the Dockerfile located at ./docker/Dockerfile.
   - Start the container and expose the application on port 61000.
   - Run database migrations using Alembic.
   - Start the FastAPI application using uvicorn.

5. Migrations

   As during local development your app directory is mounted as a volume inside the container, you can also run the migrations with alembic commands inside the container and the migration code will be in your app directory (instead of being only inside the container). So you can add it to your git repository.

   Make sure you create a "revision" of your models and that you "upgrade" your database with that revision every time you change them. As this is what will update the tables in your database. Otherwise, your application will have errors.

   Start an interactive session in the pvgis-api container:

   ```bash
   docker exec -it pvgis-api bash
   ```

   If you created a new model in `./pvgis-api-client/app/models/`, make sure to import it in `./pvgis-api-client/app/db/base.py`, that Python module (base.py) that imports all the models will be used by Alembic.

   After changing a model (for example, adding a column), inside the container, create a revision, e.g.:

   ```bash
   alembic revision --autogenerate -m "Add column last_name to User model"
   ```

   Empty migration

   ```bash
   alembic revision -m "Add column last_name to User model"
   ```

   Commit to the git repository the files generated in the alembic directory.

   After creating the revision, run the migration in the database (this is what will actually change the database):

   ```bash
   alembic upgrade head
   ```
