import pandas as pd
import requests
import time

# Load the CSV
file_path = "400.villes.Europe.csv"  # Replace with your file path
data = pd.read_csv(file_path, sep=';', header=None, names=["cityName", "countryName"])


# Google Maps Geocoding API key
api_key = "AIzaSyBWDrBbwE1MaX-nUdzWm8vIYKynqa0XCgo"  

# Function to get latitude, longitude, and ISO2 code
def get_lat_lng_iso2(city, country):
    url = f"https://maps.googleapis.com/maps/api/geocode/json"
    params = {
        "address": f"{city}, {country}",
        "key": api_key
    }
    response = requests.get(url, params=params)
    if response.status_code == 200:
        results = response.json().get("results")
        if results:
            location = results[0]["geometry"]["location"]
            lat = location["lat"]
            lng = location["lng"]
            address_components = results[0].get("address_components", [])
            # Find ISO2 country code
            country_iso2 = next(
                (comp["short_name"] for comp in address_components if "country" in comp["types"]),
                None
            )
            return lat, lng, country_iso2
    return None, None, None

# Add new columns
latitudes = []
longitudes = []
country_iso2_codes = []
i = 0
for index, row in data.iterrows():
    city = row["cityName"]
    country = row["countryName"]
    lat, lng, iso2 = get_lat_lng_iso2(city, country)
    latitudes.append(lat)
    longitudes.append(lng)
    country_iso2_codes.append(iso2)
    print(f"Processed: {city}, {country} -> Lat: {lat}, Lng: {lng}, ISO2: {iso2}, row #{i}")
    time.sleep(0.1)  # To avoid hitting API rate limits
    i+=1

data["lat"] = latitudes
data["lng"] = longitudes
data["countryIso2"] = country_iso2_codes

# Save to a new CSV
output_file = "cities_with_lat_lng_iso2.csv"
data.to_csv(output_file, index=False)
print(f"File saved: {output_file}")
