from datetime import datetime
import pymysql
import json

# Database connection parameters
db_host = 'localhost'
db_user = 'root'
db_password = 'password'
db_name = 'dev_pvgis_api'
db_port = '3306'

def load_timezone_data(json_file_path):
    """Load timezone data from JSON file"""
    try:
        with open(json_file_path, 'r') as file:
            data = json.load(file)
            return data.get('zones', [])
    except Exception as e:
        print(f"Error loading JSON file: {str(e)}")
        return []

def format_offset(gmt_offset):
    """Convert GMT offset in seconds to +/-HH:MM format"""
    try:
        total_seconds = int(gmt_offset)
        sign = '+' if total_seconds >= 0 else '-'
        total_seconds = abs(total_seconds)
        
        hours = total_seconds // 3600
        minutes = (total_seconds % 3600) // 60
        
        return f"{sign}{hours:02d}:{minutes:02d}"
    except Exception as e:
        print(f"Error formatting offset: {str(e)}")
        return None

def execute_script():
    conn = pymysql.connect(host=db_host, user=db_user, password=db_password, db=db_name, port=db_port)
    cursor = conn.cursor()

    timezone_data = load_timezone_data('timezones.json')
    
    if not timezone_data:
        print("No timezone data found. Exiting.")
        return

    country_timezone_map = {}
    for zone in timezone_data:
        country_code = zone.get('countryCode')
        timezone_name = zone.get('zoneName')
        gmt_offset = zone.get('gmtOffset')
        
        if country_code and timezone_name and gmt_offset is not None:
            formatted_offset = format_offset(gmt_offset)
            if formatted_offset:
                country_timezone_map[country_code] = {
                    'name': timezone_name,
                    'offset': formatted_offset
                }
    
    cursor.execute("SELECT code_alpha_2 FROM country")
    country_codes = cursor.fetchall()

    updated_count = 0
    for country_code_tuple in country_codes:
        code = country_code_tuple[0]  # Get the code_alpha_2 (e.g., 'US', 'IN', 'GB')
        
        if code in country_timezone_map:
            timezone_info = country_timezone_map[code]
            timezone_name = timezone_info['name']
            timezone_offset = timezone_info['offset']
            
            update_query = """
                UPDATE country 
                SET timezone_name = %s, timezone_offset = %s 
                WHERE code_alpha_2 = %s
            """
            cursor.execute(update_query, (timezone_name, timezone_offset, code))
            print(f"Updated timezone for {code}: {timezone_name} ({timezone_offset})")
            updated_count += 1
        else:
            print(f"No timezone data found for country code: {code}")

    manual_data = [
        ('XK', 'Europe/Belgrade', '+01:00'),
        ('BV', 'Indian/Kerguelen', '+05:00'),
        ('HM', 'Australia/Hobart', '+11:00'),
        ('EH', 'Africa/El_Aaiun', '+01:00'),
    ]
    
    for country_code, timezone_name, timezone_offset in manual_data:
        update_query = """
            UPDATE country 
            SET timezone_name = %s, timezone_offset = %s 
            WHERE code_alpha_2 = %s
        """
        cursor.execute(update_query, (timezone_name, timezone_offset, country_code))
        print(f"Updated timezone for {country_code}: {timezone_name} ({timezone_offset})")
        updated_count += 1

    conn.commit()
    cursor.close()
    conn.close()

    print(f"Timezone data has been updated. Total updates: {updated_count}")

if __name__ == "__main__":
    execute_script()