import json
import pymysql

# Database connection
connection = pymysql.connect(
    host='************',
    user='dev_pvgis_api_user',
    password='1!YRVKIcn53mpsU[',
    database='dev_pvgis_api'
)

cursor = connection.cursor()

# Query simulation_listing data
cursor.execute("""
    SELECT id, simulation_image
    FROM simulation_listing 
""")
rows = cursor.fetchall()

for simulation_listing_id, simulation_image in rows:
    try:
        # Parse the current simulation_image
        simulation_image_data = json.loads(simulation_image)

        # Remove the 'base_url' property if it exists
        if 'base_url' in simulation_image_data:
            del simulation_image_data['base_url']
        simulation_image_data = {
            "map": simulation_image_data
        }
        # Update the simulation_image column with the new format
        cursor.execute(
            "UPDATE simulation_listing SET simulation_image = %s WHERE id = %s",
            (json.dumps(simulation_image_data), simulation_listing_id)
        )
        connection.commit()

        print(f"Updated simulation_image for simulation_listing_id {simulation_listing_id}")

    except json.JSONDecodeError as e:
        print(f"Error parsing simulation_image for simulation_listing_id {simulation_listing_id}: {e}")
        continue

# Close the database connection
cursor.close()
connection.close()
