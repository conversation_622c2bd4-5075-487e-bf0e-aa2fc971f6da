import json
import pymysql
import requests

# Database connection
DB_HOST = 'localhost'
DB_USER = 'root'
DB_PASSWORD = 'password'
DB_NAME = 'pvgis_db'

connection = pymysql.connect(
    host=DB_HOST,
    user=DB_USER,
    password=DB_PASSWORD,
    database=DB_NAME
)


HORIZON_URL =  "https://dev.api.pvgis.com/api/v5_3/horizon_profile"
cursor = connection.cursor()




def fetch_horizon(lat, lon):
    """Fetch the simulation horizon data"""
    params = {
        "lat": lat,
        "lon": lon,
        "browser": 0,
        "outputformat": "basic"
    }
    try:
        response = requests.post(HORIZON_URL,  
            headers={"Content-Type": "application/json"},
            json=params)
        response.raise_for_status()
        return { "res":response.json(), "input": params }
    except Exception as e:
        print(f"Error fetching simulation for lat={lat}, lon={lon}: {e}")
        return None




# Query simulation_listing data
cursor.execute("""
    SELECT id, simulation_input
    FROM simulation_listing 
     ORDER BY id
    LIMIT 1
""")
rows = cursor.fetchall()

for simulation_listing_id, simulation_input in rows:
    try:
        # Parse the current simulation_image
        simulation_input_data = json.loads(simulation_input)

        # Fetch horizon data
        horizon_data = fetch_horizon(simulation_input_data['lat'], simulation_input_data['lon'])

       
        # Update the simulation_image column with the new format
        cursor.execute(
            "UPDATE simulation_listing SET horizon_data = %s WHERE id = %s",
            (horizon_data['res'], simulation_listing_id)
        )
        connection.commit()

        print(f"Updated simulation_image for simulation_listing_id {simulation_listing_id}")

    except json.JSONDecodeError as e:
        print(f"Error parsing simulation_image for simulation_listing_id {simulation_listing_id}: {e}")
        continue

# Close the database connection
cursor.close()
connection.close()




