import requests
import mysql.connector
import json

# MySQL database connection setup
connection = mysql.connector.connect(
    host="localhost",  # Update with your host
    user="root",       # Update with your username
    password="password",       # Update with your password
    database="pvgis_db"  # Update with your database name
)
cursor = connection.cursor()

# Constants
EXTENT_URL = "https://dev.api.pvgis.com/api/v5_3/extent"
SIMULATION_URL = "https://dev.api.pvgis.com/api/v5_3/pvgis_grid_connected_or_tracking"

def get_database(lat, lon):
    """Fetch the appropriate database for given lat and lon."""
    database = ["PVGIS-SARAH3", "PVGIS-ERA5"]
    try:
        response = requests.post(
            EXTENT_URL,
            headers={"Content-Type": "application/json"},
            json={"lat": lat, "lon": lon, "database": ",".join(database)}
        )
        response.raise_for_status()
        val = response.json()
        databases = [db for db, available in zip(database, val) if available]
        return databases[0] if databases else None
    except Exception as e:
        print(f"Error fetching database for lat={lat}, lon={lon}: {e}")
        return None

def fetch_simulation(lat, lon, database):
    """Fetch the simulation data for a given city."""
    params = {
        "lat": lat,
        "lon": lon,
        "usehorizon": 1,
        "raddatabase": database,
        "peakpower": 1,
        "pvtechchoice": "crystSi",
        "mountingplace": "free",
        "loss": 3,
        "optimalinclination": 1,
        "optimalangles": 1,
        "angle": 0,
        "aspect": 0,
        "inclined_axis": 0,
        "vertical_axis": 0,
        "twoaxis": 0,
        "outputformat": "json",
        "browser": 0
    }
    try:
        response = requests.post(SIMULATION_URL,  
            headers={"Content-Type": "application/json"},
            json=params)
        response.raise_for_status()
        return { "res":response.json(), "input": params }
    except Exception as e:
        print(f"Error fetching simulation for lat={lat}, lon={lon}: {e}")
        return None

# Main processing loop
success_count = 0
error_count = 0

# Fetch city data from the database
cursor.execute("SELECT id, name, latitude, longitude FROM city where id > 80")
cities = cursor.fetchall()

for city_id, city_name, lat, lon in cities:
    print(f"Processing city: {city_name} (ID: {city_id}, Lat: {lat}, Lon: {lon})")

    database = get_database(lat, lon)
    if not database:
        print(f"Error: No valid database found for city {city_name}")
        error_count += 1
        continue

    simulation = fetch_simulation(lat, lon, database)
    if not simulation:
        print(f"Error: Failed to fetch simulation data for city {city_name}")
        error_count += 1
        continue

    # Insert data into the database
    try:
        cursor.execute(
            "INSERT INTO simulation_listing (city_id, simulation_data, simulation_input) VALUES (%s, %s, %s)",
            (city_id, json.dumps(simulation["res"]), json.dumps(simulation["input"]))
        )
        connection.commit()
        print(f"Success: Simulation data stored for city {city_name}")
        success_count += 1
    except Exception as e:
        print(f"Error inserting data for city {city_name}: {e}")
        error_count += 1

# Final report
print(f"Processing complete. Success: {success_count}, Errors: {error_count}")

# Close database connection
cursor.close()
connection.close()
