from datetime import datetime
import pymysql
import requests
import pytz
 

# Database connection parameters
db_host = 'localhost'
db_user = 'root'
db_password = 'password'
db_name = 'pvgis_db' 


# Function to get timezone offset for a given country code
def get_timezone_offset(country_code):
    try:
        timezones = pytz.country_timezones(country_code)
        if len(timezones) == 0: 
            raise Exception('No timezone for country '+country_code)
        tz = pytz.timezone(timezones[0])
        local_time = datetime.now(tz)
        offset = local_time.strftime('%z')
        return offset[:3] + ':' + offset[3:]
    except Exception as e:
        print(f"Error fetching timezone offset for {country_code}: {str(e)}")
        return None
    
def execute_script():
    # Connect to the MySQL database
    conn = pymysql.connect(host=db_host, user=db_user, password=db_password, db=db_name)
    cursor = conn.cursor()

    # Get all the country codes from the country table
    cursor.execute("SELECT code_alpha_2 FROM country")
    country_codes = cursor.fetchall()

    # Iterate over the country codes and update the timezone_offset
    for country_code in country_codes:
        code = country_code[0]  # Get the code_alpha_2 (e.g., 'US', 'IN', 'GB')
        
        # Fetch timezone offset using the API
        timezone_offset = get_timezone_offset(code)
        
        if timezone_offset is not None:
            # Update the timezone_offset in the database
            update_query = "UPDATE country SET timezone_offset = %s WHERE code_alpha_2 = %s"
            cursor.execute(update_query, (timezone_offset, code))
            print(f"Updated timezone_offset for {code}: {timezone_offset}")
        

    # manual cases:
    manual_data = [
        ('XK', '+01:00'),
        ('BV', '+01:00'),
        ('HM', '+05:00'),
    ]
    for country_code, offset in manual_data:
        update_query = "UPDATE country SET timezone_offset = %s WHERE code_alpha_2 = %s"
        cursor.execute(update_query, (offset, country_code))
        print(f"Updated timezone_offset for {country_code}: {offset}")

    # Commit the changes and close the connection
    conn.commit()
    cursor.close()
    conn.close()

    print("Timezone offsets have been updated.")
    
    
execute_script()