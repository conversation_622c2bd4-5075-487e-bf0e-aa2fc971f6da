import mysql.connector
import requests

# Database connection
DB_HOST = 'localhost'
DB_USER = 'root'
DB_PASSWORD = 'password'
DB_NAME = 'pvgis_db'

# Google Geocoding API key
API_KEY = 'AIzaSyBWDrBbwE1MaX-nUdzWm8vIYKynqa0XCgo'
GEOCODE_URL = 'https://maps.googleapis.com/maps/api/geocode/json'

def get_google_place_id(city_name, country_name):
    """
    Fetch Google Place ID for a given city and country using Google Geocoding API.
    """
    params = {
        'address': f"{city_name}, {country_name}",
        'key': API_KEY
    }
    response = requests.get(GEOCODE_URL, params=params)
    if response.status_code == 200:
        data = response.json()
        if data['status'] == 'OK':
            for result in data['results']: 
                if (
                    'locality' in result['types']
                    or 'administrative_area_level_4' in result['types']
                    or 'administrative_area_level_3' in result['types']
                    or 'administrative_area_level_2' in result['types']
                    or 'political' in result['types']
                    ): 
                    return result['place_id']  
            
    return None

def populate_google_place_ids():
    """
    Populate the google_place_id column in the city table using the Google Geocoding API.
    """
    try:
        # Connect to the database
        connection = mysql.connector.connect(
            host=DB_HOST,
            user=DB_USER,
            password=DB_PASSWORD,
            database=DB_NAME
        )

        cursor = connection.cursor(dictionary=True)

        # Query to fetch cities and their countries
        cursor.execute("""
            SELECT city.id AS city_id, city.name AS city_name, country.name AS country_name
            FROM city
            INNER JOIN country ON city.country_id = country.id
            WHERE city.google_place_id IS NULL;
        """)
        cities = cursor.fetchall()

        for city in cities:
            city_id = city['city_id']
            city_name = city['city_name']
            country_name = city['country_name']

            # Fetch the Google Place ID
            place_id = get_google_place_id(city_name, country_name)

            if place_id:
                # Update the database with the Google Place ID
                cursor.execute("""
                    UPDATE city
                    SET google_place_id = %s 
                    WHERE id = %s;
                """, (place_id, city_id))
                connection.commit()
                print(f"Updated {city_name}, {country_name} with Place ID: {place_id}")
            else:
                print(f"Could not fetch Place ID for {city_name}, {country_name}")

    except mysql.connector.Error as err:
        print(f"Database error: {err}")
    except Exception as e:
        print(f"Error: {e}")
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

if __name__ == '__main__':
    populate_google_place_ids()
