import requests
import json
import pymysql
 



# Database connection
connection = pymysql.connect(
    host='************',
    user='dev_pvgis_api_user',
    password='1!YRVKIcn53mpsU[',
    database='dev_pvgis_api'
)

cursor = connection.cursor()

# Google Maps API parameters
google_maps_api_key = "AIzaSyBWDrBbwE1MaX-nUdzWm8vIYKynqa0XCgo"
zoom = 19
size = "1200x300"
map_type_id = "satellite"

# Query simulation_listing and city data
cursor.execute("""
    SELECT sl.id, c.latitude, c.longitude
    FROM simulation_listing sl
    INNER JOIN city c ON sl.city_id = c.id
""")
rows = cursor.fetchall()

for simulation_listing_id, lat, lng in rows:
    # Get the map image from Google Maps Static API
    google_maps_url = f"https://maps.googleapis.com/maps/api/staticmap?center={lat},{lng}&zoom={zoom}&size={size}&maptype={map_type_id}&markers=color:red%7Clabel:+%7C{lat},{lng}&key={google_maps_api_key}"
    response = requests.get(google_maps_url)

    if response.status_code != 200:
        print(f"Failed to fetch map image for simulation_listing_id {simulation_listing_id}")
        continue

    # Send the image to the document service
    files = {
        'files': ('map_image.png', response.content, 'image/png')
    }
    form_data = {
        'hook': 'solar-panel-system-calculator',
        'registeredResource[resourceName]': 'simulation-listing',
        'registeredResource[resourceId]': str(simulation_listing_id),
        'registeredResource[type]': 'string',
        'documentInfo[visible]': 'true',
        'documentInfo[description]': 'simulation image',
        'documentInfo[maxFile]': '1',
        'documentInfo[isMain]': 'true',
    }

    document_service_url = "https://dev.fs.pvgis.com/document"
    doc_response = requests.post(document_service_url, files=files, data=form_data)

    if doc_response.status_code != 200:
        print(f"Failed to upload document for simulation_listing_id {simulation_listing_id}")
        continue

    # Parse the response from the document service
    try:
        doc_response_json = doc_response.json()
        document_info = doc_response_json.get('documentInfo', {})
        files_info = document_info.get('file', [])
        if not files_info:
            print(f"No file information returned for simulation_listing_id {simulation_listing_id}")
            continue

        # Create the JSON object to save in the database
        simulation_image_data = { 
            **files_info[0]
        }

        # Update the simulation_image column
        cursor.execute(
            "UPDATE simulation_listing SET simulation_image = %s WHERE id = %s",
            (json.dumps(simulation_image_data), simulation_listing_id)
        )
        connection.commit()

        print(f"Updated simulation_image for simulation_listing_id {simulation_listing_id}")

    except json.JSONDecodeError as e:
        print(f"Error parsing JSON response for simulation_listing_id {simulation_listing_id}: {e}")
        continue

# Close the database connection
cursor.close()
connection.close()
