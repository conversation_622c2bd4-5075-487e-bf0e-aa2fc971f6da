import csv
import pymysql

def insert_cities(csv_file_path, db_config):
    # Connect to the MySQL database
    connection = pymysql.connect(
        host=db_config['host'],
        user=db_config['user'],
        password=db_config['password'],
        database=db_config['database']
    )

    try:
        with connection.cursor() as cursor:
            # Read the CSV file
            with open(csv_file_path, mode='r', encoding='utf-8') as csv_file:
                csv_reader = csv.DictReader(csv_file)

                for row in csv_reader:
                    city_name = row['cityName']
                    latitude = row['lat']
                    longitude = row['lng']
                    country_iso2 = row['countryIso2'].strip().lower()

                    # Find the country_id based on the country code
                    cursor.execute(
                        """
                        SELECT id FROM country WHERE LOWER(TRIM(code_alpha_2)) = %s
                        """,
                        (country_iso2,)
                    )
                    result = cursor.fetchone()

                    if result:
                        country_id = result[0]

                        # Insert the city into the city table
                        cursor.execute(
                            """
                            INSERT INTO city (name, latitude, longitude, country_id)
                            VALUES (%s, %s, %s, %s)
                            """,
                            (city_name, latitude, longitude, country_id)
                        )
                    else:
                        print(f"Country with ISO2 code '{country_iso2}' not found. Skipping city '{city_name}'.")

            # Commit the transaction
            connection.commit()

    except Exception as e:
        print(f"An error occurred: {e}")
        connection.rollback()

    finally:
        connection.close()

# Example usage
if __name__ == "__main__":
    csv_file_path = "cities_with_lat_lng_iso2.csv"  # Replace with the path to your CSV file
    db_config = {
        "host": "localhost",  # Replace with your MySQL host
        "user": "root",       # Replace with your MySQL username
        "password": "password",       # Replace with your MySQL password
        "database": "pvgis_db"  # Replace with your MySQL database name
    }

    insert_cities(csv_file_path, db_config)
