version: "3.3"
services:
  pvgis-api:
    env_file:
      - .env.pre-development
    container_name: pre-dev-pvgis-api
    build:
      context: ./
      dockerfile: ./Dockerfile
    ports:
      - 61001:3000
    network_mode: bridge
    volumes:
      - ./app:/var/www/pvgis-api/app
      - ./alembic:/var/www/pvgis-api/alembic
      - ./city-management:/var/www/pvgis-api/city-management
      - ./.env:/var/www/pvgis-api/.env
      - ./files:/app/files
      - ./files:/var/www/pvgis-api/files
    command:
      [
        "sh",
        "-c",
        "./pre_start.sh && uvicorn app.main:app --host 0.0.0.0 --port 3000 --reload",
      ]
    # command: ["bash", "./run.sh"]
